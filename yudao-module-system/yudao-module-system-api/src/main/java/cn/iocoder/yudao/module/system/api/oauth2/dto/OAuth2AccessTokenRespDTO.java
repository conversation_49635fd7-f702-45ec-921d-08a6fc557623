package cn.iocoder.yudao.module.system.api.oauth2.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * OAuth2.0 訪問令牌的信息 Response DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OAuth2AccessTokenRespDTO implements Serializable {

    /**
     * 訪問令牌
     */
    private String accessToken;
    /**
     * 刷新令牌
     */
    private String refreshToken;
    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 用戶類型
     */
    private Integer userType;
    /**
     * 過期時間
     */
    private LocalDateTime expiresTime;

}
