package cn.iocoder.yudao.module.system.api.dict;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;

import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 字典數據 API 接口
 *
 * <AUTHOR>
 */
public interface DictDataApi {

    /**
     * 校驗字典數據們是否有效。如下情況，視爲無效：
     * 1. 字典數據不存在
     * 2. 字典數據被禁用
     *
     * @param dictType 字典類型
     * @param values   字典數據值的數組
     */
    void validateDictDataList(String dictType, Collection<String> values);

    /**
     * 獲得指定的字典數據，從緩存中
     *
     * @param type  字典類型
     * @param value 字典數據值
     * @return 字典數據
     */
    DictDataRespDTO getDictData(String type, String value);

    /**
     * 獲得指定的字典標籤，從緩存中
     *
     * @param type  字典類型
     * @param value 字典數據值
     * @return 字典標籤
     */
    default String getDictDataLabel(String type, Integer value) {
        DictDataRespDTO dictData = getDictData(type, String.valueOf(value));
        if (ObjUtil.isNull(dictData)) {
            return StrUtil.EMPTY;
        }
        return dictData.getLabel();
    }

    /**
     * 解析獲得指定的字典數據，從緩存中
     *
     * @param type  字典類型
     * @param label 字典數據標籤
     * @return 字典數據
     */
    DictDataRespDTO parseDictData(String type, String label);

    /**
     * 獲得指定字典類型的字典數據列表
     *
     * @param dictType 字典類型
     * @return 字典數據列表
     */
    List<DictDataRespDTO> getDictDataList(String dictType);

    /**
     * 獲得字典數據標籤列表
     *
     * @param dictType 字典類型
     * @return 字典數據標籤列表
     */
    default List<String> getDictDataLabelList(String dictType) {
        List<DictDataRespDTO> list = getDictDataList(dictType);
        return convertList(list, DictDataRespDTO::getLabel);
    }

}
