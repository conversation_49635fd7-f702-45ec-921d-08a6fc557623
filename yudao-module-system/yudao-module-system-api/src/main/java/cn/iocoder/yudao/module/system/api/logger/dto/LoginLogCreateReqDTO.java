package cn.iocoder.yudao.module.system.api.logger.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 登錄日誌創建 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class LoginLogCreateReqDTO {

    /**
     * 日誌類型
     */
    @NotNull(message = "日誌類型不能爲空")
    private Integer logType;
    /**
     * 鏈路追蹤編號
     */
    private String traceId;

    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 用戶類型
     */
    @NotNull(message = "用戶類型不能爲空")
    private Integer userType;
    /**
     * 用戶賬號
     *
     * 不再強制校驗 username 非空，因爲 Member 社交登錄時，此時暫時沒有 username(mobile）！
     */
    private String username;

    /**
     * 登錄結果
     */
    @NotNull(message = "登錄結果不能爲空")
    private Integer result;

    /**
     * 用戶 IP
     */
    @NotEmpty(message = "用戶 IP 不能爲空")
    private String userIp;
    /**
     * 瀏覽器 UserAgent
     *
     * 允許空，原因：Job 過期登出時，是無法傳遞 UserAgent 的
     */
    private String userAgent;

}
