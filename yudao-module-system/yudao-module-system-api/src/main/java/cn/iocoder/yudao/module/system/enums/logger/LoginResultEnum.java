package cn.iocoder.yudao.module.system.enums.logger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登錄結果的枚舉類
 */
@Getter
@AllArgsConstructor
public enum LoginResultEnum {

    SUCCESS(0), // 成功
    BAD_CREDENTIALS(10), // 賬號或密碼不正確
    USER_DISABLED(20), // 用戶被禁用
    CAPTCHA_NOT_FOUND(30), // 圖片驗證碼不存在
    CAPTCHA_CODE_ERROR(31), // 圖片驗證碼不正確

    ;

    /**
     * 結果
     */
    private final Integer result;

}
