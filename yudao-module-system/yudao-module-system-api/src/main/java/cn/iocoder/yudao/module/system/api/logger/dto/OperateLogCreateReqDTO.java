package cn.iocoder.yudao.module.system.api.logger.dto;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 系統操作日誌 Create Request DTO
 *
 * <AUTHOR>
 */
@Data
public class OperateLogCreateReqDTO {

    /**
     * 鏈路追蹤編號
     *
     * 一般來說，通過鏈路追蹤編號，可以將訪問日誌，錯誤日誌，鏈路追蹤日誌，logger 打印日誌等，結合在一起，從而進行排錯。
     */
    private String traceId;
    /**
     * 用戶編號
     *
     * 關聯 MemberUserDO 的 id 屬性，或者 AdminUserDO 的 id 屬性
     */
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;
    /**
     * 用戶類型
     *
     * 關聯 {@link  UserTypeEnum}
     */
    @NotNull(message = "用戶類型不能爲空")
    private Integer userType;
    /**
     * 操作模塊類型
     */
    @NotEmpty(message = "操作模塊類型不能爲空")
    private String type;
    /**
     * 操作名
     */
    @NotEmpty(message = "操作名不能爲空")
    private String subType;
    /**
     * 操作模塊業務編號
     */
    @NotNull(message = "操作模塊業務編號不能爲空")
    private Long bizId;
    /**
     * 操作內容，記錄整個操作的明細
     * 例如說，修改編號爲 1 的用戶信息，將性別從男改成女，將姓名從芋道改成源碼。
     */
    @NotEmpty(message = "操作內容不能爲空")
    private String action;
    /**
     * 拓展字段，有些複雜的業務，需要記錄一些字段 ( JSON 格式 )
     * 例如說，記錄訂單編號，{ orderId: "1"}
     */
    private String extra;

    /**
     * 請求方法名
     */
    @NotEmpty(message = "請求方法名不能爲空")
    private String requestMethod;
    /**
     * 請求地址
     */
    @NotEmpty(message = "請求地址不能爲空")
    private String requestUrl;
    /**
     * 用戶 IP
     */
    @NotEmpty(message = "用戶 IP 不能爲空")
    private String userIp;
    /**
     * 瀏覽器 UA
     */
    @NotEmpty(message = "瀏覽器 UA 不能爲空")
    private String userAgent;

}
