package cn.iocoder.yudao.module.system.enums.social;

import cn.hutool.core.util.ArrayUtil;
import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 社交平臺的類型枚舉
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SocialTypeEnum implements ArrayValuable<Integer> {

    /**
     * Gitee
     *
     * @see <a href="https://gitee.com/api/v5/oauth_doc#/">接入文檔</a>
     */
    GITEE(10, "GITEE"),
    /**
     * 釘釘
     *
     * @see <a href="https://developers.dingtalk.com/document/app/obtain-identity-credentials">接入文檔</a>
     */
    DINGTALK(20, "DINGTALK"),

    /**
     * 企業微信
     *
     * @see <a href="https://xkcoding.com/2019/08/06/use-justauth-integration-wechat-enterprise.html">接入文檔</a>
     */
    WECHAT_ENTERPRISE(30, "WECHAT_ENTERPRISE"),
    /**
     * 微信公衆平臺 - 移動端 H5
     *
     * @see <a href="https://www.cnblogs.com/juewuzhe/p/11905461.html">接入文檔</a>
     */
    WECHAT_MP(31, "WECHAT_MP"),
    /**
     * 微信開放平臺 - 網站應用 PC 端掃碼授權登錄
     *
     * @see <a href="https://justauth.wiki/guide/oauth/wechat_open/#_2-申請開發者資質認證">接入文檔</a>
     */
    WECHAT_OPEN(32, "WECHAT_OPEN"),
    /**
     * 微信小程序
     *
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/login.html">接入文檔</a>
     */
    WECHAT_MINI_APP(34, "WECHAT_MINI_APP"),
    ;

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(SocialTypeEnum::getType).toArray(Integer[]::new);

    /**
     * 類型
     */
    private final Integer type;
    /**
     * 類型的標識
     */
    private final String source;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static SocialTypeEnum valueOfType(Integer type) {
        return ArrayUtil.firstMatch(o -> o.getType().equals(type), values());
    }

}
