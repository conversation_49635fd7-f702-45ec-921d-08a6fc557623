package cn.iocoder.yudao.module.system.api.permission.dto;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * 部門的數據權限 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class DeptDataPermissionRespDTO {

    /**
     * 是否可查看全部數據
     */
    private Boolean all;
    /**
     * 是否可查看自己的數據
     */
    private Boolean self;
    /**
     * 可查看的部門編號數組
     */
    private Set<Long> deptIds;

    public DeptDataPermissionRespDTO() {
        this.all = false;
        this.self = false;
        this.deptIds = new HashSet<>();
    }

}
