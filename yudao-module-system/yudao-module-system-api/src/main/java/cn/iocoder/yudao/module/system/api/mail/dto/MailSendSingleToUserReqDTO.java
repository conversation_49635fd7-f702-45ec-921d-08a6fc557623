package cn.iocoder.yudao.module.system.api.mail.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 郵件發送 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class MailSendSingleToUserReqDTO {

    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 郵箱
     */
    @Email
    private String mail;

    /**
     * 郵件模板編號
     */
    @NotNull(message = "郵件模板編號不能爲空")
    private String templateCode;
    /**
     * 郵件模板參數
     */
    private Map<String, Object> templateParams;

}
