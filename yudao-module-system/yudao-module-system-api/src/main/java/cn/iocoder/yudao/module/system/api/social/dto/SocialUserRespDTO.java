package cn.iocoder.yudao.module.system.api.social.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 社交用戶 Response DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocialUserRespDTO {

    /**
     * 社交用戶的 openid
     */
    private String openid;
    /**
     * 社交用戶的暱稱
     */
    private String nickname;
    /**
     * 社交用戶的頭像
     */
    private String avatar;

    /**
     * 關聯的用戶編號
     */
    private Long userId;

}
