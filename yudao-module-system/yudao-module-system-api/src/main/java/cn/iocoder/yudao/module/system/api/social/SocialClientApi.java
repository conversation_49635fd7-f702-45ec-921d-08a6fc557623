package cn.iocoder.yudao.module.system.api.social;

import cn.iocoder.yudao.module.system.api.social.dto.*;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;

import javax.validation.Valid;

import java.util.List;

/**
 * 社交應用的 API 接口
 *
 * <AUTHOR>
 */
public interface SocialClientApi {

    /**
     * 獲得社交平臺的授權 URL
     *
     * @param socialType  社交平臺的類型 {@link SocialTypeEnum}
     * @param userType    用戶類型
     * @param redirectUri 重定向 URL
     * @return 社交平臺的授權 URL
     */
    String getAuthorizeUrl(Integer socialType, Integer userType, String redirectUri);

    /**
     * 創建微信公衆號 JS SDK 初始化所需的簽名
     *
     * @param userType 用戶類型
     * @param url      訪問的 URL 地址
     * @return 簽名
     */
    SocialWxJsapiSignatureRespDTO createWxMpJsapiSignature(Integer userType, String url);

    //======================= 微信小程序獨有 =======================

    /**
     * 獲得微信小程序的手機信息
     *
     * @param userType  用戶類型
     * @param phoneCode 手機授權碼
     * @return 手機信息
     */
    SocialWxPhoneNumberInfoRespDTO getWxMaPhoneNumberInfo(Integer userType, String phoneCode);

    /**
     * 獲得小程序二維碼
     *
     * @param reqVO 請求信息
     * @return 小程序二維碼
     */
    byte[] getWxaQrcode(@Valid SocialWxQrcodeReqDTO reqVO);

    /**
     * 獲得微信小程訂閱模板
     *
     * @return 小程序訂閱消息模版
     */
    List<SocialWxaSubscribeTemplateRespDTO> getWxaSubscribeTemplateList(Integer userType);

    /**
     * 發送微信小程序訂閱消息
     *
     * @param reqDTO 請求
     */
    void sendWxaSubscribeMessage(SocialWxaSubscribeMessageSendReqDTO reqDTO);

}
