package cn.iocoder.yudao.module.system.enums;

/**
 * System 字典類型的枚舉類
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    String USER_TYPE = "user_type"; // 用戶類型
    String COMMON_STATUS = "common_status"; // 系統狀態

    // ========== SYSTEM 模塊 ==========

    String USER_SEX = "system_user_sex"; // 用戶性別
    String DATA_SCOPE = "system_data_scope"; // 數據範圍

    String LOGIN_TYPE = "system_login_type"; // 登錄日誌的類型
    String LOGIN_RESULT = "system_login_result"; // 登錄結果

    String SMS_CHANNEL_CODE = "system_sms_channel_code"; // 短信渠道編碼
    String SMS_TEMPLATE_TYPE = "system_sms_template_type"; // 短信模板類型
    String SMS_SEND_STATUS = "system_sms_send_status"; // 短信發送狀態
    String SMS_RECEIVE_STATUS = "system_sms_receive_status"; // 短信接收狀態

}
