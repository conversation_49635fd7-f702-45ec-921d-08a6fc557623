package cn.iocoder.yudao.module.system.enums.sms;

import cn.hutool.core.util.ArrayUtil;
import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 用戶短信驗證碼發送場景的枚舉
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SmsSceneEnum implements ArrayValuable<Integer> {

    MEMBER_LOGIN(1, "user-sms-login", "會員用戶 - 手機號登陸"),
    MEMBER_UPDATE_MOBILE(2, "user-update-mobile", "會員用戶 - 修改手機"),
    MEMBER_UPDATE_PASSWORD(3, "user-update-password", "會員用戶 - 修改密碼"),
    MEMBER_RESET_PASSWORD(4, "user-reset-password", "會員用戶 - 忘記密碼"),

    ADMIN_MEMBER_LOGIN(21, "admin-sms-login", "後臺用戶 - 手機號登錄"),
    ADMIN_MEMBER_REGISTER(22, "admin-sms-register", "後臺用戶 - 手機號註冊"),
    ADMIN_MEMBER_RESET_PASSWORD(23, "admin-reset-password", "後臺用戶 - 忘記密碼");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(SmsSceneEnum::getScene).toArray(Integer[]::new);

    /**
     * 驗證場景的編號
     */
    private final Integer scene;
    /**
     * 模版編碼
     */
    private final String templateCode;
    /**
     * 描述
     */
    private final String description;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    public static SmsSceneEnum getCodeByScene(Integer scene) {
        return ArrayUtil.firstMatch(sceneEnum -> sceneEnum.getScene().equals(scene),
                values());
    }

}
