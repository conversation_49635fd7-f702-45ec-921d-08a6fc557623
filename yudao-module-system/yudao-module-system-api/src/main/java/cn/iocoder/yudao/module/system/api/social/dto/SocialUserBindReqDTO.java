package cn.iocoder.yudao.module.system.api.social.dto;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 取消綁定社交用戶 Request DTO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocialUserBindReqDTO {

    /**
     * 用戶編號
     */
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;
    /**
     * 用戶類型
     */
    @InEnum(UserTypeEnum.class)
    @NotNull(message = "用戶類型不能爲空")
    private Integer userType;

    /**
     * 社交平臺的類型
     */
    @InEnum(SocialTypeEnum.class)
    @NotNull(message = "社交平臺的類型不能爲空")
    private Integer socialType;
    /**
     * 授權碼
     */
    @NotEmpty(message = "授權碼不能爲空")
    private String code;
    /**
     * state
     */
    @NotNull(message = "state 不能爲空")
    private String state;

}
