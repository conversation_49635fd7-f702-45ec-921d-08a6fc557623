package cn.iocoder.yudao.module.system.api.sms.dto.send;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * 短信發送給 Admin 或者 Member 用戶
 *
 * <AUTHOR>
 */
@Data
public class SmsSendSingleToUserReqDTO {

    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 手機號
     */
    @Mobile
    private String mobile;
    /**
     * 短信模板編號
     */
    @NotEmpty(message = "短信模板編號不能爲空")
    private String templateCode;
    /**
     * 短信模板參數
     */
    private Map<String, Object> templateParams;

}
