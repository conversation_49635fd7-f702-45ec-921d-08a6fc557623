package cn.iocoder.yudao.module.system.api.social;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserRespDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserUnbindReqDTO;

import javax.validation.Valid;

/**
 * 社交用戶的 API 接口
 *
 * <AUTHOR>
 */
public interface SocialUserApi {

    /**
     * 綁定社交用戶
     *
     * @param reqDTO 綁定信息
     * @return 社交用戶 openid
     */
    String bindSocialUser(@Valid SocialUserBindReqDTO reqDTO);

    /**
     * 取消綁定社交用戶
     *
     * @param reqDTO 解綁
     */
    void unbindSocialUser(@Valid SocialUserUnbindReqDTO reqDTO);

    /**
     * 獲得社交用戶，基於 userId
     *
     * @param userType   用戶類型
     * @param userId     用戶編號
     * @param socialType 社交平臺的類型
     * @return 社交用戶
     */
    SocialUserRespDTO getSocialUserByUserId(Integer userType, Long userId, Integer socialType);

    /**
     * 獲得社交用戶
     *
     * 在認證信息不正確的情況下，也會拋出 {@link ServiceException} 業務異常
     *
     * @param userType   用戶類型
     * @param socialType 社交平臺的類型
     * @param code       授權碼
     * @param state      state
     * @return 社交用戶
     */
    SocialUserRespDTO getSocialUserByCode(Integer userType, Integer socialType, String code, String state);

}
