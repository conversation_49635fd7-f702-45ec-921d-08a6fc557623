package cn.iocoder.yudao.module.system.api.permission;

import cn.iocoder.yudao.module.system.api.permission.dto.DeptDataPermissionRespDTO;

import java.util.Collection;
import java.util.Set;

/**
 * 權限 API 接口
 *
 * <AUTHOR>
 */
public interface PermissionApi {

    /**
     * 獲得擁有多個角色的用戶編號集合
     *
     * @param roleIds 角色編號集合
     * @return 用戶編號集合
     */
    Set<Long> getUserRoleIdListByRoleIds(Collection<Long> roleIds);

    /**
     * 判斷是否有權限，任一一個即可
     *
     * @param userId 用戶編號
     * @param permissions 權限
     * @return 是否
     */
    boolean hasAnyPermissions(Long userId, String... permissions);

    /**
     * 判斷是否有角色，任一一個即可
     *
     * @param userId 用戶編號
     * @param roles 角色數組
     * @return 是否
     */
    boolean hasAnyRoles(Long userId, String... roles);

    /**
     * 獲得登陸用戶的部門數據權限
     *
     * @param userId 用戶編號
     * @return 部門數據權限
     */
    DeptDataPermissionRespDTO getDeptDataPermission(Long userId);

}
