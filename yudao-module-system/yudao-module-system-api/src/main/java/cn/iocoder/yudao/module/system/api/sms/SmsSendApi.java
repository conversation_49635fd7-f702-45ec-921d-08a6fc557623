package cn.iocoder.yudao.module.system.api.sms;

import cn.iocoder.yudao.module.system.api.sms.dto.send.SmsSendSingleToUserReqDTO;

import javax.validation.Valid;

/**
 * 短信發送 API 接口
 *
 * <AUTHOR>
 */
public interface SmsSendApi {

    /**
     * 發送單條短信給 Admin 用戶
     *
     * 在 mobile 爲空時，使用 userId 加載對應 Admin 的手機號
     *
     * @param reqDTO 發送請求
     * @return 發送日誌編號
     */
    Long sendSingleSmsToAdmin(@Valid SmsSendSingleToUserReqDTO reqDTO);

    /**
     * 發送單條短信給 Member 用戶
     *
     * 在 mobile 爲空時，使用 userId 加載對應 Member 的手機號
     *
     * @param reqDTO 發送請求
     * @return 發送日誌編號
     */
    Long sendSingleSmsToMember(@Valid SmsSendSingleToUserReqDTO reqDTO);

}
