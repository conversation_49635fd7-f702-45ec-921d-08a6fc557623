package cn.iocoder.yudao.module.system.api.social.dto;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序訂閱消息發送 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class SocialWxaSubscribeMessageSendReqDTO {

    /**
     * 用戶編號
     *
     * 關聯 MemberUserDO 的 id 編號
     * 關聯 AdminUserDO 的 id 編號
     */
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;
    /**
     * 用戶類型
     *
     * 關聯 {@link UserTypeEnum}
     */
    @NotNull(message = "用戶類型不能爲空")
    private Integer userType;

    /**
     * 消息模版標題
     */
    @NotEmpty(message = "消息模版標題不能爲空")
    private String templateTitle;

    /**
     * 點擊模板卡片後的跳轉頁面，僅限本小程序內的頁面
     *
     * 支持帶參數，（示例 index?foo=bar ）。該字段不填則模板無跳轉。
     */
    private String page;

    /**
     * 模板內容的參數
     */
    private Map<String, String> messages;

    public SocialWxaSubscribeMessageSendReqDTO addMessage(String key, String value) {
        if (messages == null) {
            messages = new HashMap<>();
        }
        messages.put(key, value);
        return this;
    }

}
