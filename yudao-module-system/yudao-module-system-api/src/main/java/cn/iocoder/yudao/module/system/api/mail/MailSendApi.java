package cn.iocoder.yudao.module.system.api.mail;

import cn.iocoder.yudao.module.system.api.mail.dto.MailSendSingleToUserReqDTO;

import javax.validation.Valid;

/**
 * 郵箱發送 API 接口
 *
 * <AUTHOR>
 */
public interface MailSendApi {

    /**
     * 發送單條郵箱給 Admin 用戶
     *
     * 在 mail 爲空時，使用 userId 加載對應 Admin 的郵箱
     *
     * @param reqDTO 發送請求
     * @return 發送日誌編號
     */
    Long sendSingleMailToAdmin(@Valid MailSendSingleToUserReqDTO reqDTO);

    /**
     * 發送單條郵箱給 Member 用戶
     *
     * 在 mail 爲空時，使用 userId 加載對應 Member 的郵箱
     *
     * @param reqDTO 發送請求
     * @return 發送日誌編號
     */
    Long sendSingleMailToMember(@Valid MailSendSingleToUserReqDTO reqDTO);

}
