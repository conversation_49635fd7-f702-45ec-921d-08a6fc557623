package cn.iocoder.yudao.module.system.api.oauth2;

import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import cn.iocoder.yudao.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;

import javax.validation.Valid;

/**
 * OAuth2.0 Token API 接口
 *
 * <AUTHOR>
 */
public interface OAuth2TokenApi {

    /**
     * 創建訪問令牌
     *
     * @param reqDTO 訪問令牌的創建信息
     * @return 訪問令牌的信息
     */
    OAuth2AccessTokenRespDTO createAccessToken(@Valid OAuth2AccessTokenCreateReqDTO reqDTO);

    /**
     * 校驗訪問令牌
     *
     * @param accessToken 訪問令牌
     * @return 訪問令牌的信息
     */
    OAuth2AccessTokenCheckRespDTO checkAccessToken(String accessToken);

    /**
     * 移除訪問令牌
     *
     * @param accessToken 訪問令牌
     * @return 訪問令牌的信息
     */
    OAuth2AccessTokenRespDTO removeAccessToken(String accessToken);

    /**
     * 刷新訪問令牌
     *
     * @param refreshToken 刷新令牌
     * @param clientId 客戶端編號
     * @return 訪問令牌的信息
     */
    OAuth2AccessTokenRespDTO refreshAccessToken(String refreshToken, String clientId);

}
