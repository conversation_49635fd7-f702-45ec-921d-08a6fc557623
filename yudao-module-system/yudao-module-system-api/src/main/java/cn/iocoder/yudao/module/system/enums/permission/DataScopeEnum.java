package cn.iocoder.yudao.module.system.enums.permission;

import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 數據範圍枚舉類
 *
 * 用於實現數據級別的權限
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DataScopeEnum implements ArrayValuable<Integer> {

    ALL(1), // 全部數據權限

    DEPT_CUSTOM(2), // 指定部門數據權限
    DEPT_ONLY(3), // 部門數據權限
    DEPT_AND_CHILD(4), // 部門及以下數據權限

    SELF(5); // 僅本人數據權限

    /**
     * 範圍
     */
    private final Integer scope;

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(DataScopeEnum::getScope).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

}
