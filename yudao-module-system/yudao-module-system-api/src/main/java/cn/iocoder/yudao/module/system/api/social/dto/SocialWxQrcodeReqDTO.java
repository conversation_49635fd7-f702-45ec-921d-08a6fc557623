package cn.iocoder.yudao.module.system.api.social.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 獲取小程序碼 Request DTO
 *
 * <AUTHOR>
 * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getUnlimitedQRCode.html">獲取不限制的小程序碼</a>
 */
@Data
public class SocialWxQrcodeReqDTO {

    /**
     * 頁面路徑不能攜帶參數（參數請放在scene字段裏）
     */
    public static final String SCENE = "";
    /**
     * 二維碼寬度
     */
    public static final Integer WIDTH = 430;
    /**
     * 自動配置線條顏色，如果顏色依然是黑色，則說明不建議配置主色調
     */
    public static final Boolean AUTO_COLOR = true;
    /**
     * 檢查 page 是否存在
     */
    public static final Boolean CHECK_PATH = true;
    /**
     * 是否需要透明底色
     *
     * hyaline 爲 true 時，生成透明底色的小程序碼
     */
    public static final Boolean HYALINE = true;

    /**
     * 場景
     */
    @NotEmpty(message = "場景不能爲空")
    private String scene;
    /**
     * 頁面路徑
     */
    @NotEmpty(message = "頁面路徑不能爲空")
    private String path;
    /**
     * 二維碼寬度
     */
    private Integer width;

    /**
     * 是否需要透明底色
     */
    private Boolean autoColor;
    /**
     * 是否檢查 page 是否存在
     */
    private Boolean checkPath;
    /**
     * 是否需要透明底色
     */
    private Boolean hyaline;

}
