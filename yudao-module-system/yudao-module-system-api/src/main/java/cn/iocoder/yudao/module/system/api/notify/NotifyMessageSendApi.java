package cn.iocoder.yudao.module.system.api.notify;

import cn.iocoder.yudao.module.system.api.notify.dto.NotifySendSingleToUserReqDTO;

import javax.validation.Valid;

/**
 * 站內信發送 API 接口
 *
 * <AUTHOR>
 */
public interface NotifyMessageSendApi {

    /**
     * 發送單條站內信給 Admin 用戶
     *
     * @param reqDTO 發送請求
     * @return 發送消息 ID
     */
    Long sendSingleMessageToAdmin(@Valid NotifySendSingleToUserReqDTO reqDTO);

    /**
     * 發送單條站內信給 Member 用戶
     *
     * @param reqDTO 發送請求
     * @return 發送消息 ID
     */
    Long sendSingleMessageToMember(@Valid NotifySendSingleToUserReqDTO reqDTO);

}
