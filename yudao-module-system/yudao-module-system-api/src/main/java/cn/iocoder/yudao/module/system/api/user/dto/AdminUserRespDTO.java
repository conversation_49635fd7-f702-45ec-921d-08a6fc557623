package cn.iocoder.yudao.module.system.api.user.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import lombok.Data;

import java.util.Set;

/**
 * Admin 用戶 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserRespDTO {

    /**
     * 用戶ID
     */
    private Long id;
    /**
     * 用戶暱稱
     */
    private String nickname;
    /**
     * 帳號狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 部門ID
     */
    private Long deptId;
    /**
     * 崗位編號數組
     */
    private Set<Long> postIds;
    /**
     * 手機號碼
     */
    private String mobile;
    /**
     * 用戶頭像
     */
    private String avatar;

    /**
     * 上司用戶ID
     */
    private Long leaderId;

    /**
     * 電郵
     */
    private String email;

}
