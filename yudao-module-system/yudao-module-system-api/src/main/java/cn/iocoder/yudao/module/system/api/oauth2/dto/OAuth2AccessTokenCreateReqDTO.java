package cn.iocoder.yudao.module.system.api.oauth2.dto;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * OAuth2.0 訪問令牌創建 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class OAuth2AccessTokenCreateReqDTO implements Serializable {

    /**
     * 用戶編號
     */
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;
    /**
     * 用戶類型
     */
    @NotNull(message = "用戶類型不能爲空")
    @InEnum(value = UserTypeEnum.class, message = "用戶類型必須是 {value}")
    private Integer userType;
    /**
     * 客戶端編號
     */
    @NotNull(message = "客戶端編號不能爲空")
    private String clientId;
    /**
     * 授權範圍
     */
    private List<String> scopes;

}
