package cn.iocoder.yudao.module.system.enums.oauth2;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OAuth2 授權類型（模式）的枚舉
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum OAuth2GrantTypeEnum {

    PASSWORD("password"), // 密碼模式
    AUTHORIZATION_CODE("authorization_code"), // 授權碼模式
    IMPLICIT("implicit"), // 簡化模式
    CLIENT_CREDENTIALS("client_credentials"), // 客戶端模式
    REFRESH_TOKEN("refresh_token"), // 刷新模式
    ;

    private final String grantType;

    public static OAuth2GrantTypeEnum getByGrantType(String grantType) {
        return ArrayUtil.firstMatch(o -> o.getGrantType().equals(grantType), values());
    }

}
