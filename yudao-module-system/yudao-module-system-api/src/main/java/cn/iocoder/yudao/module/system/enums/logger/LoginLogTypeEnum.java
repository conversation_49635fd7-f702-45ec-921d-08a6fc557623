package cn.iocoder.yudao.module.system.enums.logger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登錄日誌的類型枚舉
 */
@Getter
@AllArgsConstructor
public enum LoginLogTypeEnum {

    LOGIN_USERNAME(100), // 使用賬號登錄
    LOGIN_SOCIAL(101), // 使用社交登錄
    LOGIN_MOBILE(103), // 使用手機登陸
    LOGIN_SMS(104), // 使用短信登陸

    LOGOUT_SELF(200),  // 自己主動登出
    LOGOUT_DELETE(202), // 強制退出
    ;

    /**
     * 日誌類型
     */
    private final Integer type;

}
