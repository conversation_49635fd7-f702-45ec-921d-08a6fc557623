package cn.iocoder.yudao.module.system.api.sms.dto.code;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 短信驗證碼的校驗 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class SmsCodeValidateReqDTO {

    /**
     * 手機號
     */
    @Mobile
    @NotEmpty(message = "手機號不能爲空")
    private String mobile;
    /**
     * 發送場景
     */
    @NotNull(message = "發送場景不能爲空")
    @InEnum(SmsSceneEnum.class)
    private Integer scene;
    /**
     * 驗證碼
     */
    @NotEmpty(message = "驗證碼")
    private String code;

}
