package cn.iocoder.yudao.module.system.api.user;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSONArray;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Admin 用戶 API 接口
 *
 * <AUTHOR>
 */
public interface AdminUserApi {

    /**
     * 通過用戶 ID 查詢用戶
     *
     * @param id 用戶ID
     * @return 用戶對象信息
     */
    AdminUserRespDTO getUser(Long id);

    /**
     * 通過用戶 ID 查詢用戶下屬
     *
     * @param id 用戶編號
     * @return 用戶下屬用戶列表
     */
    List<AdminUserRespDTO> getUserListBySubordinate(Long id);

    /**
     * 通過用戶 ID 查詢用戶們
     *
     * @param ids 用戶 ID 們
     * @return 用戶對象信息
     */
    List<AdminUserRespDTO> getUserList(Collection<Long> ids);

    /**
     * 獲得指定部門的用戶數組
     *
     * @param deptIds 部門數組
     * @return 用戶數組
     */
    List<AdminUserRespDTO> getUserListByDeptIds(Collection<Long> deptIds);

    /**
     * 獲得指定崗位的用戶數組
     *
     * @param postIds 崗位數組
     * @return 用戶數組
     */
    List<AdminUserRespDTO> getUserListByPostIds(Collection<Long> postIds);

    /**
     * 獲得用戶 Map
     *
     * @param ids 用戶編號數組
     * @return 用戶 Map
     */
    default Map<Long, AdminUserRespDTO> getUserMap(Collection<Long> ids) {
        List<AdminUserRespDTO> users = getUserList(ids);
        return CollectionUtils.convertMap(users, AdminUserRespDTO::getId);
    }

    /**
     * 校驗用戶是否有效。如下情況，視爲無效：
     * 1. 用戶編號不存在
     * 2. 用戶被禁用
     *
     * @param id 用戶編號
     */
    default void validateUser(Long id) {
        validateUserList(Collections.singleton(id));
    }

    /**
     * 校驗用戶們是否有效。如下情況，視爲無效：
     * 1. 用戶編號不存在
     * 2. 用戶被禁用
     *
     * @param ids 用戶編號數組
     */
    void validateUserList(Collection<Long> ids);

    /**
     * 獲取用戶列表，基于 IDs
     *
     * @param userIds 用戶 IDs
     * @return 用戶列表
     */
    JSONArray getUserListByIds(List<Long> userIds);

}
