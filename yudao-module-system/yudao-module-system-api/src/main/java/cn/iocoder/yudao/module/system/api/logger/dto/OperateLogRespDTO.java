package cn.iocoder.yudao.module.system.api.logger.dto;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系統操作日誌 Resp DTO
 *
 * <AUTHOR>
 */
@Data
public class OperateLogRespDTO implements VO {

    /**
     * 日誌編號
     */
    private Long id;
    /**
     * 鏈路追蹤編號
     */
    private String traceId;
    /**
     * 用戶編號
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO",
            fields = "nickname", ref = "userName")
    private Long userId;
    /**
     * 用戶名稱
     */
    private String userName;
    /**
     * 用戶類型
     */
    private Integer userType;
    /**
     * 操作模塊類型
     */
    private String type;
    /**
     * 操作名
     */
    private String subType;
    /**
     * 操作模塊業務編號
     */
    private Long bizId;
    /**
     * 操作內容
     */
    private String action;
    /**
     * 拓展字段
     */
    private String extra;

    /**
     * 請求方法名
     */
    private String requestMethod;
    /**
     * 請求地址
     */
    private String requestUrl;
    /**
     * 用戶 IP
     */
    private String userIp;
    /**
     * 瀏覽器 UA
     */
    private String userAgent;

    /**
     * 創建時間
     */
    private LocalDateTime createTime;

}
