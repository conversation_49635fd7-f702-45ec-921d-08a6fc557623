package cn.iocoder.yudao.module.system.api.notify.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 站內信發送給 Admin 或者 Member 用戶
 *
 * <AUTHOR>
 */
@Data
public class NotifySendSingleToUserReqDTO {

    /**
     * 用戶編號
     */
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;

    /**
     * 站內信模板編號
     */
    @NotEmpty(message = "站內信模板編號不能爲空")
    private String templateCode;

    /**
     * 站內信模板參數
     */
    private Map<String, Object> templateParams;
}
