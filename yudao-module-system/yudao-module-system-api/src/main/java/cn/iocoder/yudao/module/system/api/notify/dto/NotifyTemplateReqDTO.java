package cn.iocoder.yudao.module.system.api.notify.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class NotifyTemplateReqDTO {

    @NotEmpty(message = "模版名稱不能爲空")
    private String name;

    @NotNull(message = "模版編碼不能爲空")
    private String code;

    @NotNull(message = "模版類型不能爲空")
    private Integer type;

    @NotEmpty(message = "發送人名稱不能爲空")
    private String nickname;

    @NotEmpty(message = "模版內容不能爲空")
    private String content;

    @NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "狀態必須是 {value}")
    private Integer status;

    private String remark;

}
