package cn.iocoder.yudao.module.system.enums;

/**
 * System 操作日誌枚舉
 * 目的：統一管理，也減少 Service 裏各種“複雜”字符串
 *
 * <AUTHOR>
 */
public interface LogRecordConstants {

    // ======================= SYSTEM_USER 用戶 =======================

    String SYSTEM_USER_TYPE = "SYSTEM 用戶";
    String SYSTEM_USER_CREATE_SUB_TYPE = "創建用戶";
    String SYSTEM_USER_CREATE_SUCCESS = "創建了用戶【{{#user.nickname}}】";
    String SYSTEM_USER_UPDATE_SUB_TYPE = "更新用戶";
    String SYSTEM_USER_UPDATE_SUCCESS = "更新了用戶【{{#user.nickname}}】: {_DIFF{#updateReqVO}}";
    String SYSTEM_USER_DELETE_SUB_TYPE = "刪除用戶";
    String SYSTEM_USER_DELETE_SUCCESS = "刪除了用戶【{{#user.nickname}}】";
    String SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE = "重置用戶密碼";
    String SYSTEM_USER_UPDATE_PASSWORD_SUCCESS = "將用戶【{{#user.nickname}}】的密碼從【{{#user.password}}】重置爲【{{#newPassword}}】";

    // ======================= SYSTEM_ROLE 角色 =======================

    String SYSTEM_ROLE_TYPE = "SYSTEM 角色";
    String SYSTEM_ROLE_CREATE_SUB_TYPE = "創建角色";
    String SYSTEM_ROLE_CREATE_SUCCESS = "創建了角色【{{#role.name}}】";
    String SYSTEM_ROLE_UPDATE_SUB_TYPE = "更新角色";
    String SYSTEM_ROLE_UPDATE_SUCCESS = "更新了角色【{{#role.name}}】: {_DIFF{#updateReqVO}}";
    String SYSTEM_ROLE_DELETE_SUB_TYPE = "刪除角色";
    String SYSTEM_ROLE_DELETE_SUCCESS = "刪除了角色【{{#role.name}}】";

}
