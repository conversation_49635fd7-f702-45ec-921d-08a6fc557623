package cn.iocoder.yudao.module.system.api.social.dto;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 社交綁定 Request DTO，使用 code 授權碼
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SocialUserUnbindReqDTO {

    /**
     * 用戶編號
     */
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;
    /**
     * 用戶類型
     */
    @InEnum(UserTypeEnum.class)
    @NotNull(message = "用戶類型不能爲空")
    private Integer userType;

    /**
     * 社交平臺的類型
     */
    @InEnum(SocialTypeEnum.class)
    @NotNull(message = "社交平臺的類型不能爲空")
    private Integer socialType;

    /**
     * 社交平臺的 openid
     */
    @NotEmpty(message = "社交平臺的 openid 不能爲空")
    private String openid;

}
