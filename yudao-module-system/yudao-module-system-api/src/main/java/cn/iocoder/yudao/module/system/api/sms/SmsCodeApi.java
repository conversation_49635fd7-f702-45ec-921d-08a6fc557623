package cn.iocoder.yudao.module.system.api.sms;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeValidateReqDTO;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeSendReqDTO;
import cn.iocoder.yudao.module.system.api.sms.dto.code.SmsCodeUseReqDTO;

import javax.validation.Valid;

/**
 * 短信驗證碼 API 接口
 *
 * <AUTHOR>
 */
public interface SmsCodeApi {

    /**
     * 創建短信驗證碼，並進行發送
     *
     * @param reqDTO 發送請求
     */
    void sendSmsCode(@Valid SmsCodeSendReqDTO reqDTO);

    /**
     * 驗證短信驗證碼，並進行使用
     * 如果正確，則將驗證碼標記成已使用
     * 如果錯誤，則拋出 {@link ServiceException} 異常
     *
     * @param reqDTO 使用請求
     */
    void useSmsCode(@Valid SmsCodeUseReqDTO reqDTO);

    /**
     * 檢查驗證碼是否有效
     *
     * @param reqDTO 校驗請求
     */
    void validateSmsCode(@Valid SmsCodeValidateReqDTO reqDTO);

}
