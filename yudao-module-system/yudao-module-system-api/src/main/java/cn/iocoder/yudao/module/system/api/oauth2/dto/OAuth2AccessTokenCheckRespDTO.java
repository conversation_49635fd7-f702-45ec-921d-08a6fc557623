package cn.iocoder.yudao.module.system.api.oauth2.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * OAuth2.0 訪問令牌的校驗 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class OAuth2AccessTokenCheckRespDTO implements Serializable {

    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 用戶類型
     */
    private Integer userType;
    /**
     * 用戶信息
     */
    private Map<String, String> userInfo;
    /**
     * 租戶編號
     */
    private Long tenantId;
    /**
     * 授權範圍的數組
     */
    private List<String> scopes;
    /**
     * 過期時間
     */
    private LocalDateTime expiresTime;

}
