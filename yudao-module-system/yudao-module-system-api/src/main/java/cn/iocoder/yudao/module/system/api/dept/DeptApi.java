package cn.iocoder.yudao.module.system.api.dept;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 部門 API 接口
 *
 * <AUTHOR>
 */
public interface DeptApi {

    /**
     * 獲得部門信息
     *
     * @param id 部門編號
     * @return 部門信息
     */
    DeptRespDTO getDept(Long id);

    /**
     * 獲得部門信息數組
     *
     * @param ids 部門編號數組
     * @return 部門信息數組
     */
    List<DeptRespDTO> getDeptList(Collection<Long> ids);

    /**
     * 校驗部門們是否有效。如下情況，視爲無效：
     * 1. 部門編號不存在
     * 2. 部門被禁用
     *
     * @param ids 角色編號數組
     */
    void validateDeptList(Collection<Long> ids);

    /**
     * 獲得指定編號的部門 Map
     *
     * @param ids 部門編號數組
     * @return 部門 Map
     */
    default Map<Long, DeptRespDTO> getDeptMap(Collection<Long> ids) {
        List<DeptRespDTO> list = getDeptList(ids);
        return CollectionUtils.convertMap(list, DeptRespDTO::getId);
    }

    /**
     * 獲得指定部門的所有子部門
     *
     * @param id 部門編號
     * @return 子部門列表
     */
    List<DeptRespDTO> getChildDeptList(Long id);

    /**
     * 獲得指定部門的所有子部門
     *
     * @param deptIds 部門編號
     * @return 子部門列表
     */
    JSONArray getDeptListByIds(List<Long> deptIds);

    /**
     * 獲得指定部門的所有子部門
     * @param parentId
     * @return
     */
    List<Long> getDeptListByParentId(Long parentId);

}
