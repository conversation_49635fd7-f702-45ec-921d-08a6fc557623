package cn.iocoder.yudao.module.system.controller.admin.mail.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理後臺 - 郵件末班 Response VO")
@Data
public class MailTemplateRespVO {

    @Schema(description = "編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "模版名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試名字")
    private String name;

    @Schema(description = "模版編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "test")
    private String code;

    @Schema(description = "發送的郵箱賬號編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long accountId;

    @Schema(description = "發送人名稱", example = "芋頭")
    private String nickname;

    @Schema(description = "標題", requiredMode = Schema.RequiredMode.REQUIRED, example = "註冊成功")
    private String title;

    @Schema(description = "內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "你好，註冊成功啦")
    private String content;

    @Schema(description = "參數數組", example = "name,code")
    private List<String> params;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "備註", example = "奧特曼")
    private String remark;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
