package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 租戶精簡 Response VO")
@Data
public class TenantSimpleRespVO {

    @Schema(description = "租戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "租戶名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

}
