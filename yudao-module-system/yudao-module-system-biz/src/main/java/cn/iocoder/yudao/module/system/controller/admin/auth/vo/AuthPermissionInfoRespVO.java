package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Schema(description = "管理後臺 - 登錄用戶的權限信息 Response VO，額外包括用戶信息和角色列表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthPermissionInfoRespVO {

    @Schema(description = "用戶信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserVO user;

    @Schema(description = "角色標識數組", requiredMode = Schema.RequiredMode.REQUIRED)
    private Set<String> roles;

    @Schema(description = "操作權限數組", requiredMode = Schema.RequiredMode.REQUIRED)
    private Set<String> permissions;

    @Schema(description = "菜單樹", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<MenuVO> menus;

    @Schema(description = "用戶信息 VO")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserVO {

        @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
        private Long id;

        @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道源碼")
        private String nickname;

        @Schema(description = "用戶頭像", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xx.jpg")
        private String avatar;

        @Schema(description = "部門編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "2048")
        private Long deptId;

    }

    @Schema(description = "管理後臺 - 登錄用戶的菜單信息 Response VO")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MenuVO {

        @Schema(description = "菜單名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
        private Long id;

        @Schema(description = "父菜單 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
        private Long parentId;

        @Schema(description = "菜單名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
        private String name;

        @Schema(description = "路由地址,僅菜單類型爲菜單或者目錄時，才需要傳", example = "post")
        private String path;

        @Schema(description = "組件路徑,僅菜單類型爲菜單時，才需要傳", example = "system/post/index")
        private String component;

        @Schema(description = "組件名", example = "SystemUser")
        private String componentName;

        @Schema(description = "菜單圖標,僅菜單類型爲菜單或者目錄時，才需要傳", example = "/menu/list")
        private String icon;

        @Schema(description = "是否可見", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
        private Boolean visible;

        @Schema(description = "是否緩存", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
        private Boolean keepAlive;

        @Schema(description = "是否總是顯示", example = "false")
        private Boolean alwaysShow;

        /**
         * 子路由
         */
        private List<MenuVO> children;

    }

}
