package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 租戶 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantRespVO {

    @Schema(description = "租戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("租戶編號")
    private Long id;

    @Schema(description = "租戶名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @ExcelProperty("租戶名")
    private String name;

    @Schema(description = "聯繫人", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("聯繫人")
    private String contactName;

    @Schema(description = "聯繫手機", example = "15601691300")
    @ExcelProperty("聯繫手機")
    private String contactMobile;

    @Schema(description = "租戶狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "綁定域名", example = "https://www.iocoder.cn")
    private String website;

    @Schema(description = "租戶套餐編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long packageId;

    @Schema(description = "過期時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime expireTime;

    @Schema(description = "賬號數量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer accountCount;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("創建時間")
    private LocalDateTime createTime;

}
