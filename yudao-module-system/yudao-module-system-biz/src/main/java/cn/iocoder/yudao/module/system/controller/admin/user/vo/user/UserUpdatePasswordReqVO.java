package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理後臺 - 用戶更新密碼 Request VO")
@Data
public class UserUpdatePasswordReqVO {

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "用戶編號不能爲空")
    private Long id;

    @Schema(description = "密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotEmpty(message = "密碼不能爲空")
    @Length(min = 4, max = 16, message = "密碼長度爲 4-16 位")
    private String password;

}
