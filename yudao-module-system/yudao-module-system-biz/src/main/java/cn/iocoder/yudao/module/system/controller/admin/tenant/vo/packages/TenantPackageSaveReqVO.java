package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

@Schema(description = "管理後臺 - 租戶套餐創建/修改 Request VO")
@Data
public class TenantPackageSaveReqVO {

    @Schema(description = "套餐編號", example = "1024")
    private Long id;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "VIP")
    @NotEmpty(message = "套餐名不能爲空")
    private String name;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "狀態必須是 {value}")
    private Integer status;

    @Schema(description = "備註", example = "好")
    private String remark;

    @Schema(description = "關聯的菜單編號", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "關聯的菜單編號不能爲空")
    private Set<Long> menuIds;

}
