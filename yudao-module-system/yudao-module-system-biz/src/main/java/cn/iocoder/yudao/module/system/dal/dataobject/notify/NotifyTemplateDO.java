package cn.iocoder.yudao.module.system.dal.dataobject.notify;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.util.List;

/**
 * 站內信模版 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_notify_template", autoResultMap = true)
@KeySequence("system_notify_template_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotifyTemplateDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 模版名稱
     */
    private String name;
    /**
     * 模版編碼
     */
    private String code;
    /**
     * 模版類型
     *
     * 對應 system_notify_template_type 字典
     */
    private Integer type;
    /**
     * 發送人名稱
     */
    private String nickname;
    /**
     * 模版內容
     */
    private String content;
    /**
     * 參數數組
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> params;
    /**
     * 狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 備註
     */
    private String remark;

}
