package cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Set;

@Schema(description = "管理後臺 - 賦予角色菜單 Request VO")
@Data
public class PermissionAssignRoleMenuReqVO {

    @Schema(description = "角色編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "角色編號不能爲空")
    private Long roleId;

    @Schema(description = "菜單編號列表", example = "1,3,5")
    private Set<Long> menuIds = Collections.emptySet(); // 兜底

}
