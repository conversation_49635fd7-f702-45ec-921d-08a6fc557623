package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 用戶 Excel 導入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 設置 chain = false，避免用戶導入有問題
public class UserImportExcelVO {

    @ExcelProperty("登錄名稱")
    private String username;

    @ExcelProperty("用戶名稱")
    private String nickname;

    @ExcelProperty("部門編號")
    private Long deptId;

    @ExcelProperty("用戶郵箱")
    private String email;

    @ExcelProperty("手機號碼")
    private String mobile;

    @ExcelProperty(value = "用戶性別", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @ExcelProperty(value = "賬號狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

}
