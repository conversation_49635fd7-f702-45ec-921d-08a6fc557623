package cn.iocoder.yudao.module.system.controller.admin.sms.vo.template;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理後臺 - 短信模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsTemplateRespVO {

    @Schema(description = "編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("編號")
    private Long id;

    @Schema(description = "短信類型，參見 SmsTemplateTypeEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "短信簽名", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SMS_TEMPLATE_TYPE)
    private Integer type;

    @Schema(description = "開啓狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "開啓狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test_01")
    @ExcelProperty("模板編碼")
    private String code;

    @Schema(description = "模板名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @ExcelProperty("模板名稱")
    private String name;

    @Schema(description = "模板內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "你好，{name}。你長的太{like}啦！")
    @ExcelProperty("模板內容")
    private String content;

    @Schema(description = "參數數組", example = "name,code")
    private List<String> params;

    @Schema(description = "備註", example = "哈哈哈")
    @ExcelProperty("備註")
    private String remark;

    @Schema(description = "短信 API 的模板編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "4383920")
    @ExcelProperty("短信 API 的模板編號")
    private String apiTemplateId;

    @Schema(description = "短信渠道編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @ExcelProperty("短信渠道編號")
    private Long channelId;

    @Schema(description = "短信渠道編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "ALIYUN")
    @ExcelProperty(value = "短信渠道編碼", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SMS_CHANNEL_CODE)
    private String channelCode;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("創建時間")
    private LocalDateTime createTime;

}
