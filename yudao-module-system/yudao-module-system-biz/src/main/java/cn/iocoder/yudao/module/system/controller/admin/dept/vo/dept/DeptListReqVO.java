package cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 部門列表 Request VO")
@Data
public class DeptListReqVO {

    @Schema(description = "部門名稱，模糊匹配", example = "芋道")
    private String name;

    @Schema(description = "展示狀態，參見 CommonStatusEnum 枚舉類", example = "1")
    private Integer status;

}
