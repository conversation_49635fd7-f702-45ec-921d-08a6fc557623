package cn.iocoder.yudao.module.system.controller.admin.logger.vo.operatelog;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理後臺 - 操作日誌分頁列表 Request VO")
@Data
public class OperateLogPageReqVO extends PageParam {

    @Schema(description = "用戶編號", example = "芋道")
    private Long userId;

    @Schema(description = "操作模塊業務編號", example = "1")
    private Long bizId;

    @Schema(description = "操作模塊，模擬匹配", example = "訂單")
    private String type;

    @Schema(description = "操作名，模擬匹配", example = "創建訂單")
    private String subType;

    @Schema(description = "操作明細，模擬匹配", example = "修改編號爲 1 的用戶信息")
    private String action;

    @Schema(description = "開始時間", example = "[2022-07-01 00:00:00,2022-07-01 23:59:59]")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
