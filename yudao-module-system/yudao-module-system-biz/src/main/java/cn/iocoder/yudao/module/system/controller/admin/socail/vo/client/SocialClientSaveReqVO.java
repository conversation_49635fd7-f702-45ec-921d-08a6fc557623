package cn.iocoder.yudao.module.system.controller.admin.socail.vo.client;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import java.util.Objects;

@Schema(description = "管理後臺 - 社交客戶端創建/修改 Request VO")
@Data
public class SocialClientSaveReqVO {

    @Schema(description = "編號", example = "27162")
    private Long id;

    @Schema(description = "應用名", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao商城")
    @NotNull(message = "應用名不能爲空")
    private String name;

    @Schema(description = "社交平臺的類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "31")
    @NotNull(message = "社交平臺的類型不能爲空")
    @InEnum(SocialTypeEnum.class)
    private Integer socialType;

    @Schema(description = "用戶類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "用戶類型不能爲空")
    @InEnum(UserTypeEnum.class)
    private Integer userType;

    @Schema(description = "客戶端編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "wwd411c69a39ad2e54")
    @NotNull(message = "客戶端編號不能爲空")
    private String clientId;

    @Schema(description = "客戶端密鑰", requiredMode = Schema.RequiredMode.REQUIRED, example = "peter")
    @NotNull(message = "客戶端密鑰不能爲空")
    private String clientSecret;

    @Schema(description = "授權方的網頁應用編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "2000045")
    private String agentId;

    @Schema(description = "狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    @InEnum(CommonStatusEnum.class)
    private Integer status;

    @AssertTrue(message = "agentId 不能爲空")
    @JsonIgnore
    public boolean isAgentIdValid() {
        // 如果是企業微信，必須填寫 agentId 屬性
        return !Objects.equals(socialType, SocialTypeEnum.WECHAT_ENTERPRISE.getType())
                || !StrUtil.isEmpty(agentId);
    }

}
