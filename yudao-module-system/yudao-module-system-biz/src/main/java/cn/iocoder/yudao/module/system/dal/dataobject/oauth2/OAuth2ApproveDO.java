package cn.iocoder.yudao.module.system.dal.dataobject.oauth2;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * OAuth2 批准 DO
 *
 * 用戶在 sso.vue 界面時，記錄接受的 scope 列表
 *
 * <AUTHOR>
 */
@TableName(value = "system_oauth2_approve", autoResultMap = true)
@KeySequence("system_oauth2_approve_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class OAuth2ApproveDO extends BaseDO {

    /**
     * 編號，數據庫自增
     */
    @TableId
    private Long id;
    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 用戶類型
     *
     * 枚舉 {@link UserTypeEnum}
     */
    private Integer userType;
    /**
     * 客戶端編號
     *
     * 關聯 {@link OAuth2ClientDO#getId()}
     */
    private String clientId;
    /**
     * 授權範圍
     */
    private String scope;
    /**
     * 是否接受
     *
     * true - 接受
     * false - 拒絕
     */
    private Boolean approved;
    /**
     * 過期時間
     */
    private LocalDateTime expiresTime;

}
