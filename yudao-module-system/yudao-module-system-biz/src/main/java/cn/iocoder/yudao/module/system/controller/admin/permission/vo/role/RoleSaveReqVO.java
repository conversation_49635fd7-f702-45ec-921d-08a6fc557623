package cn.iocoder.yudao.module.system.controller.admin.permission.vo.role;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 角色創建/更新 Request VO")
@Data
public class RoleSaveReqVO {

    @Schema(description = "角色編號", example = "1")
    private Long id;

    @Schema(description = "角色名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "管理員")
    @NotBlank(message = "角色名稱不能爲空")
    @Size(max = 30, message = "角色名稱長度不能超過 30 個字符")
    @DiffLogField(name = "角色名稱")
    private String name;

    @NotBlank(message = "角色標誌不能爲空")
    @Size(max = 100, message = "角色標誌長度不能超過 100 個字符")
    @Schema(description = "角色標誌", requiredMode = Schema.RequiredMode.REQUIRED, example = "ADMIN")
    @DiffLogField(name = "角色標誌")
    private String code;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "顯示順序不能爲空")
    @DiffLogField(name = "顯示順序")
    private Integer sort;

    @Schema(description = "狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @DiffLogField(name = "狀態")
    //@NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "狀態必須是 {value}")
    private Integer status;

    @Schema(description = "備註", example = "我是一個角色")
    @Size(max = 500, message = "備註長度不能超過 500 個字符")
    @DiffLogField(name = "備註")
    private String remark;

}
