package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理後臺 - 短信驗證碼的登錄 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthSmsLoginReqVO {

    @Schema(description = "手機號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudaoyuanma")
    @NotEmpty(message = "手機號不能爲空")
    @Mobile
    private String mobile;

    @Schema(description = "短信驗證碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "驗證碼不能爲空")
    private String code;

}
