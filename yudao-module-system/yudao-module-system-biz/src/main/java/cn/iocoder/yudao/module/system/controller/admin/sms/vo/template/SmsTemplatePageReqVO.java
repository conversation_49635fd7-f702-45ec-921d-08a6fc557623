package cn.iocoder.yudao.module.system.controller.admin.sms.vo.template;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理後臺 - 短信模板分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsTemplatePageReqVO extends PageParam {

    @Schema(description = "短信簽名", example = "1")
    private Integer type;

    @Schema(description = "開啓狀態", example = "1")
    private Integer status;

    @Schema(description = "模板編碼，模糊匹配", example = "test_01")
    private String code;

    @Schema(description = "模板內容，模糊匹配", example = "你好，{name}。你長的太{like}啦！")
    private String content;

    @Schema(description = "短信 API 的模板編號，模糊匹配", example = "4383920")
    private String apiTemplateId;

    @Schema(description = "短信渠道編號", example = "10")
    private Long channelId;

    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @Schema(description = "創建時間")
    private LocalDateTime[] createTime;

}
