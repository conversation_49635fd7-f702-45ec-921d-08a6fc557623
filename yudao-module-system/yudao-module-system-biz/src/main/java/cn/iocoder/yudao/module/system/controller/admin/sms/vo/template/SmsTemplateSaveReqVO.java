package cn.iocoder.yudao.module.system.controller.admin.sms.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理後臺 - 短信模板創建/修改 Request VO")
@Data
public class SmsTemplateSaveReqVO {

    @Schema(description = "編號", example = "1024")
    private Long id;

    @Schema(description = "短信類型，參見 SmsTemplateTypeEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "短信類型不能爲空")
    private Integer type;

    @Schema(description = "開啓狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "開啓狀態不能爲空")
    private Integer status;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test_01")
    @NotNull(message = "模板編碼不能爲空")
    private String code;

    @Schema(description = "模板名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @NotNull(message = "模板名稱不能爲空")
    private String name;

    @Schema(description = "模板內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "你好，{name}。你長的太{like}啦！")
    @NotNull(message = "模板內容不能爲空")
    private String content;

    @Schema(description = "備註", example = "哈哈哈")
    private String remark;

    @Schema(description = "短信 API 的模板編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "4383920")
    @NotNull(message = "短信 API 的模板編號不能爲空")
    private String apiTemplateId;

    @Schema(description = "短信渠道編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "短信渠道編號不能爲空")
    private Long channelId;

}
