package cn.iocoder.yudao.module.system.controller.admin.dict.vo.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 字典類型創建/修改 Request VO")
@Data
public class DictTypeSaveReqVO {

    @Schema(description = "字典類型編號", example = "1024")
    private Long id;

    @Schema(description = "字典名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "性別")
    @NotBlank(message = "字典名稱不能爲空")
    @Size(max = 100, message = "字典類型名稱長度不能超過100個字符")
    private String name;

    @Schema(description = "字典類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "sys_common_sex")
    @NotNull(message = "字典類型不能爲空")
    @Size(max = 100, message = "字典類型類型長度不能超過 100 個字符")
    private String type;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    private Integer status;

    @Schema(description = "備註", example = "快樂的備註")
    private String remark;

}
