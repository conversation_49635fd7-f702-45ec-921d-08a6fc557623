package cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Set;

@Schema(description = "管理後臺 - 賦予用戶角色 Request VO")
@Data
public class PermissionAssignUserRoleReqVO {

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用戶編號不能爲空")
    private Long userId;

    @Schema(description = "角色編號列表", example = "1,3,5")
    private Set<Long> roleIds = Collections.emptySet(); // 兜底

}
