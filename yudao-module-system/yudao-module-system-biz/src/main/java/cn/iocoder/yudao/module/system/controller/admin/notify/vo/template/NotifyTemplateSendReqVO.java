package cn.iocoder.yudao.module.system.controller.admin.notify.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Schema(description = "管理後臺 - 站內信模板的發送 Request VO")
@Data
public class NotifyTemplateSendReqVO {

    @Schema(description = "用戶id", requiredMode = Schema.RequiredMode.REQUIRED, example = "01")
    @NotNull(message = "用戶id不能爲空")
    private Long userId;

    @Schema(description = "用戶類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用戶類型不能爲空")
    private Integer userType;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "01")
    @NotEmpty(message = "模板編碼不能爲空")
    private String templateCode;

    @Schema(description = "模板參數")
    private Map<String, Object> templateParams;

}
