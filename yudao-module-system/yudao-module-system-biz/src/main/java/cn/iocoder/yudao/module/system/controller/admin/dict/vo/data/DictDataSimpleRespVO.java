package cn.iocoder.yudao.module.system.controller.admin.dict.vo.data;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 數據字典精簡 Response VO")
@Data
public class DictDataSimpleRespVO {

    @Schema(description = "字典類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "gender")
    private String dictType;

    @Schema(description = "字典鍵值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String value;

    @Schema(description = "字典標籤", requiredMode = Schema.RequiredMode.REQUIRED, example = "男")
    private String label;

    @Schema(description = "顏色類型，default、primary、success、info、warning、danger", example = "default")
    private String colorType;

    @Schema(description = "css 樣式", example = "btn-visible")
    private String cssClass;

}
