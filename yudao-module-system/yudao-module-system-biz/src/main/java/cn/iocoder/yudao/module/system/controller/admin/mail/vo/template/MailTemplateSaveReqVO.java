package cn.iocoder.yudao.module.system.controller.admin.mail.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理後臺 - 郵件模版創建/修改 Request VO")
@Data
public class MailTemplateSaveReqVO {

    @Schema(description = "編號", example = "1024")
    private Long id;

    @Schema(description = "模版名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試名字")
    @NotNull(message = "名稱不能爲空")
    private String name;

    @Schema(description = "模版編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "test")
    @NotNull(message = "模版編號不能爲空")
    private String code;

    @Schema(description = "發送的郵箱賬號編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "發送的郵箱賬號編號不能爲空")
    private Long accountId;

    @Schema(description = "發送人名稱", example = "芋頭")
    private String nickname;

    @Schema(description = "標題", requiredMode = Schema.RequiredMode.REQUIRED, example = "註冊成功")
    @NotEmpty(message = "標題不能爲空")
    private String title;

    @Schema(description = "內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "你好，註冊成功啦")
    @NotEmpty(message = "內容不能爲空")
    private String content;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    private Integer status;

    @Schema(description = "備註", example = "奧特曼")
    private String remark;

}
