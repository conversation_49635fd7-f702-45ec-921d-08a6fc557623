package cn.iocoder.yudao.module.system.controller.admin.dept.vo.post;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 崗位創建/修改 Request VO")
@Data
public class PostSaveReqVO {

    @Schema(description = "崗位編號", example = "1024")
    private Long id;

    @Schema(description = "崗位名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "小土豆")
    @NotBlank(message = "崗位名稱不能爲空")
    @Size(max = 50, message = "崗位名稱長度不能超過 50 個字符")
    private String name;

    @Schema(description = "崗位編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @NotBlank(message = "崗位編碼不能爲空")
    @Size(max = 64, message = "崗位編碼長度不能超過64個字符")
    private String code;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "顯示順序不能爲空")
    private Integer sort;

    @Schema(description = "狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(CommonStatusEnum.class)
    private Integer status;

    @Schema(description = "備註", example = "快樂的備註")
    private String remark;

}