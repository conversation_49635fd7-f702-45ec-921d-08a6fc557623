package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理後臺 - 用戶更新狀態 Request VO")
@Data
public class UserUpdateStatusReqVO {

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "角色編號不能爲空")
    private Long id;

    @Schema(description = "狀態，見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "修改狀態必須是 {value}")
    private Integer status;

}
