package cn.iocoder.yudao.module.system.controller.admin.notify.vo.template;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理後臺 - 站內信模版創建/修改 Request VO")
@Data
public class NotifyTemplateSaveReqVO {

    @Schema(description = "ID", example = "1024")
    private Long id;

    @Schema(description = "模版名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試模版")
    @NotEmpty(message = "模版名稱不能爲空")
    private String name;

    @Schema(description = "模版編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "SEND_TEST")
    @NotNull(message = "模版編碼不能爲空")
    private String code;

    @Schema(description = "模版類型，對應 system_notify_template_type 字典", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "模版類型不能爲空")
    private Integer type;

    @Schema(description = "發送人名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "土豆")
    @NotEmpty(message = "發送人名稱不能爲空")
    private String nickname;

    @Schema(description = "模版內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是模版內容")
    @NotEmpty(message = "模版內容不能爲空")
    private String content;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "狀態必須是 {value}")
    private Integer status;

    @Schema(description = "備註", example = "我是備註")
    private String remark;

}
