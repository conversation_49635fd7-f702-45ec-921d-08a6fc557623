package cn.iocoder.yudao.module.system.dal.dataobject.oauth2;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OAuth2 刷新令牌
 *
 * <AUTHOR>
 */
@TableName(value = "system_oauth2_refresh_token", autoResultMap = true)
// 由於 Oracle 的 SEQ 的名字長度有限制，所以就先用 system_oauth2_access_token_seq 吧，反正也沒啥問題
@KeySequence("system_oauth2_access_token_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OAuth2RefreshTokenDO extends TenantBaseDO {

    /**
     * 編號，數據庫字典
     */
    private Long id;
    /**
     * 刷新令牌
     */
    private String refreshToken;
    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 用戶類型
     *
     * 枚舉 {@link UserTypeEnum}
     */
    private Integer userType;
    /**
     * 客戶端編號
     *
     * 關聯 {@link OAuth2ClientDO#getId()}
     */
    private String clientId;
    /**
     * 授權範圍
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> scopes;
    /**
     * 過期時間
     */
    private LocalDateTime expiresTime;

}
