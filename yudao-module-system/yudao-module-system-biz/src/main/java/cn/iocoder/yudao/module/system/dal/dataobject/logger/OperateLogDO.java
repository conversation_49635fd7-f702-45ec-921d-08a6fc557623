package cn.iocoder.yudao.module.system.dal.dataobject.logger;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 操作日誌表
 *
 * <AUTHOR>
 */
@TableName(value = "system_operate_log", autoResultMap = true)
@KeySequence("system_operate_log_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
public class OperateLogDO extends BaseDO {

    /**
     * 日誌主鍵
     */
    @TableId
    private Long id;
    /**
     * 鏈路追蹤編號
     *
     * 一般來說，通過鏈路追蹤編號，可以將訪問日誌，錯誤日誌，鏈路追蹤日誌，logger 打印日誌等，結合在一起，從而進行排錯。
     */
    private String traceId;
    /**
     * 用戶編號
     *
     * 關聯 MemberUserDO 的 id 屬性，或者 AdminUserDO 的 id 屬性
     */
    private Long userId;
    /**
     * 用戶類型
     *
     * 關聯 {@link  UserTypeEnum}
     */
    private Integer userType;
    /**
     * 操作模塊類型
     */
    private String type;
    /**
     * 操作名
     */
    private String subType;
    /**
     * 操作模塊業務編號
     */
    private Long bizId;
    /**
     * 日誌內容，記錄整個操作的明細
     *
     * 例如說，修改編號爲 1 的用戶信息，將性別從男改成女，將姓名從芋道改成源碼。
     */
    private String action;
    /**
     * 拓展字段，有些複雜的業務，需要記錄一些字段 ( JSON 格式 )
     *
     * 例如說，記錄訂單編號，{ orderId: "1"}
     */
    private String extra;

    /**
     * 請求方法名
     */
    private String requestMethod;
    /**
     * 請求地址
     */
    private String requestUrl;
    /**
     * 用戶 IP
     */
    private String userIp;
    /**
     * 瀏覽器 UA
     */
    private String userAgent;

}
