package cn.iocoder.yudao.module.system.controller.admin.oauth2;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.http.HttpUtils;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.open.OAuth2OpenAccessTokenRespVO;
import cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.open.OAuth2OpenAuthorizeInfoRespVO;
import cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.open.OAuth2OpenCheckTokenRespVO;
import cn.iocoder.yudao.module.system.convert.oauth2.OAuth2OpenConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ApproveDO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2ClientDO;
import cn.iocoder.yudao.module.system.enums.oauth2.OAuth2GrantTypeEnum;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2ApproveService;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2ClientService;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2GrantService;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2TokenService;
import cn.iocoder.yudao.module.system.util.oauth2.OAuth2Utils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 提供給外部應用調用爲主
 *
 * 一般來說，管理後臺的 /system-api/* 是不直接提供給外部應用使用，主要是外部應用能夠訪問的數據與接口是有限的，而管理後臺的 RBAC 無法很好的控制。
 * 參考大量的開放平臺，都是獨立的一套 OpenAPI，對應到【本系統】就是在 Controller 下新建 open 包，實現 /open-api/* 接口，然後通過 scope 進行控制。
 * 另外，一個公司如果有多個管理後臺，它們 client_id 產生的 access token 相互之間是無法互通的，即無法訪問它們系統的 API 接口，直到兩個 client_id 產生信任授權。
 *
 * 考慮到【本系統】暫時不想做的過於複雜，默認只有獲取到 access token 之後，可以訪問【本系統】管理後臺的 /system-api/* 所有接口，除非手動添加 scope 控制。
 * scope 的使用示例，可見 {@link OAuth2UserController} 類
 *
 * <AUTHOR>
 */
@Tag(name = "管理後臺 - OAuth2.0 授權")
@RestController
@RequestMapping("/system/oauth2")
@Validated
@Slf4j
public class OAuth2OpenController {

    @Resource
    private OAuth2GrantService oauth2GrantService;
    @Resource
    private OAuth2ClientService oauth2ClientService;
    @Resource
    private OAuth2ApproveService oauth2ApproveService;
    @Resource
    private OAuth2TokenService oauth2TokenService;

    /**
     * 對應 Spring Security OAuth 的 TokenEndpoint 類的 postAccessToken 方法
     *
     * 授權碼 authorization_code 模式時：code + redirectUri + state 參數
     * 密碼 password 模式時：username + password + scope 參數
     * 刷新 refresh_token 模式時：refreshToken 參數
     * 客戶端 client_credentials 模式：scope 參數
     * 簡化 implicit 模式時：不支持
     *
     * 注意，默認需要傳遞 client_id + client_secret 參數
     */
    @PostMapping("/token")
    @PermitAll
    @Operation(summary = "獲得訪問令牌", description = "適合 code 授權碼模式，或者 implicit 簡化模式；在 sso.vue 單點登錄界面被【獲取】調用")
    @Parameters({
            @Parameter(name = "grant_type", required = true, description = "授權類型", example = "code"),
            @Parameter(name = "code", description = "授權範圍", example = "userinfo.read"),
            @Parameter(name = "redirect_uri", description = "重定向 URI", example = "https://www.iocoder.cn"),
            @Parameter(name = "state", description = "狀態", example = "1"),
            @Parameter(name = "username", example = "tudou"),
            @Parameter(name = "password", example = "cai"), // 多個使用空格分隔
            @Parameter(name = "scope", example = "user_info"),
            @Parameter(name = "refresh_token", example = "123424233"),
    })
    public CommonResult<OAuth2OpenAccessTokenRespVO> postAccessToken(HttpServletRequest request,
                                                                     @RequestParam("grant_type") String grantType,
                                                                     @RequestParam(value = "code", required = false) String code, // 授權碼模式
                                                                     @RequestParam(value = "redirect_uri", required = false) String redirectUri, // 授權碼模式
                                                                     @RequestParam(value = "state", required = false) String state, // 授權碼模式
                                                                     @RequestParam(value = "username", required = false) String username, // 密碼模式
                                                                     @RequestParam(value = "password", required = false) String password, // 密碼模式
                                                                     @RequestParam(value = "scope", required = false) String scope, // 密碼模式
                                                                     @RequestParam(value = "refresh_token", required = false) String refreshToken) { // 刷新模式
        List<String> scopes = OAuth2Utils.buildScopes(scope);
        // 1.1 校驗授權類型
        OAuth2GrantTypeEnum grantTypeEnum = OAuth2GrantTypeEnum.getByGrantType(grantType);
        if (grantTypeEnum == null) {
            throw exception0(BAD_REQUEST.getCode(), StrUtil.format("未知授權類型({})", grantType));
        }
        if (grantTypeEnum == OAuth2GrantTypeEnum.IMPLICIT) {
            throw exception0(BAD_REQUEST.getCode(), "Token 接口不支持 implicit 授權模式");
        }

        // 1.2 校驗客戶端
        String[] clientIdAndSecret = obtainBasicAuthorization(request);
        OAuth2ClientDO client = oauth2ClientService.validOAuthClientFromCache(clientIdAndSecret[0], clientIdAndSecret[1],
                grantType, scopes, redirectUri);

        // 2. 根據授權模式，獲取訪問令牌
        OAuth2AccessTokenDO accessTokenDO;
        switch (grantTypeEnum) {
            case AUTHORIZATION_CODE:
                accessTokenDO = oauth2GrantService.grantAuthorizationCodeForAccessToken(client.getClientId(), code, redirectUri, state);
                break;
            case PASSWORD:
                accessTokenDO = oauth2GrantService.grantPassword(username, password, client.getClientId(), scopes);
                break;
            case CLIENT_CREDENTIALS:
                accessTokenDO = oauth2GrantService.grantClientCredentials(client.getClientId(), scopes);
                break;
            case REFRESH_TOKEN:
                accessTokenDO = oauth2GrantService.grantRefreshToken(refreshToken, client.getClientId());
                break;
            default:
                throw new IllegalArgumentException("未知授權類型：" + grantType);
        }
        Assert.notNull(accessTokenDO, "訪問令牌不能爲空"); // 防禦性檢查
        return success(OAuth2OpenConvert.INSTANCE.convert(accessTokenDO));
    }

    @DeleteMapping("/token")
    @PermitAll
    @Operation(summary = "刪除訪問令牌")
    @Parameter(name = "token", required = true, description = "訪問令牌", example = "biu")
    public CommonResult<Boolean> revokeToken(HttpServletRequest request,
                                             @RequestParam("token") String token) {
        // 校驗客戶端
        String[] clientIdAndSecret = obtainBasicAuthorization(request);
        OAuth2ClientDO client = oauth2ClientService.validOAuthClientFromCache(clientIdAndSecret[0], clientIdAndSecret[1],
                null, null, null);

        // 刪除訪問令牌
        return success(oauth2GrantService.revokeToken(client.getClientId(), token));
    }

    /**
     * 對應 Spring Security OAuth 的 CheckTokenEndpoint 類的 checkToken 方法
     */
    @PostMapping("/check-token")
    @PermitAll
    @Operation(summary = "校驗訪問令牌")
    @Parameter(name = "token", required = true, description = "訪問令牌", example = "biu")
    public CommonResult<OAuth2OpenCheckTokenRespVO> checkToken(HttpServletRequest request,
                                                               @RequestParam("token") String token) {
        // 校驗客戶端
        String[] clientIdAndSecret = obtainBasicAuthorization(request);
        oauth2ClientService.validOAuthClientFromCache(clientIdAndSecret[0], clientIdAndSecret[1],
                null, null, null);

        // 校驗令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2TokenService.checkAccessToken(token);
        Assert.notNull(accessTokenDO, "訪問令牌不能爲空"); // 防禦性檢查
        return success(OAuth2OpenConvert.INSTANCE.convert2(accessTokenDO));
    }

    /**
     * 對應 Spring Security OAuth 的 AuthorizationEndpoint 類的 authorize 方法
     */
    @GetMapping("/authorize")
    @Operation(summary = "獲得授權信息", description = "適合 code 授權碼模式，或者 implicit 簡化模式；在 sso.vue 單點登錄界面被【獲取】調用")
    @Parameter(name = "clientId", required = true, description = "客戶端編號", example = "tudou")
    public CommonResult<OAuth2OpenAuthorizeInfoRespVO> authorize(@RequestParam("clientId") String clientId) {
        // 0. 校驗用戶已經登錄。通過 Spring Security 實現

        // 1. 獲得 Client 客戶端的信息
        OAuth2ClientDO client = oauth2ClientService.validOAuthClientFromCache(clientId);
        // 2. 獲得用戶已經授權的信息
        List<OAuth2ApproveDO> approves = oauth2ApproveService.getApproveList(getLoginUserId(), getUserType(), clientId);
        // 拼接返回
        return success(OAuth2OpenConvert.INSTANCE.convert(client, approves));
    }

    /**
     * 對應 Spring Security OAuth 的 AuthorizationEndpoint 類的 approveOrDeny 方法
     *
     * 場景一：【自動授權 autoApprove = true】
     *      剛進入 sso.vue 界面，調用該接口，用戶歷史已經給該應用做過對應的授權，或者 OAuth2Client 支持該 scope 的自動授權
     * 場景二：【手動授權 autoApprove = false】
     *      在 sso.vue 界面，用戶選擇好 scope 授權範圍，調用該接口，進行授權。此時，approved 爲 true 或者 false
     *
     * 因爲前後端分離，Axios 無法很好的處理 302 重定向，所以和 Spring Security OAuth 略有不同，返回結果是重定向的 URL，剩餘交給前端處理
     */
    @PostMapping("/authorize")
    @Operation(summary = "申請授權", description = "適合 code 授權碼模式，或者 implicit 簡化模式；在 sso.vue 單點登錄界面被【提交】調用")
    @Parameters({
            @Parameter(name = "response_type", required = true, description = "響應類型", example = "code"),
            @Parameter(name = "client_id", required = true, description = "客戶端編號", example = "tudou"),
            @Parameter(name = "scope", description = "授權範圍", example = "userinfo.read"), // 使用 Map<String, Boolean> 格式，Spring MVC 暫時不支持這麼接收參數
            @Parameter(name = "redirect_uri", required = true, description = "重定向 URI", example = "https://www.iocoder.cn"),
            @Parameter(name = "auto_approve", required = true, description = "用戶是否接受", example = "true"),
            @Parameter(name = "state", example = "1")
    })
    public CommonResult<String> approveOrDeny(@RequestParam("response_type") String responseType,
                                              @RequestParam("client_id") String clientId,
                                              @RequestParam(value = "scope", required = false) String scope,
                                              @RequestParam("redirect_uri") String redirectUri,
                                              @RequestParam(value = "auto_approve") Boolean autoApprove,
                                              @RequestParam(value = "state", required = false) String state) {
        @SuppressWarnings("unchecked")
        Map<String, Boolean> scopes = JsonUtils.parseObject(scope, Map.class);
        scopes = ObjectUtil.defaultIfNull(scopes, Collections.emptyMap());
        // 0. 校驗用戶已經登錄。通過 Spring Security 實現

        // 1.1 校驗 responseType 是否滿足 code 或者 token 值
        OAuth2GrantTypeEnum grantTypeEnum = getGrantTypeEnum(responseType);
        // 1.2 校驗 redirectUri 重定向域名是否合法 + 校驗 scope 是否在 Client 授權範圍內
        OAuth2ClientDO client = oauth2ClientService.validOAuthClientFromCache(clientId, null,
                grantTypeEnum.getGrantType(), scopes.keySet(), redirectUri);

        // 2.1 假設 approved 爲 null，說明是場景一
        if (Boolean.TRUE.equals(autoApprove)) {
            // 如果無法自動授權通過，則返回空 url，前端不進行跳轉
            if (!oauth2ApproveService.checkForPreApproval(getLoginUserId(), getUserType(), clientId, scopes.keySet())) {
                return success(null);
            }
        } else { // 2.2 假設 approved 非 null，說明是場景二
            // 如果計算後不通過，則跳轉一個錯誤鏈接
            if (!oauth2ApproveService.updateAfterApproval(getLoginUserId(), getUserType(), clientId, scopes)) {
                return success(OAuth2Utils.buildUnsuccessfulRedirect(redirectUri, responseType, state,
                        "access_denied", "User denied access"));
            }
        }

        // 3.1 如果是 code 授權碼模式，則發放 code 授權碼，並重定向
        List<String> approveScopes = convertList(scopes.entrySet(), Map.Entry::getKey, Map.Entry::getValue);
        if (grantTypeEnum == OAuth2GrantTypeEnum.AUTHORIZATION_CODE) {
            return success(getAuthorizationCodeRedirect(getLoginUserId(), client, approveScopes, redirectUri, state));
        }
        // 3.2 如果是 token 則是 implicit 簡化模式，則發送 accessToken 訪問令牌，並重定向
        return success(getImplicitGrantRedirect(getLoginUserId(), client, approveScopes, redirectUri, state));
    }

    private static OAuth2GrantTypeEnum getGrantTypeEnum(String responseType) {
        if (StrUtil.equals(responseType, "code")) {
            return OAuth2GrantTypeEnum.AUTHORIZATION_CODE;
        }
        if (StrUtil.equalsAny(responseType, "token")) {
            return OAuth2GrantTypeEnum.IMPLICIT;
        }
        throw exception0(BAD_REQUEST.getCode(), "response_type 參數值只允許 code 和 token");
    }

    private String getImplicitGrantRedirect(Long userId, OAuth2ClientDO client,
                                            List<String> scopes, String redirectUri, String state) {
        // 1. 創建 access token 訪問令牌
        OAuth2AccessTokenDO accessTokenDO = oauth2GrantService.grantImplicit(userId, getUserType(), client.getClientId(), scopes);
        Assert.notNull(accessTokenDO, "訪問令牌不能爲空"); // 防禦性檢查
        // 2. 拼接重定向的 URL
        // noinspection unchecked
        return OAuth2Utils.buildImplicitRedirectUri(redirectUri, accessTokenDO.getAccessToken(), state, accessTokenDO.getExpiresTime(),
                scopes, JsonUtils.parseObject(client.getAdditionalInformation(), Map.class));
    }

    private String getAuthorizationCodeRedirect(Long userId, OAuth2ClientDO client,
                                                List<String> scopes, String redirectUri, String state) {
        // 1. 創建 code 授權碼
        String authorizationCode = oauth2GrantService.grantAuthorizationCodeForCode(userId, getUserType(), client.getClientId(), scopes,
                redirectUri, state);
        // 2. 拼接重定向的 URL
        return OAuth2Utils.buildAuthorizationCodeRedirectUri(redirectUri, authorizationCode, state);
    }

    private Integer getUserType() {
        return UserTypeEnum.ADMIN.getValue();
    }

    private String[] obtainBasicAuthorization(HttpServletRequest request) {
        String[] clientIdAndSecret = HttpUtils.obtainBasicAuthorization(request);
        if (ArrayUtil.isEmpty(clientIdAndSecret) || clientIdAndSecret.length != 2) {
            throw exception0(BAD_REQUEST.getCode(), "client_id 或 client_secret 未正確傳遞");
        }
        return clientIdAndSecret;
    }

}
