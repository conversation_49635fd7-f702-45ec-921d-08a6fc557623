package cn.iocoder.yudao.module.system.controller.admin.sms.vo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 短信渠道精簡 Response VO")
@Data
public class SmsChannelSimpleRespVO {

    @Schema(description = "編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "短信簽名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道源碼")
    private String signature;

    @Schema(description = "渠道編碼，參見 SmsChannelEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "YUN_PIAN")
    private String code;

}
