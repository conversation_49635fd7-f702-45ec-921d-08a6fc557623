package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理後臺 - 驗證碼 Request VO")
@Data
public class CaptchaVerificationReqVO {

    // ========== 圖片驗證碼相關 ==========
    @Schema(description = "驗證碼，驗證碼開啓時，需要傳遞", requiredMode = Schema.RequiredMode.REQUIRED,
            example = "PfcH6mgr8tpXuMWFjvW6YVaqrswIuwmWI5dsVZSg7sGpWtDCUbHuDEXl3cFB1+VvCC/rAkSwK8Fad52FSuncVg==")
    @NotEmpty(message = "驗證碼不能爲空", groups = CodeEnableGroup.class)
    private String captchaVerification;

    /**
     * 開啓驗證碼的 Group
     */
    public interface CodeEnableGroup {
    }
}
