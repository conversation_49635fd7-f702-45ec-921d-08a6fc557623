package cn.iocoder.yudao.module.system.controller.admin.sms.vo.log;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.framework.excel.core.convert.JsonConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Schema(description = "管理後臺 - 短信日誌 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsLogRespVO {

    @Schema(description = "編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("編號")
    private Long id;

    @Schema(description = "短信渠道編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @ExcelProperty("短信渠道編號")
    private Long channelId;

    @Schema(description = "短信渠道編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "ALIYUN")
    @ExcelProperty("短信渠道編碼")
    private String channelCode;

    @Schema(description = "模板編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    @ExcelProperty("模板編號")
    private Long templateId;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test-01")
    @ExcelProperty("模板編碼")
    private String templateCode;

    @Schema(description = "短信類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "短信類型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SMS_TEMPLATE_TYPE)
    private Integer templateType;

    @Schema(description = "短信內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "你好，你的驗證碼是 1024")
    @ExcelProperty("短信內容")
    private String templateContent;

    @Schema(description = "短信參數", requiredMode = Schema.RequiredMode.REQUIRED, example = "name,code")
    @ExcelProperty(value = "短信參數", converter = JsonConvert.class)
    private Map<String, Object> templateParams;

    @Schema(description = "短信 API 的模板編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "SMS_207945135")
    @ExcelProperty("短信 API 的模板編號")
    private String apiTemplateId;

    @Schema(description = "手機號", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    @ExcelProperty("手機號")
    private String mobile;

    @Schema(description = "用戶編號", example = "10")
    @ExcelProperty("用戶編號")
    private Long userId;

    @Schema(description = "用戶類型", example = "1")
    @ExcelProperty(value = "用戶類型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_TYPE)
    private Integer userType;

    @Schema(description = "發送狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "發送狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SMS_SEND_STATUS)
    private Integer sendStatus;

    @Schema(description = "發送時間")
    @ExcelProperty("發送時間")
    private LocalDateTime sendTime;

    @Schema(description = "短信 API 發送結果的編碼", example = "SUCCESS")
    @ExcelProperty("短信 API 發送結果的編碼")
    private String apiSendCode;

    @Schema(description = "短信 API 發送失敗的提示", example = "成功")
    @ExcelProperty("短信 API 發送失敗的提示")
    private String apiSendMsg;

    @Schema(description = "短信 API 發送返回的唯一請求 ID", example = "3837C6D3-B96F-428C-BBB2-86135D4B5B99")
    @ExcelProperty("短信 API 發送返回的唯一請求 ID")
    private String apiRequestId;

    @Schema(description = "短信 API 發送返回的序號", example = "62923244790")
    @ExcelProperty("短信 API 發送返回的序號")
    private String apiSerialNo;

    @Schema(description = "接收狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty(value = "接收狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SMS_RECEIVE_STATUS)
    private Integer receiveStatus;

    @Schema(description = "接收時間")
    @ExcelProperty("接收時間")
    private LocalDateTime receiveTime;

    @Schema(description = "API 接收結果的編碼", example = "DELIVRD")
    @ExcelProperty("API 接收結果的編碼")
    private String apiReceiveCode;

    @Schema(description = "API 接收結果的說明", example = "用戶接收成功")
    @ExcelProperty("API 接收結果的說明")
    private String apiReceiveMsg;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("創建時間")
    private LocalDateTime createTime;

}
