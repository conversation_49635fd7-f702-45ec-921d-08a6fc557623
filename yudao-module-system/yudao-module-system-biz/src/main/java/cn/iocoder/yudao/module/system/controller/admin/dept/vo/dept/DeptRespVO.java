package cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 部門信息 Response VO")
@Data
public class DeptRespVO {

    @Schema(description = "部門編號", example = "1024")
    private Long id;

    @Schema(description = "部門名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

    @Schema(description = "父部門 ID", example = "1024")
    private Long parentId;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Integer sort;

    @Schema(description = "負責人的用戶編號", example = "2048")
    private Long leaderUserId;

    @Schema(description = "聯繫電話", example = "15601691000")
    private String phone;

    @Schema(description = "郵箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "狀態,見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    private LocalDateTime createTime;

}
