package cn.iocoder.yudao.module.system.controller.admin.notice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 通知公告創建/修改 Request VO")
@Data
public class NoticeSaveReqVO {

    @Schema(description = "崗位公告編號", example = "1024")
    private Long id;

    @Schema(description = "公告標題", requiredMode = Schema.RequiredMode.REQUIRED, example = "小博主")
    @NotBlank(message = "公告標題不能爲空")
    @Size(max = 50, message = "公告標題不能超過50個字符")
    private String title;

    @Schema(description = "公告類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "小博主")
    @NotNull(message = "公告類型不能爲空")
    private Integer type;

    @Schema(description = "公告內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "半生編碼")
    private String content;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

}
