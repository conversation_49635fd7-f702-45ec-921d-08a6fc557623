package cn.iocoder.yudao.module.system.controller.admin.dept.vo.post;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 崗位信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PostRespVO {

    @Schema(description = "崗位序號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("崗位序號")
    private Long id;

    @Schema(description = "崗位名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "小土豆")
    @ExcelProperty("崗位名稱")
    private String name;

    @Schema(description = "崗位編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @ExcelProperty("崗位編碼")
    private String code;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("崗位排序")
    private Integer sort;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "備註", example = "快樂的備註")
    private String remark;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
