package cn.iocoder.yudao.module.system.dal.dataobject.dept;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 崗位表
 *
 * <AUTHOR>
 */
@TableName("system_post")
@KeySequence("system_post_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class PostDO extends BaseDO {

    /**
     * 崗位序號
     */
    @TableId
    private Long id;
    /**
     * 崗位名稱
     */
    private String name;
    /**
     * 崗位編碼
     */
    private String code;
    /**
     * 崗位排序
     */
    private Integer sort;
    /**
     * 狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 備註
     */
    private String remark;

}
