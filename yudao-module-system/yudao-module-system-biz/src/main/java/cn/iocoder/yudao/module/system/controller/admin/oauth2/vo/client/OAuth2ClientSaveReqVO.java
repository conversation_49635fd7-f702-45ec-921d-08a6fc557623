package cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.client;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理後臺 - OAuth2 客戶端創建/修改 Request VO")
@Data
public class OAuth2ClientSaveReqVO {

    @Schema(description = "編號", example = "1024")
    private Long id;

    @Schema(description = "客戶端編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "tudou")
    @NotNull(message = "客戶端編號不能爲空")
    private String clientId;

    @Schema(description = "客戶端密鑰", requiredMode = Schema.RequiredMode.REQUIRED, example = "fan")
    @NotNull(message = "客戶端密鑰不能爲空")
    private String secret;

    @Schema(description = "應用名", requiredMode = Schema.RequiredMode.REQUIRED, example = "土豆")
    @NotNull(message = "應用名不能爲空")
    private String name;

    @Schema(description = "應用圖標", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn/xx.png")
    @NotNull(message = "應用圖標不能爲空")
    @URL(message = "應用圖標的地址不正確")
    private String logo;

    @Schema(description = "應用描述", example = "我是一個應用")
    private String description;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    private Integer status;

    @Schema(description = "訪問令牌的有效期", requiredMode = Schema.RequiredMode.REQUIRED, example = "8640")
    @NotNull(message = "訪問令牌的有效期不能爲空")
    private Integer accessTokenValiditySeconds;

    @Schema(description = "刷新令牌的有效期", requiredMode = Schema.RequiredMode.REQUIRED, example = "8640000")
    @NotNull(message = "刷新令牌的有效期不能爲空")
    private Integer refreshTokenValiditySeconds;

    @Schema(description = "可重定向的 URI 地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotNull(message = "可重定向的 URI 地址不能爲空")
    private List<@NotEmpty(message = "重定向的 URI 不能爲空") @URL(message = "重定向的 URI 格式不正確") String> redirectUris;

    @Schema(description = "授權類型，參見 OAuth2GrantTypeEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "password")
    @NotNull(message = "授權類型不能爲空")
    private List<String> authorizedGrantTypes;

    @Schema(description = "授權範圍", example = "user_info")
    private List<String> scopes;

    @Schema(description = "自動通過的授權範圍", example = "user_info")
    private List<String> autoApproveScopes;

    @Schema(description = "權限", example = "system:user:query")
    private List<String> authorities;

    @Schema(description = "資源", example = "1024")
    private List<String> resourceIds;

    @Schema(description = "附加信息", example = "{yunai: true}")
    private String additionalInformation;

    @AssertTrue(message = "附加信息必須是 JSON 格式")
    public boolean isAdditionalInformationJson() {
        return StrUtil.isEmpty(additionalInformation) || JsonUtils.isJson(additionalInformation);
    }

}
