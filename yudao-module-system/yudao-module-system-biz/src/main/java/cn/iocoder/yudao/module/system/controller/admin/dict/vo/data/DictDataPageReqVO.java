package cn.iocoder.yudao.module.system.controller.admin.dict.vo.data;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 字典類型分頁列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class DictDataPageReqVO extends PageParam {

    @Schema(description = "字典標籤", example = "芋道")
    @Size(max = 100, message = "字典標籤長度不能超過100個字符")
    private String label;

    @Schema(description = "字典類型，模糊匹配", example = "sys_common_sex")
    @Size(max = 100, message = "字典類型類型長度不能超過100個字符")
    private String dictType;

    @Schema(description = "展示狀態，參見 CommonStatusEnum 枚舉類", example = "1")
    @InEnum(value = CommonStatusEnum.class, message = "修改狀態必須是 {value}")
    private Integer status;

}
