package cn.iocoder.yudao.module.system.controller.admin.mail.vo.log;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理後臺 - 郵箱日誌分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MailLogPageReqVO extends PageParam {

    @Schema(description = "用戶編號", example = "30883")
    private Long userId;

    @Schema(description = "用戶類型，參見 UserTypeEnum 枚舉", example = "2")
    private Integer userType;

    @Schema(description = "接收郵箱地址，模糊匹配", example = "<EMAIL>")
    private String toMail;

    @Schema(description = "郵箱賬號編號", example = "18107")
    private Long accountId;

    @Schema(description = "模板編號", example = "5678")
    private Long templateId;

    @Schema(description = "發送狀態，參見 MailSendStatusEnum 枚舉", example = "1")
    private Integer sendStatus;

    @Schema(description = "發送時間")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sendTime;

}
