package cn.iocoder.yudao.module.system.dal.dataobject.logger;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.enums.logger.LoginResultEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 登錄日誌表
 *
 * 注意，包括登錄和登出兩種行爲
 *
 * <AUTHOR>
 */
@TableName("system_login_log")
@KeySequence("system_login_log_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LoginLogDO extends BaseDO {

    /**
     * 日誌主鍵
     */
    private Long id;
    /**
     * 日誌類型
     *
     * 枚舉 {@link LoginLogTypeEnum}
     */
    private Integer logType;
    /**
     * 鏈路追蹤編號
     */
    private String traceId;
    /**
     * 用戶編號
     */
    private Long userId;
    /**
     * 用戶類型
     *
     * 枚舉 {@link UserTypeEnum}
     */
    private Integer userType;
    /**
     * 用戶賬號
     *
     * 冗餘，因爲賬號可以變更
     */
    private String username;
    /**
     * 登錄結果
     *
     * 枚舉 {@link LoginResultEnum}
     */
    private Integer result;
    /**
     * 用戶 IP
     */
    private String userIp;
    /**
     * 瀏覽器 UA
     */
    private String userAgent;

}
