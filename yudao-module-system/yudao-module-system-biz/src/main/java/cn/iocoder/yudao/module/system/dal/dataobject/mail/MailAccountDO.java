package cn.iocoder.yudao.module.system.dal.dataobject.mail;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 郵箱賬號 DO
 *
 * 用途：配置發送郵箱的賬號
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@TableName(value = "system_mail_account", autoResultMap = true)
@KeySequence("system_mail_account_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class MailAccountDO extends BaseDO {

    /**
     * 主鍵
     */
    @TableId
    private Long id;
    /**
     * 郵箱
     */
    private String mail;

    /**
     * 用戶名
     */
    private String username;
    /**
     * 密碼
     */
    private String password;
    /**
     * SMTP 服務器域名
     */
    private String host;
    /**
     * SMTP 服務器端口
     */
    private Integer port;
    /**
     * 是否開啓 SSL
     */
    private Boolean sslEnable;
    /**
     * 是否開啓 STARTTLS
     */
    private Boolean starttlsEnable;

}
