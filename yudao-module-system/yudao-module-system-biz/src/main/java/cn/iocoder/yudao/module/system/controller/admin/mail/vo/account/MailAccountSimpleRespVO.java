package cn.iocoder.yudao.module.system.controller.admin.mail.vo.account;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 郵箱賬號的精簡 Response VO")
@Data
public class MailAccountSimpleRespVO {

    @Schema(description = "郵箱編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "郵箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String mail;

}
