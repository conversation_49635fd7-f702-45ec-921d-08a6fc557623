package cn.iocoder.yudao.module.system.dal.dataobject.dict;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 字典類型表
 *
 * <AUTHOR>
 */
@TableName("system_dict_type")
@KeySequence("system_dict_type_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DictTypeDO extends BaseDO {

    /**
     * 字典主鍵
     */
    @TableId
    private Long id;
    /**
     * 字典名稱
     */
    private String name;
    /**
     * 字典類型
     */
    private String type;
    /**
     * 狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 備註
     */
    private String remark;

    /**
     * 刪除時間
     */
    private LocalDateTime deletedTime;

}
