package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理後臺 - 用戶精簡信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSimpleRespVO {

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String nickname;

    @Schema(description = "部門ID", example = "我是一個用戶")
    private Long deptId;
    @Schema(description = "部門名稱", example = "IT 部")
    private String deptName;

    @Schema(description = "头像地址", example = "http://test.yudao.iocoder.cn/xxx.png")
    private String avatar;

}
