package cn.iocoder.yudao.module.system.controller.admin.mail.vo.account;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;

@Schema(description = "管理後臺 - 郵箱賬號創建/修改 Request VO")
@Data
public class MailAccountSaveReqVO {

    @Schema(description = "編號", example = "1024")
    private Long id;

    @Schema(description = "郵箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @NotNull(message = "郵箱不能爲空")
    @Email(message = "必須是 Email 格式")
    private String mail;

    @Schema(description = "用戶名", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @NotNull(message = "用戶名不能爲空")
    private String username;

    @Schema(description = "密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotNull(message = "密碼必填")
    private String password;

    @Schema(description = "SMTP 服務器域名", requiredMode = Schema.RequiredMode.REQUIRED, example = "www.iocoder.cn")
    @NotNull(message = "SMTP 服務器域名不能爲空")
    private String host;

    @Schema(description = "SMTP 服務器端口", requiredMode = Schema.RequiredMode.REQUIRED, example = "80")
    @NotNull(message = "SMTP 服務器端口不能爲空")
    private Integer port;

    @Schema(description = "是否開啓 ssl", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否開啓 ssl 必填")
    private Boolean sslEnable;

    @Schema(description = "是否開啓 starttls", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @NotNull(message = "是否開啓 starttls 必填")
    private Boolean starttlsEnable;

}
