package cn.iocoder.yudao.module.system.controller.admin.mail.vo.account;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理後臺 - 郵箱賬號分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MailAccountPageReqVO extends PageParam {

    @Schema(description = "郵箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String mail;

    @Schema(description = "用戶名" , requiredMode = Schema.RequiredMode.REQUIRED , example = "yudao")
    private String username;

}
