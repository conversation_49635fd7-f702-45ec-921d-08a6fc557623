package cn.iocoder.yudao.module.system.dal.dataobject.notice;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.system.enums.notice.NoticeTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通知公告表
 *
 * <AUTHOR>
 */
@TableName("system_notice")
@KeySequence("system_notice_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticeDO extends BaseDO {

    /**
     * 公告ID
     */
    private Long id;
    /**
     * 公告標題
     */
    private String title;
    /**
     * 公告類型
     *
     * 枚舉 {@link NoticeTypeEnum}
     */
    private Integer type;
    /**
     * 公告內容
     */
    private String content;
    /**
     * 公告狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;

}
