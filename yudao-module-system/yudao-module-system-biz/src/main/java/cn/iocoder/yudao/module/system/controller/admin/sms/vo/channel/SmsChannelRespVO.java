package cn.iocoder.yudao.module.system.controller.admin.sms.vo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 短信渠道 Response VO")
@Data
public class SmsChannelRespVO {

    @Schema(description = "編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "短信簽名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道源碼")
    @NotNull(message = "短信簽名不能爲空")
    private String signature;

    @Schema(description = "渠道編碼，參見 SmsChannelEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "YUN_PIAN")
    private String code;

    @Schema(description = "啓用狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "啓用狀態不能爲空")
    private Integer status;

    @Schema(description = "備註", example = "好喫！")
    private String remark;

    @Schema(description = "短信 API 的賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @NotNull(message = "短信 API 的賬號不能爲空")
    private String apiKey;

    @Schema(description = "短信 API 的密鑰", example = "yuanma")
    private String apiSecret;

    @Schema(description = "短信發送回調 URL", example = "https://www.iocoder.cn")
    @URL(message = "回調 URL 格式不正確")
    private String callbackUrl;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
