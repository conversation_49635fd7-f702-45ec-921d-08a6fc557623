package cn.iocoder.yudao.module.system.dal.dataobject.dept;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部門表
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@TableName("system_dept")
@KeySequence("system_dept_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class DeptDO extends TenantBaseDO {

    public static final Long PARENT_ID_ROOT = 0L;

    /**
     * 部門ID
     */
    @TableId
    private Long id;
    /**
     * 部門名稱
     */
    private String name;
    /**
     * 父部門ID
     *
     * 關聯 {@link #id}
     */
    private Long parentId;
    /**
     * 顯示順序
     */
    private Integer sort;
    /**
     * 負責人
     *
     * 關聯 {@link AdminUserDO#getId()}
     */
    private Long leaderUserId;
    /**
     * 聯繫電話
     */
    private String phone;
    /**
     * 郵箱
     */
    private String email;
    /**
     * 部門狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;

}
