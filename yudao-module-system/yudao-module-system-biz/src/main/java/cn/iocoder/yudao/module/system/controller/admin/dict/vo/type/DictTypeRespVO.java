package cn.iocoder.yudao.module.system.controller.admin.dict.vo.type;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 字典類型信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DictTypeRespVO {

    @Schema(description = "字典類型編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("字典主鍵")
    private Long id;

    @Schema(description = "字典名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "性別")
    @ExcelProperty("字典名稱")
    private String name;

    @Schema(description = "字典類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "sys_common_sex")
    @ExcelProperty("字典類型")
    private String type;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "備註", example = "快樂的備註")
    private String remark;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    private LocalDateTime createTime;

}
