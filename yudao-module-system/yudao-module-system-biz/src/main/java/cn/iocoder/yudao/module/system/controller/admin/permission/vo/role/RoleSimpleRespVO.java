package cn.iocoder.yudao.module.system.controller.admin.permission.vo.role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理後臺 - 角色精簡信息 Response VO")
@Data
public class RoleSimpleRespVO {

    @Schema(description = "角色編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "角色名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

}
