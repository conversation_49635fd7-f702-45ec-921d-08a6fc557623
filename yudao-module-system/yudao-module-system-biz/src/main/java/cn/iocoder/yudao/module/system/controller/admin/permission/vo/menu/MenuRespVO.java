package cn.iocoder.yudao.module.system.controller.admin.permission.vo.menu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 菜單信息 Response VO")
@Data
public class MenuRespVO {

    @Schema(description = "菜單編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "菜單名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotBlank(message = "菜單名稱不能爲空")
    @Size(max = 50, message = "菜單名稱長度不能超過50個字符")
    private String name;

    @Schema(description = "權限標識,僅菜單類型爲按鈕時，才需要傳遞", example = "sys:menu:add")
    @Size(max = 100)
    private String permission;

    @Schema(description = "類型，參見 MenuTypeEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "菜單類型不能爲空")
    private Integer type;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "顯示順序不能爲空")
    private Integer sort;

    @Schema(description = "父菜單 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "父菜單 ID 不能爲空")
    private Long parentId;

    @Schema(description = "路由地址,僅菜單類型爲菜單或者目錄時，才需要傳", example = "post")
    @Size(max = 200, message = "路由地址不能超過200個字符")
    private String path;

    @Schema(description = "菜單圖標,僅菜單類型爲菜單或者目錄時，才需要傳", example = "/menu/list")
    private String icon;

    @Schema(description = "組件路徑,僅菜單類型爲菜單時，才需要傳", example = "system/post/index")
    @Size(max = 200, message = "組件路徑不能超過255個字符")
    private String component;

    @Schema(description = "組件名", example = "SystemUser")
    private String componentName;

    @Schema(description = "狀態,見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    private Integer status;

    @Schema(description = "是否可見", example = "false")
    private Boolean visible;

    @Schema(description = "是否緩存", example = "false")
    private Boolean keepAlive;

    @Schema(description = "是否總是顯示", example = "false")
    private Boolean alwaysShow;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    private LocalDateTime createTime;

}
