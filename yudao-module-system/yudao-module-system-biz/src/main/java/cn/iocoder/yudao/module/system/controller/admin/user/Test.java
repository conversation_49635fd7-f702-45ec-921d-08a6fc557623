package cn.iocoder.yudao.module.system.controller.admin.user;

import java.util.regex.Pattern;

public class Test {
    public static void main(String[] args) {
        String regex = "^(?:(?:\\+|00)86)?1[3-9]\\d{9}$|^(?:(?:\\+|00)852)?[2-9]\\d{7}$";
        String input = "37557082";
        boolean matches = Pattern.matches(regex, input);
        System.out.println("Matches: " + matches); // 应输出 Matches: true
    }
}
