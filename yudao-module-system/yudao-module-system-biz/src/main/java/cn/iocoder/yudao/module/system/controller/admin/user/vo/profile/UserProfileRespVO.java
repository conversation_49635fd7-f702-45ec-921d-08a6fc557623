package cn.iocoder.yudao.module.system.controller.admin.user.vo.profile;

import cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept.DeptSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.dept.vo.post.PostSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RoleSimpleRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "管理後臺 - 用戶個人中心信息 Response VO")
public class UserProfileRespVO {

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    private String username;

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String nickname;

    @Schema(description = "用戶郵箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手機號碼", example = "15601691300")
    private String mobile;

    @Schema(description = "用戶性別，參見 SexEnum 枚舉類", example = "1")
    private Integer sex;

    @Schema(description = "用戶頭像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @Schema(description = "最後登錄 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    private String loginIp;

    @Schema(description = "最後登錄時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    private LocalDateTime loginDate;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    private LocalDateTime createTime;

    /**
     * 所屬角色
     */
    private List<RoleSimpleRespVO> roles;
    /**
     * 所在部門
     */
    private DeptSimpleRespVO dept;
    /**
     * 所屬崗位數組
     */
    private List<PostSimpleRespVO> posts;
    /**
     * 社交用戶數組
     */
    private List<SocialUser> socialUsers;

    @Schema(description = "社交用戶")
    @Data
    public static class SocialUser {

        @Schema(description = "社交平臺的類型，參見 SocialTypeEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
        private Integer type;

        @Schema(description = "社交用戶的 openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "IPRmJ0wvBptiPIlGEZiPewGwiEiE")
        private String openid;

    }

}
