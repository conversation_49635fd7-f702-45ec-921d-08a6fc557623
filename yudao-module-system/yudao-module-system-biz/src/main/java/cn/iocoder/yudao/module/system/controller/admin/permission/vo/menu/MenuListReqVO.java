package cn.iocoder.yudao.module.system.controller.admin.permission.vo.menu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 菜單列表 Request VO")
@Data
public class MenuListReqVO {

    @Schema(description = "菜單名稱，模糊匹配", example = "芋道")
    private String name;

    @Schema(description = "展示狀態，參見 CommonStatusEnum 枚舉類", example = "1")
    private Integer status;

}
