package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@Schema(description = "管理後臺 - 租戶套餐 Response VO")
@Data
public class TenantPackageRespVO {

    @Schema(description = "套餐編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "VIP")
    private String name;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "備註", example = "好")
    private String remark;

    @Schema(description = "關聯的菜單編號", requiredMode = Schema.RequiredMode.REQUIRED)
    private Set<Long> menuIds;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
