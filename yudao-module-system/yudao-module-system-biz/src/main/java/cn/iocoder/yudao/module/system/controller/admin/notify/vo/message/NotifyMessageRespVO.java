package cn.iocoder.yudao.module.system.controller.admin.notify.vo.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Schema(description = "管理後臺 - 站內信 Response VO")
@Data
public class NotifyMessageRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "25025")
    private Long userId;

    @Schema(description = "用戶類型，參見 UserTypeEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Byte userType;

    @Schema(description = "模版編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "13013")
    private Long templateId;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test_01")
    private String templateCode;

    @Schema(description = "模版發送人名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String templateNickname;

    @Schema(description = "模版內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試內容")
    private String templateContent;

    @Schema(description = "模版類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer templateType;

    @Schema(description = "模版參數", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, Object> templateParams;

    @Schema(description = "是否已讀", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean readStatus;

    @Schema(description = "閱讀時間")
    private LocalDateTime readTime;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
