package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@Schema(description = "管理後臺 - 用戶信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserRespVO{

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("用戶編號")
    private Long id;

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @ExcelProperty("用戶名稱")
    private String username;

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("用戶暱稱")
    private String nickname;

    @Schema(description = "備註", example = "我是一個用戶")
    private String remark;

    @Schema(description = "部門ID", example = "我是一個用戶")
    private Long deptId;
    @Schema(description = "部門名稱", example = "IT 部")
    @ExcelProperty("部門名稱")
    private String deptName;

    @Schema(description = "崗位編號數組", example = "1")
    private Set<Long> postIds;

    @Schema(description = "用戶郵箱", example = "<EMAIL>")
    @ExcelProperty("用戶郵箱")
    private String email;

    @Schema(description = "手機號碼", example = "15601691300")
    @ExcelProperty("手機號碼")
    private String mobile;

    @Schema(description = "用戶性別，參見 SexEnum 枚舉類", example = "1")
    @ExcelProperty(value = "用戶性別", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @Schema(description = "用戶頭像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "帳號狀態", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.COMMON_STATUS)
    private Integer status;

    @Schema(description = "最後登錄 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "***********")
    @ExcelProperty("最後登錄IP")
    private String loginIp;

    @Schema(description = "最後登錄時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    @ExcelProperty("最後登錄時間")
    private LocalDateTime loginDate;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED, example = "時間戳格式")
    private LocalDateTime createTime;

    @Schema(description = "上司", example = "1")
    @ExcelProperty("上司")
    private Long leaderId;

    @Schema(description = "是否允許導出數據", example = "true")
    private Integer allowExport;

}
