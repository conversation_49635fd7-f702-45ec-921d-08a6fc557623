package cn.iocoder.yudao.module.system.controller.admin.mail.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 郵件模版的精簡 Response VO")
@Data
public class MailTemplateSimpleRespVO {

    @Schema(description = "模版編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "模版名字", requiredMode = Schema.RequiredMode.REQUIRED, example = "噠噠噠")
    private String name;

}
