package cn.iocoder.yudao.module.system.controller.admin.dict.vo.data;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 字典數據創建/修改 Request VO")
@Data
public class DictDataSaveReqVO {

    @Schema(description = "字典數據編號", example = "1024")
    private Long id;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "顯示順序不能爲空")
    private Integer sort;

    @Schema(description = "字典標籤", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotBlank(message = "字典標籤不能爲空")
    @Size(max = 100, message = "字典標籤長度不能超過100個字符")
    private String label;

    @Schema(description = "字典值", requiredMode = Schema.RequiredMode.REQUIRED, example = "iocoder")
    @NotBlank(message = "字典鍵值不能爲空")
    @Size(max = 100, message = "字典鍵值長度不能超過100個字符")
    private String value;

    @Schema(description = "字典類型", requiredMode = Schema.RequiredMode.REQUIRED, example = "sys_common_sex")
    @NotBlank(message = "字典類型不能爲空")
    @Size(max = 100, message = "字典類型長度不能超過100個字符")
    private String dictType;

    @Schema(description = "狀態,見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "修改狀態必須是 {value}")
    private Integer status;

    @Schema(description = "顏色類型,default、primary、success、info、warning、danger", example = "default")
    private String colorType;

    @Schema(description = "css 樣式", example = "btn-visible")
    private String cssClass;

    @Schema(description = "備註", example = "我是一個角色")
    private String remark;

}
