package cn.iocoder.yudao.module.system.controller.admin.dept.vo.post;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "管理後臺 - 崗位分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class PostPageReqVO extends PageParam {

    @Schema(description = "崗位編碼，模糊匹配", example = "yudao")
    private String code;

    @Schema(description = "崗位名稱，模糊匹配", example = "芋道")
    private String name;

    @Schema(description = "展示狀態，參見 CommonStatusEnum 枚舉類", example = "1")
    private Integer status;

}
