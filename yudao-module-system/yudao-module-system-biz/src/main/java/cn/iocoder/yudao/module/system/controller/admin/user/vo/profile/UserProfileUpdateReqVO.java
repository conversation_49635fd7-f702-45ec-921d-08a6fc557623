package cn.iocoder.yudao.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;


@Schema(description = "管理後臺 - 用戶個人信息更新 Request VO")
@Data
public class UserProfileUpdateReqVO {

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @Size(max = 30, message = "用戶暱稱長度不能超過 30 個字符")
    private String nickname;

    @Schema(description = "用戶郵箱", example = "<EMAIL>")
    @Email(message = "郵箱格式不正確")
    @Size(max = 50, message = "郵箱長度不能超過 50 個字符")
    private String email;

    @Schema(description = "手機號碼", example = "15601691300")
    @Length(min = 11, max = 11, message = "手機號長度必須 11 位")
    private String mobile;

    @Schema(description = "用戶性別，參見 SexEnum 枚舉類", example = "1")
    private Integer sex;

}
