package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 租戶創建/修改 Request VO")
@Data
public class TenantSaveReqVO {

    @Schema(description = "租戶編號", example = "1024")
    private Long id;

    @Schema(description = "租戶名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotNull(message = "租戶名不能爲空")
    private String name;

    @Schema(description = "聯繫人", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotNull(message = "聯繫人不能爲空")
    private String contactName;

    @Schema(description = "聯繫手機", example = "15601691300")
    private String contactMobile;

    @Schema(description = "租戶狀態", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租戶狀態")
    private Integer status;

    @Schema(description = "綁定域名", example = "https://www.iocoder.cn")
    private String website;

    @Schema(description = "租戶套餐編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "租戶套餐編號不能爲空")
    private Long packageId;

    @Schema(description = "過期時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "過期時間不能爲空")
    private LocalDateTime expireTime;

    @Schema(description = "賬號數量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "賬號數量不能爲空")
    private Integer accountCount;

    // ========== 僅【創建】時，需要傳遞的字段 ==========

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用戶賬號由 數字、字母 組成")
    @Size(min = 4, max = 30, message = "用戶賬號長度爲 4-30 個字符")
    private String username;

    @Schema(description = "密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Length(min = 4, max = 16, message = "密碼長度爲 4-16 位")
    private String password;

    @AssertTrue(message = "用戶賬號、密碼不能爲空")
    @JsonIgnore
    public boolean isUsernameValid() {
        return id != null // 修改時，不需要傳遞
                || (ObjectUtil.isAllNotEmpty(username, password)); // 新增時，必須都傳遞 username、password
    }

}
