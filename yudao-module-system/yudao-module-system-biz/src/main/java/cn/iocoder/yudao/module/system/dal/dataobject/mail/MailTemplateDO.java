package cn.iocoder.yudao.module.system.dal.dataobject.mail;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 郵件模版 DO
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@TableName(value = "system_mail_template", autoResultMap = true)
@KeySequence("system_mail_template_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class MailTemplateDO extends BaseDO {

    /**
     * 主鍵
     */
    private Long id;
    /**
     * 模版名稱
     */
    private String name;
    /**
     * 模版編號
     */
    private String code;
    /**
     * 發送的郵箱賬號編號
     *
     * 關聯 {@link MailAccountDO#getId()}
     */
    private Long accountId;

    /**
     * 發送人名稱
     */
    private String nickname;
    /**
     * 標題
     */
    private String title;
    /**
     * 內容
     */
    private String content;
    /**
     * 參數數組(自動根據內容生成)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> params;
    /**
     * 狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 備註
     */
    private String remark;

}
