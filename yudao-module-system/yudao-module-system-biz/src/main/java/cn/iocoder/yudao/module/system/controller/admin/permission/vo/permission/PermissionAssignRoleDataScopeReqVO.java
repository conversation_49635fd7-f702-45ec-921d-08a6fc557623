package cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.module.system.enums.permission.DataScopeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.Set;

@Schema(description = "管理後臺 - 賦予角色數據權限 Request VO")
@Data
public class PermissionAssignRoleDataScopeReqVO {

    @Schema(description = "角色編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "角色編號不能爲空")
    private Long roleId;

    @Schema(description = "數據範圍，參見 DataScopeEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "數據範圍不能爲空")
    @InEnum(value = DataScopeEnum.class, message = "數據範圍必須是 {value}")
    private Integer dataScope;

    @Schema(description = "部門編號列表，只有範圍類型爲 DEPT_CUSTOM 時，該字段才需要", example = "1,3,5")
    private Set<Long> dataScopeDeptIds = Collections.emptySet(); // 兜底

}
