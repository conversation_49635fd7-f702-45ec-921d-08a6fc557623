package cn.iocoder.yudao.module.system.controller.admin.dept.vo.dept;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - 部門創建/修改 Request VO")
@Data
public class DeptSaveReqVO {

    @Schema(description = "部門編號", example = "1024")
    private Long id;

    @Schema(description = "部門名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    @NotBlank(message = "部門名稱不能爲空")
    @Size(max = 30, message = "部門名稱長度不能超過 30 個字符")
    private String name;

    @Schema(description = "父部門 ID", example = "1024")
    private Long parentId;

    @Schema(description = "顯示順序", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "顯示順序不能爲空")
    private Integer sort;

    @Schema(description = "負責人的用戶編號", example = "2048")
    private Long leaderUserId;

    @Schema(description = "聯繫電話", example = "15601691000")
    @Size(max = 11, message = "聯繫電話長度不能超過11個字符")
    private String phone;

    @Schema(description = "郵箱", example = "<EMAIL>")
    @Email(message = "郵箱格式不正確")
    @Size(max = 50, message = "郵箱長度不能超過 50 個字符")
    private String email;

    @Schema(description = "狀態,見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "狀態不能爲空")
    @InEnum(value = CommonStatusEnum.class, message = "修改狀態必須是 {value}")
    private Integer status;

}
