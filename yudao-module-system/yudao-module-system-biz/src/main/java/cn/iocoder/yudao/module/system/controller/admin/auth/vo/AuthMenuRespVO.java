package cn.iocoder.yudao.module.system.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "管理後臺 - 登錄用戶的菜單信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthMenuRespVO {

    @Schema(description = "菜單名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private Long id;

    @Schema(description = "父菜單 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long parentId;

    @Schema(description = "菜單名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String name;

    @Schema(description = "路由地址,僅菜單類型爲菜單或者目錄時，才需要傳", example = "post")
    private String path;

    @Schema(description = "組件路徑,僅菜單類型爲菜單時，才需要傳", example = "system/post/index")
    private String component;

    @Schema(description = "組件名", example = "SystemUser")
    private String componentName;

    @Schema(description = "菜單圖標,僅菜單類型爲菜單或者目錄時，才需要傳", example = "/menu/list")
    private String icon;

    @Schema(description = "是否可見", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean visible;

    @Schema(description = "是否緩存", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean keepAlive;

    @Schema(description = "是否總是顯示", example = "false")
    private Boolean alwaysShow;

    /**
     * 子路由
     */
    private List<AuthMenuRespVO> children;

}
