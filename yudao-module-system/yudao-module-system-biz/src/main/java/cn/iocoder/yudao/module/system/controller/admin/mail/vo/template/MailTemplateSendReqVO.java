package cn.iocoder.yudao.module.system.controller.admin.mail.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Schema(description = "管理後臺 - 郵件發送 Req VO")
@Data
public class MailTemplateSendReqVO {

    @Schema(description = "接收郵箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @NotEmpty(message = "接收郵箱不能爲空")
    private String mail;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test_01")
    @NotNull(message = "模板編碼不能爲空")
    private String templateCode;

    @Schema(description = "模板參數")
    private Map<String, Object> templateParams;

}
