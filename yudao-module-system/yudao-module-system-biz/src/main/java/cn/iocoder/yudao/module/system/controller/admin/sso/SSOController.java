package cn.iocoder.yudao.module.system.controller.admin.sso;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import java.util.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.annotation.Resource;
import javax.annotation.security.PermitAll;


@Tag(name = "管理後臺 - SSO")
@RestController
@RequestMapping("/system/sso")
@Validated
@Slf4j
public class SSOController {

    private static final Long  DEFAULT_DEPT_ID = 100L; // 默认部门ID，顶级部门
    private static final Long DEFAULT_ROLE_ID = 2L; // 默认角色ID, 普通角色

    @Resource
    private AdminUserService adminUserService;
    @Resource
    private AdminAuthService adminAuthService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private RoleService roleService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private DeptService deptService;

    @Value("${is2.sso.slient-login-url}")
    private String slientLoginUrl;

    @Value("${is2.sso.verify-token-url}")
    private String verifyTokenUrl;

    @Value("${is2.sso.api-token-url}")
    private String apiTokenUrl;

    @Value("${is2.sso.staff-reporting-url}")
    private String staffUrl;

    @Value("${is2.sso.bearer-token}")
    private String bearerToken;

    @Value("${is2.sso.cookie-name}")
    private String cookieName;

    /**
     * 获取sso登录连接
     * @return
     */
    @GetMapping("/getSlientLogin")
    @PermitAll
    @Operation(summary = "获取 SSO 静默登录链接")
    public CommonResult<String> getSlientLogin() {
        return CommonResult.success(slientLoginUrl);
    }

    /**
     * 处理单点登录（SSO）请求的接口方法。
     *
     * @param params 通过请求参数传递的访问令牌，用于验证用户身份。
     * @return 返回一个通用结果对象，包含登录成功后的认证信息（AuthLoginRespVO），
     *         或者在失败时返回错误信息。
     *
     * 功能描述：
     * 1. 验证外部系统的 access_token 并获取用户信息；
     * 2. 检查用户是否已存在于系统中，如果不存在则创建新用户；
     * 3. 调用登录逻辑完成用户认证并返回结果。
     */
    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "SSO 登录，使用 access_token 认证")
    public CommonResult<AuthLoginRespVO> login(@RequestBody JSONObject params) {
        String accessToken = params.getString("access_token");
        log.info("sso login access_token {}", accessToken);

        if (accessToken == null || accessToken.trim().isEmpty()) {
            log.warn("Invalid access_token: {}", accessToken);
            return CommonResult.success(null);
        }

        // 调用外部系统验证 access_token 的有效性，并获取用户信息
        JSONObject dataJson = verifyToken(accessToken);
        log.info("sso login userJson {}", dataJson);

        if (dataJson == null || !dataJson.getBoolean("success")) {
            return CommonResult.success(null);
        }

        JSONArray userArray = dataJson.getJSONArray("users");
        if (userArray == null || userArray.isEmpty()) {
            return CommonResult.success(null);
        }

        JSONObject userJson = userArray.getJSONObject(0);

        // 检查 SYS_PCM 权限
        JSONObject groups = userJson.getJSONObject("groups");
        if (groups == null || groups.isEmpty() || !groups.containsKey("SYS_PCM") || groups.getJSONObject("SYS_PCM").isEmpty()) {
            log.info("用户无SYS_PCM权限");
            return CommonResult.success(null);
        }

        String userName = userJson.getString("loginID");
        if (StringUtils.isBlank(userName)) {
            log.warn("Missing or empty loginID in user data");
            return CommonResult.success(null);
        }

        AdminUserDO managerUser = getOrCreateManager(userName);
        log.info("獲得上司對象： {}", JSON.toJSON(managerUser));
        Long leaderId = Optional.ofNullable(managerUser)
                .map(AdminUserDO::getId)
                .orElse(null);

        // 检查或创建用户
        AdminUserDO user = adminUserService.getUserByUsername(userName);
        if (user == null) {
            // 如果用户不存在，则根据外部用户信息创建新用户
            UserSaveReqVO userSaveReqVO = getUserSaveReqVO(userJson);
            user = createUser(userSaveReqVO, leaderId);
            // 分配角色和数据权限
            assignUserRole(user.getId());
        } else {
            // 每次登入都需要更新部門和上司
            String deptName = getDeptName(userJson);
            Long deptId = getOrCreateDept(deptName);
            adminUserService.updateUserDeptAndLeader(user.getId(), deptId, leaderId);
        }

        // 构造登录请求并执行登录
        AuthLoginReqVO reqVO = BeanUtils.toBean(user, AuthLoginReqVO.class);
        reqVO.setPassword("123456");

        // 调用登录服务完成用户认证，并返回登录结果
        return CommonResult.success(adminAuthService.loginSSO(reqVO));
    }

    /**
     * 獲取或創建上司信息
     * @param loginId
     * @return
     */
    private AdminUserDO getOrCreateManager(String loginId) {
        // 通過SSO獲取上司信息
        JSONObject apiTokenJson = getApiToken();
        log.info("sso api token {}", apiTokenJson);
        if (apiTokenJson == null || !apiTokenJson.getBoolean("success")) {
            return null;
        }

        String apiTokenStr = apiTokenJson.getString("token");
        if (StringUtils.isBlank(apiTokenStr)) {
            return null;
        }

        JSONObject staffReportingLine = getStaffReportingLine(loginId, apiTokenStr);
        log.info("sso staff reporting line {}", staffReportingLine);
        JSONArray dataArray = staffReportingLine.getJSONArray("data");
        if (dataArray == null || dataArray.isEmpty()) {
            return null;
        }
        JSONObject dataJson = dataArray.getJSONObject(0);
        JSONArray managerJson = dataJson.getJSONArray("manager");
        if (managerJson == null || managerJson.isEmpty()) {
            return null;
        }

        Long deptId = DEFAULT_DEPT_ID;
        String orgUnitGroup = dataJson.getString("orgUnitGroup");//獲取上司的部門ID
        if (StringUtils.isNotBlank(orgUnitGroup)) {
            JSONArray groups = dataJson.getJSONArray("groups");
            // 从 SSO报文中获取上司的部門名称
            String deptName = null;
            for (Object obj : groups) {
                Map<String, Object> map = (Map<String, Object>) obj;
                if (orgUnitGroup.equals(map.get("_id"))) {
                    deptName = (String) map.get("c_name");
                    break;
                }
            }
            // 获取或创建部门
            deptId = getOrCreateDept(deptName);
        }

        JSONObject jsonObject = managerJson.getJSONObject(0);
        if (jsonObject == null || jsonObject.isEmpty()) {
            return null;
        }

        String userName = jsonObject.getString("loginID");

        // 检查或创建上司
        AdminUserDO managerUser = adminUserService.getUserByUsername(userName);
        if (managerUser == null) {
            // 如果上司不存在，则根据外部用户信息创建新上司
            UserSaveReqVO userReq = getUserSaveReqVO(jsonObject);
            userReq.setDeptId(deptId);
            // 上司 不设置上司ID
            managerUser = createUser(userReq, null);
            // 分配角色和数据权限
            assignUserRole(managerUser.getId());
        }
        return managerUser;
    }

    /**
     * 验证访问令牌的有效性并返回外部用户信息。
     *
     * @param accessToken 访问令牌，用于验证用户身份的字符串。
     * @return 如果验证成功，返回包含用户信息的 ExternalUserDTO 对象；
     *         如果验证失败或发生异常，返回 null。
     */
    private JSONObject verifyToken(String accessToken) {
        String url = verifyTokenUrl + accessToken;
        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.add("Cookie", "connect.sid=s%253AuCAw4Ulyg09pbVuM7rzbt8eIU6H6ZHxW.C4ogs9lMt%252BfWo%252B1eNvbJuhEvJuKABbNRl00Q9q71JMs");

            // 创建 HttpEntity 并设置请求头
            HttpEntity<String> entity = new HttpEntity<>(headers);
            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            // 解析 JSON
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            Map<String, Object> map = objectMapper.convertValue(jsonNode, Map.class);
            return new JSONObject(map);
        } catch (Exception e) {
            System.err.println("验证失败" + e.getMessage());
            // 捕获异常并返回 null，表示验证失败
            return null;
        }
    }

    /**
     * 获取 API Token
     * @return
     */
    private JSONObject getApiToken() {
        String url = apiTokenUrl;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.add("Authorization", "Bearer " + bearerToken);
            headers.add("Cookie", cookieName);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            Map<String, Object> map = objectMapper.convertValue(jsonNode, Map.class);
            return new JSONObject(map);
        } catch (Exception e) {
            System.err.println("获取 API Token 失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取员工汇报链
     * @param loginID
     * @param apiToken
     * @return
     */
    private JSONObject getStaffReportingLine(String loginID, String apiToken) {
        String url = staffUrl + loginID;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.add("Authorization", "Bearer " + apiToken);
            headers.add("Cookie", cookieName);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(response.getBody());
            Map<String, Object> map = objectMapper.convertValue(jsonNode, Map.class);
            return new JSONObject(map);
        } catch (Exception e) {
            System.err.println("获取员工汇报线失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 根据外部用户信息创建管理员用户。
     *
     * @param userReq 外部用户信息对象，包含用户名、昵称、邮箱等信息。
     *                     该对象会被转换为内部用户保存请求对象。
     * @return 返回创建的管理员用户对象 AdminUserDO。
     *         如果创建失败或用户不存在，则返回值可能为 null。
     */
    private AdminUserDO createUser(UserSaveReqVO userReq, Long leaderId) {
        userReq.setLeaderId(leaderId);// 設置上司ID
        try {
            // 调用服务层方法创建用户，并获取返回的用户ID
            Long userId = adminUserService.createUserSSO(userReq);
            if (userId == null) {
                log.error("Failed to create user: username={}", userReq.getUsername());
                throw new RuntimeException("用户创建失败");
            }

            // 获取用户信息（假设 createUser 无法直接返回 AdminUserDO）
            AdminUserDO createdUser = adminUserService.getUser(userId);
            if (createdUser == null) {
                log.error("User not found after creation: userId={}", userId);
                throw new RuntimeException("用户创建后未找到");
            }

            log.info("Successfully created user: username={}", userReq.getUsername());
            return createdUser;

        } catch (Exception e) {
            log.error("Error creating user: username={}", userReq.getUsername(), e);
            throw new RuntimeException("用户创建失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户保存请求对象
     * @param userJson
     * @return
     */
    private UserSaveReqVO getUserSaveReqVO(JSONObject userJson) {
        // 验证必填字段
        String loginID = userJson.getString("loginID");
        if (StringUtils.isBlank(loginID)) {
            log.warn("Cannot create user: missing or empty loginID");
            throw new IllegalArgumentException("无效的用户数据：loginID 缺失");
        }

        // 验证可选字段格式
        String email = userJson.getString("email");
        String mobilePhone = userJson.getString("mobilePhone");
        String officePhone = userJson.getString("officePhone");
        String phone = StringUtils.isBlank(mobilePhone) ? officePhone : mobilePhone;
        String name = userJson.getString("name");
        String gender = userJson.getString("gender");
        Integer sex = "M".equals(gender) ? 1 : "F".equals(gender) ? 2 : 0;
        String businessTitle = userJson.getString("businessTitle");

        // 将外部用户信息转换为内部用户保存请求对象
        UserSaveReqVO userReq = new UserSaveReqVO();
        userReq.setUsername(loginID);
        userReq.setNickname(name);
        userReq.setEmail(email);
        userReq.setSex(sex);
        userReq.setRemark(businessTitle);// 把业务职务作为备注
        userReq.setPassword("123456");// 初始化密码
        String deptName = getDeptName(userJson);
        Long deptId = getOrCreateDept(deptName);
        userReq.setDeptId(deptId); // 默认部门ID

        return userReq;
    }

    /**
     * 从 userObj 获取部门 ID
     */
    private Long getOrCreateDept(String deptName) {
        Long deptId = DEFAULT_DEPT_ID;

        if (StringUtils.isNotBlank(deptName)) {
            // 根據部門名稱創建或獲取部門
            DeptDO dept = deptService.getOrCreateDeptByName(deptName);
            if (dept != null) {
                deptId = dept.getId();
            }
        }
        return deptId; //回退到默认部门
    }

    /**
     * 创建sso用户时，从sso返回的信息中获取部门名称
     * 当前登入的用户：orgUnitGroup 是 JSONObject
     * 上司api中没有userJson,所以 orgUnitGroup is null
     * @param userJson
     * @return
     */
    private String getDeptName(JSONObject userJson) {
        if (!userJson.containsKey("orgUnitGroup")
                || userJson.getJSONObject("orgUnitGroup") == null
                || userJson.getJSONObject("orgUnitGroup").isEmpty()) {
            log.info("Cannot get deptId: missing or empty orgUnitGroup");
            return null;
        }

        JSONObject orgUnitJson = userJson.getJSONObject("orgUnitGroup");
        String deptName = orgUnitJson.getString("c_name");
        return deptName;
    }

    /**
     * 为用户分配默认角色。
     * @param userId
     */
    private void assignUserRole(Long userId) {
        Set<Long> roleIds = new HashSet<>();
        roleIds.add(DEFAULT_ROLE_ID);
        RoleDO role = roleService.getRole(DEFAULT_ROLE_ID);
        if (role != null) {
            permissionService.assignUserRole(userId, roleIds);
        }
    }


}
