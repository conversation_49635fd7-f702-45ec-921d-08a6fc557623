package cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理後臺 - OAuth2 客戶端分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OAuth2ClientPageReqVO extends PageParam {

    @Schema(description = "應用名，模糊匹配", example = "土豆")
    private String name;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", example = "1")
    private Integer status;

}
