package cn.iocoder.yudao.module.system.controller.admin.logger.vo.loginlog;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理後臺 - 登錄日誌 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LoginLogRespVO {

    @Schema(description = "日誌編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("日誌主鍵")
    private Long id;

    @Schema(description = "日誌類型，參見 LoginLogTypeEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "日誌類型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.LOGIN_TYPE)
    private Integer logType;

    @Schema(description = "用戶編號", example = "666")
    private Long userId;

    @Schema(description = "用戶類型，參見 UserTypeEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer userType;

    @Schema(description = "鏈路追蹤編號", example = "89aca178-a370-411c-ae02-3f0d672be4ab")
    private String traceId;

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @ExcelProperty("用戶賬號")
    private String username;

    @Schema(description = "登錄結果，參見 LoginResultEnum 枚舉類", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "登錄結果", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.LOGIN_RESULT)
    private Integer result;

    @Schema(description = "用戶 IP", requiredMode = Schema.RequiredMode.REQUIRED, example = "127.0.0.1")
    @ExcelProperty("登錄 IP")
    private String userIp;

    @Schema(description = "瀏覽器 UserAgent", example = "Mozilla/5.0")
    @ExcelProperty("瀏覽器 UA")
    private String userAgent;

    @Schema(description = "登錄時間", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("登錄時間")
    private LocalDateTime createTime;

}
