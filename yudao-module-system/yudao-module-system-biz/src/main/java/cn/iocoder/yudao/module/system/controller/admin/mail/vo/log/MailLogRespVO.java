package cn.iocoder.yudao.module.system.controller.admin.mail.vo.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理後臺 - 郵件日誌 Response VO")
@Data
public class MailLogRespVO {

    @Schema(description = "編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "31020")
    private Long id;

    @Schema(description = "用戶編號", example = "30883")
    private Long userId;

    @Schema(description = "用戶類型，參見 UserTypeEnum 枚舉", example = "2")
    private Byte userType;

    @Schema(description = "接收郵箱地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String toMail;

    @Schema(description = "郵箱賬號編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "18107")
    private Long accountId;

    @Schema(description = "發送郵箱地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String fromMail;

    @Schema(description = "模板編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "5678")
    private Long templateId;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test_01")
    private String templateCode;

    @Schema(description = "模版發送人名稱", example = "李四")
    private String templateNickname;

    @Schema(description = "郵件標題", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試標題")
    private String templateTitle;

    @Schema(description = "郵件內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試內容")
    private String templateContent;

    @Schema(description = "郵件參數", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, Object> templateParams;

    @Schema(description = "發送狀態，參見 MailSendStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Byte sendStatus;

    @Schema(description = "發送時間")
    private LocalDateTime sendTime;

    @Schema(description = "發送返回的消息 ID", example = "28568")
    private String sendMessageId;

    @Schema(description = "發送異常")
    private String sendException;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
