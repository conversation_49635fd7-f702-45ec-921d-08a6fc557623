package cn.iocoder.yudao.module.system.controller.admin.auth.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Schema(description = "管理後臺 - Register Request VO")
@Data
public class AuthRegisterReqVO extends CaptchaVerificationReqVO {

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @NotBlank(message = "用戶賬號不能爲空")
    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用戶賬號由 數字、字母 組成")
    @Size(min = 4, max = 30, message = "用戶賬號長度爲 4-30 個字符")
    private String username;

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotBlank(message = "用戶暱稱不能爲空")
    @Size(max = 30, message = "用戶暱稱長度不能超過 30 個字符")
    private String nickname;

    @Schema(description = "密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotEmpty(message = "密碼不能爲空")
    @Length(min = 4, max = 16, message = "密碼長度爲 4-16 位")
    private String password;
}