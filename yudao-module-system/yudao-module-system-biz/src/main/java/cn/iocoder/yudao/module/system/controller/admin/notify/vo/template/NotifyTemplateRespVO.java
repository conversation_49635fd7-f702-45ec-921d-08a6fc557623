package cn.iocoder.yudao.module.system.controller.admin.notify.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理後臺 - 站內信模版 Response VO")
@Data
public class NotifyTemplateRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "模版名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "測試模版")
    private String name;

    @Schema(description = "模版編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "SEND_TEST")
    private String code;

    @Schema(description = "模版類型，對應 system_notify_template_type 字典", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer type;

    @Schema(description = "發送人名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "土豆")
    private String nickname;

    @Schema(description = "模版內容", requiredMode = Schema.RequiredMode.REQUIRED, example = "我是模版內容")
    private String content;

    @Schema(description = "參數數組", example = "name,code")
    private List<String> params;

    @Schema(description = "狀態，參見 CommonStatusEnum 枚舉", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "備註", example = "我是備註")
    private String remark;

    @Schema(description = "創建時間", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
