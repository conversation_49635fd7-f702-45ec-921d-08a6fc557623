package cn.iocoder.yudao.module.system.dal.dataobject.notify;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.system.dal.dataobject.mail.MailTemplateDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 站內信 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_notify_message", autoResultMap = true)
@KeySequence("system_notify_message_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotifyMessageDO extends BaseDO {

    /**
     * 站內信編號，自增
     */
    @TableId
    private Long id;
    /**
     * 用戶編號
     *
     * 關聯 MemberUserDO 的 id 字段、或者 AdminUserDO 的 id 字段
     */
    private Long userId;
    /**
     * 用戶類型
     *
     * 枚舉 {@link UserTypeEnum}
     */
    private Integer userType;

    // ========= 模板相關字段 =========

    /**
     * 模版編號
     *
     * 關聯 {@link NotifyTemplateDO#getId()}
     */
    private Long templateId;
    /**
     * 模版編碼
     *
     * 關聯 {@link NotifyTemplateDO#getCode()}
     */
    private String templateCode;
    /**
     * 模版類型
     *
     * 冗餘 {@link NotifyTemplateDO#getType()}
     */
    private Integer templateType;
    /**
     * 模版發送人名稱
     *
     * 冗餘 {@link NotifyTemplateDO#getNickname()}
     */
    private String templateNickname;
    /**
     * 模版內容
     *
     * 基於 {@link NotifyTemplateDO#getContent()} 格式化後的內容
     */
    private String templateContent;
    /**
     * 模版參數
     *
     * 基於 {@link NotifyTemplateDO#getParams()} 輸入後的參數
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> templateParams;

    // ========= 讀取相關字段 =========

    /**
     * 是否已讀
     */
    private Boolean readStatus;
    /**
     * 閱讀時間
     */
    private LocalDateTime readTime;

}
