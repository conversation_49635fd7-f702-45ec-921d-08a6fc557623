package cn.iocoder.yudao.module.system.dal.dataobject.dict;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典數據表
 *
 * <AUTHOR>
 */
@TableName("system_dict_data")
@KeySequence("system_dict_data_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
public class DictDataDO extends BaseDO {

    /**
     * 字典數據編號
     */
    @TableId
    private Long id;
    /**
     * 字典排序
     */
    private Integer sort;
    /**
     * 字典標籤
     */
    private String label;
    /**
     * 字典值
     */
    private String value;
    /**
     * 字典類型
     *
     * 冗餘 {@link DictDataDO#getDictType()}
     */
    private String dictType;
    /**
     * 狀態
     *
     * 枚舉 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 顏色類型
     *
     * 對應到 element-ui 爲 default、primary、success、info、warning、danger
     */
    private String colorType;
    /**
     * css 樣式
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String cssClass;
    /**
     * 備註
     */
    private String remark;

}
