package cn.iocoder.yudao.module.system.controller.admin.user.vo.profile;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理後臺 - 用戶個人中心更新密碼 Request VO")
@Data
public class UserProfileUpdatePasswordReqVO {

    @Schema(description = "舊密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @NotEmpty(message = "舊密碼不能爲空")
    @Length(min = 4, max = 16, message = "密碼長度爲 4-16 位")
    private String oldPassword;

    @Schema(description = "新密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "654321")
    @NotEmpty(message = "新密碼不能爲空")
    @Length(min = 4, max = 16, message = "密碼長度爲 4-16 位")
    private String newPassword;

}
