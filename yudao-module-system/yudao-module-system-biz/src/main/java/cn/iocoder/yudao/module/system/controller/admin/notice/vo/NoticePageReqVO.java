package cn.iocoder.yudao.module.system.controller.admin.notice.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "管理後臺 - 通知公告分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class NoticePageReqVO extends PageParam {

    @Schema(description = "通知公告名稱，模糊匹配", example = "芋道")
    private String title;

    @Schema(description = "展示狀態，參見 CommonStatusEnum 枚舉類", example = "1")
    private Integer status;

}
