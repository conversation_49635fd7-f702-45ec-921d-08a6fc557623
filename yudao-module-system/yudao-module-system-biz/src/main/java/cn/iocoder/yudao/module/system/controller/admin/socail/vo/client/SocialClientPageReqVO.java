package cn.iocoder.yudao.module.system.controller.admin.socail.vo.client;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理後臺 - 社交客戶端分頁 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SocialClientPageReqVO extends PageParam {

    @Schema(description = "應用名", example = "yudao商城")
    private String name;

    @Schema(description = "社交平臺的類型", example = "31")
    private Integer socialType;

    @Schema(description = "用戶類型", example = "2")
    private Integer userType;

    @Schema(description = "客戶端編號", example = "145442115")
    private String clientId;

    @Schema(description = "狀態", example = "1")
    private Integer status;

}
