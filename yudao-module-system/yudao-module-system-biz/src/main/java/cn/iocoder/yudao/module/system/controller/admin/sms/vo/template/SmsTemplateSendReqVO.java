package cn.iocoder.yudao.module.system.controller.admin.sms.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Schema(description = "管理後臺 - 短信模板的發送 Request VO")
@Data
public class SmsTemplateSendReqVO {

    @Schema(description = "手機號", requiredMode = Schema.RequiredMode.REQUIRED, example = "15601691300")
    @NotNull(message = "手機號不能爲空")
    private String mobile;

    @Schema(description = "模板編碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "test_01")
    @NotNull(message = "模板編碼不能爲空")
    private String templateCode;

    @Schema(description = "模板參數")
    private Map<String, Object> templateParams;

}
