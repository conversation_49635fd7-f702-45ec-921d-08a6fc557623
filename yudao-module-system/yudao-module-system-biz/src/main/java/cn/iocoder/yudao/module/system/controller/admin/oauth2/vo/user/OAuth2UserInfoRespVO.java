package cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "管理後臺 - OAuth2 獲得用戶基本信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OAuth2UserInfoRespVO {

    @Schema(description = "用戶編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String username;

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋道")
    private String nickname;

    @Schema(description = "用戶郵箱", example = "<EMAIL>")
    private String email;
    @Schema(description = "手機號碼", example = "15601691300")
    private String mobile;

    @Schema(description = "用戶性別，參見 SexEnum 枚舉類", example = "1")
    private Integer sex;

    @Schema(description = "用戶頭像", example = "https://www.iocoder.cn/xxx.png")
    private String avatar;

    /**
     * 所在部門
     */
    private Dept dept;

    /**
     * 所屬崗位數組
     */
    private List<Post> posts;

    @Schema(description = "部門")
    @Data
    public static class Dept {

        @Schema(description = "部門編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;

        @Schema(description = "部門名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "研發部")
        private String name;

    }

    @Schema(description = "崗位")
    @Data
    public static class Post {

        @Schema(description = "崗位編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;

        @Schema(description = "崗位名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "開發")
        private String name;

    }

}
