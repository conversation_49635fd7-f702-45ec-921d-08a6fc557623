package cn.iocoder.yudao.module.system.dal.dataobject.mail;

import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.system.enums.mail.MailSendStatusEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 郵箱日誌 DO
 * 記錄每一次郵件的發送
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@TableName(value = "system_mail_log", autoResultMap = true)
@KeySequence("system_mail_log_seq") // 用於 Oracle、PostgreSQL、Kingbase、DB2、H2 數據庫的主鍵自增。如果是 MySQL 等數據庫，可不寫。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailLogDO extends BaseDO implements Serializable {

    /**
     * 日誌編號，自增
     */
    private Long id;

    /**
     * 用戶編碼
     */
    private Long userId;
    /**
     * 用戶類型
     *
     * 枚舉 {@link UserTypeEnum}
     */
    private Integer userType;
    /**
     * 接收郵箱地址
     */
    private String toMail;

    /**
     * 郵箱賬號編號
     *
     * 關聯 {@link MailAccountDO#getId()}
     */
    private Long accountId;
    /**
     * 發送郵箱地址
     *
     * 冗餘 {@link MailAccountDO#getMail()}
     */
    private String fromMail;

    // ========= 模板相關字段 =========
    /**
     * 模版編號
     *
     * 關聯 {@link MailTemplateDO#getId()}
     */
    private Long templateId;
    /**
     * 模版編碼
     *
     * 冗餘 {@link MailTemplateDO#getCode()}
     */
    private String templateCode;
    /**
     * 模版發送人名稱
     *
     * 冗餘 {@link MailTemplateDO#getNickname()}
     */
    private String templateNickname;
    /**
     * 模版標題
     */
    private String templateTitle;
    /**
     * 模版內容
     *
     * 基於 {@link MailTemplateDO#getContent()} 格式化後的內容
     */
    private String templateContent;
    /**
     * 模版參數
     *
     * 基於 {@link MailTemplateDO#getParams()} 輸入後的參數
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> templateParams;

    // ========= 發送相關字段 =========
    /**
     * 發送狀態
     *
     * 枚舉 {@link MailSendStatusEnum}
     */
    private Integer sendStatus;
    /**
     * 發送時間
     */
    private LocalDateTime sendTime;
    /**
     * 發送返回的消息 ID
     */
    private String sendMessageId;
    /**
     * 發送異常
     */
    private String sendException;

}
