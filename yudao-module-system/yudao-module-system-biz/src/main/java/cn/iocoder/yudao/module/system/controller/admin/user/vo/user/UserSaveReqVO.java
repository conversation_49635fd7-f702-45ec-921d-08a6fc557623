package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import cn.iocoder.yudao.module.system.framework.operatelog.core.DeptParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.PostParseFunction;
import cn.iocoder.yudao.module.system.framework.operatelog.core.SexParseFunction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mzt.logapi.starter.annotation.DiffLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.util.Set;

@Schema(description = "管理後臺 - 用戶創建/修改 Request VO")
@Data
public class UserSaveReqVO {

    @Schema(description = "用戶編號", example = "1024")
    private Long id;

    @Schema(description = "用戶賬號", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudao")
    @NotBlank(message = "用戶賬號不能爲空")
    @Pattern(regexp = "^[a-zA-Z0-9]+$", message = "用戶賬號由 數字、字母 組成")
    @Size(min = 4, max = 30, message = "用戶賬號長度爲 4-30 個字符")
    @DiffLogField(name = "用戶賬號")
    private String username;

    @Schema(description = "用戶暱稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @Size(max = 30, message = "用戶暱稱長度不能超過30個字符")
    @DiffLogField(name = "用戶暱稱")
    private String nickname;

    @Schema(description = "備註", example = "我是一個用戶")
    @DiffLogField(name = "備註")
    private String remark;

    @Schema(description = "部門編號", example = "我是一個用戶")
    @DiffLogField(name = "部門", function = DeptParseFunction.NAME)
    private Long deptId;

    @Schema(description = "崗位編號數組", example = "1")
    @DiffLogField(name = "崗位", function = PostParseFunction.NAME)
    private Set<Long> postIds;

    @Schema(description = "用戶郵箱", example = "<EMAIL>")
    @Email(message = "郵箱格式不正確")
    @Size(max = 50, message = "郵箱長度不能超過 50 個字符")
    @DiffLogField(name = "用戶郵箱")
    private String email;

    @Schema(description = "手機號碼", example = "15601691300")
    @DiffLogField(name = "手機號碼")
    private String mobile;

    @Schema(description = "用戶性別，參見 SexEnum 枚舉類", example = "1")
    @DiffLogField(name = "用戶性別", function = SexParseFunction.NAME)
    private Integer sex;

    @Schema(description = "用戶頭像", example = "https://www.iocoder.cn/xxx.png")
    @DiffLogField(name = "用戶頭像")
    private String avatar;

    // ========== 僅【創建】時，需要傳遞的字段 ==========

    @Schema(description = "密碼", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Length(min = 4, max = 16, message = "密碼長度爲 4-16 位")
    private String password;

    @AssertTrue(message = "密碼不能爲空")
    @JsonIgnore
    public boolean isPasswordValid() {
        return id != null // 修改時，不需要傳遞
                || (ObjectUtil.isAllNotEmpty(password)); // 新增時，必須都傳遞 password
    }

    @Schema(description = "上司", example = "1")
    private Long leaderId;

    @Schema(description = "是否允許導出數據", example = "true")
    private Integer allowExport;

}
