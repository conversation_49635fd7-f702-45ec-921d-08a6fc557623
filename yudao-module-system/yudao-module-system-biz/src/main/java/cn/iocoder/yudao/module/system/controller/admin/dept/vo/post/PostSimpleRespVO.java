package cn.iocoder.yudao.module.system.controller.admin.dept.vo.post;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 崗位信息的精簡 Response VO")
@Data
public class PostSimpleRespVO {

    @Schema(description = "崗位序號", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("崗位序號")
    private Long id;

    @Schema(description = "崗位名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "小土豆")
    @ExcelProperty("崗位名稱")
    private String name;

}
