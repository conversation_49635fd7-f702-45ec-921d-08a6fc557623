# PCM (Partner Contact Management) 系统详细设计文档

## 1. 项目概述

### 1.1 项目简介
PCM (Partner Contact Management) 是一个基于Spring Boot + Vue.js的名片管理系统，主要用于管理合作伙伴的联系信息、商谈记录和相关业务数据。系统采用前后端分离架构，支持名片OCR识别、细粒度权限管理、数据导出等功能。



### 1.2 技术栈
- **后端框架**: Spring Boot 2.7.18
- **前端框架**: Vue.js 2.7.14 + Element UI 2.15.14
- **数据库**: MySQL
- **ORM框架**: MyBatis Plus
- **权限框架**: Spring Security
- **构建工具**: Maven
- **Java版本**: JDK 1.8

## 2. 系统架构

### 2.1 整体架构
系统采用分层架构设计，主要分为以下几个层次：

```
┌─────────────────────────────────────────┐
│              前端层 (Vue.js)              │
├─────────────────────────────────────────┤
│              控制器层 (Controller)         │
├─────────────────────────────────────────┤
│              业务逻辑层 (Service)          │
├─────────────────────────────────────────┤
│              数据访问层 (Mapper)           │
├─────────────────────────────────────────┤
│              数据库层 (MySQL)             │
└─────────────────────────────────────────┘
```

### 2.2 模块结构
项目采用Maven多模块结构：

- **yudao-dependencies**: 依赖管理模块
- **yudao-framework**: 框架核心组件
- **yudao-server**: 主服务模块
- **yudao-module-system**: 系统管理模块
- **yudao-module-infra**: 基础设施模块
- **yudao-module-pcm**: PCM业务模块（核心）
- **yudao-ui**: 前端Vue项目

## 3. 核心业务模块

### 3.1 PCM模块结构
```
yudao-module-pcm/
├── yudao-module-pcm-api/     # API接口定义
└── yudao-module-pcm-biz/     # 业务实现
    ├── controller/           # 控制器层
    ├── service/             # 业务逻辑层
    ├── dal/                 # 数据访问层
    │   ├── dataobject/      # 数据对象
    │   └── mapper/          # 数据映射器
    └── convert/             # 对象转换器
```

### 3.2 主要功能模块

#### 3.2.1 名片管理 (Card)
- **功能**: 名片信息的增删改查、分组管理、OCR识别、名片权限管理
- **核心实体**: CardDO, CardRawDO
- **主要字段**:
  - 基本信息：姓名、职位、公司、联系方式
  - 多语言支持：中英文姓名、职位
  - 分组管理：groupId用于合并相关名片
  - OCR数据：原始识别文本和结构化结果

#### 3.2.2 公司管理 (Company)
- **功能**: 公司信息管理、层级结构维护
- **核心实体**: CompanyDO
- **特点**: 支持树形结构，parent_id实现层级关系

#### 3.2.3 會面記錄 (Meeting)
- **功能**: 记录与名片相关的商谈信息
- **核心实体**: MeetingDO, MeetingPermissionDO
- **权限控制**: 支持按用户和部门控制可见性

#### 3.2.4 导出管理 (Export)
- **功能**: 名片数据导出申请、审批流程
- **核心实体**: ExportRecordDO, ExportTotalDO
- **流程**: 申请 → 审批 → 导出
- **限制**: 30天内累计导出限制

#### 3.2.5 标签管理 (Tags)
- **功能**: 为名片添加标签分类
- **核心实体**: TagsDO

## 4. 数据库设计

### 4.1 核心表结构

#### 4.1.1 名片表 (pcm_card)
**表名**: `pcm_card`
**说明**: 存储名片的详细信息，支持中英文双语，包含完整的联系信息和业务信息

**字段详情**:
```sql
CREATE TABLE pcm_card (
  -- 主键和分组
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '名片ID',
  group_id VARCHAR(50) COMMENT '分组ID,用于合并名片',

  -- 英文个人信息
  title_en VARCHAR(20) COMMENT 'Title (Mr, Ms, Mrs, Others)',
  first_name_en VARCHAR(50) COMMENT 'First Name (e.g. Tai-man)',
  last_name_en VARCHAR(50) COMMENT 'Last Name (e.g. CHAN)',
  honour_en VARCHAR(50) COMMENT 'Honour (e.g. MH, JP)',

  -- 中文个人信息
  title VARCHAR(20) COMMENT '稱謂（先生/ 小姐/ 女士/其他）',
  first_name VARCHAR(50) COMMENT '姓氏',
  last_name VARCHAR(50) COMMENT '名字',
  honour VARCHAR(50) COMMENT '勳銜',
  full_name VARCHAR(100) COMMENT '全名',
  nick_name VARCHAR(100) COMMENT '暱稱',

  -- 英文公司信息
  company_id_en BIGINT COMMENT 'ID of Organisation',
  company_name_en VARCHAR(200) COMMENT 'Name of Organisation',
  department_en VARCHAR(100) COMMENT 'Department',
  job_title_en VARCHAR(100) COMMENT 'Job Title',

  -- 中文公司信息
  company_id BIGINT COMMENT '機構ID',
  company_name VARCHAR(200) COMMENT '機構名稱',
  department VARCHAR(100) COMMENT '部門',
  job_title VARCHAR(100) COMMENT '職位',

  -- 联系方式 - 电话
  phone_mobile VARCHAR(20) COMMENT '流動電話',
  phone_mobile_area_code VARCHAR(10) COMMENT '流動電話區號',
  phone_mainland VARCHAR(20) COMMENT '內地號碼',
  phone_mainland_area_code VARCHAR(10) COMMENT '內地號碼區號',
  phone_office VARCHAR(20) COMMENT 'Phone(office) 電話（辦公室）',
  phone_office_area_code VARCHAR(10) COMMENT '辦公電話區號',
  phone_direct_line VARCHAR(20) COMMENT 'Phone(Direct Line)電話（直線）',
  phone_direct_line_area_code VARCHAR(10) COMMENT '直線電話區號',
  fax_number VARCHAR(20) COMMENT 'fax number 傳真（如適用）',

  -- 联系方式 - 邮箱
  email VARCHAR(100) COMMENT 'Email Address 電郵',
  email1 VARCHAR(100) COMMENT 'Email Address 電郵1',
  email2 VARCHAR(100) COMMENT 'Email Address 電郵2',

  -- 英文地址信息
  address_line1_en VARCHAR(200) COMMENT 'address line 1',
  address_line2_en VARCHAR(200) COMMENT 'address line 2',
  district_en VARCHAR(100) COMMENT 'district(e.g. Tsim Sha Tsui)',
  area_en VARCHAR(100) COMMENT 'area(e.g. Kowloon)',
  country_en VARCHAR(100) COMMENT 'country',

  -- 中文地址信息
  address_line1 VARCHAR(200) COMMENT '地址1',
  address_line2 VARCHAR(200) COMMENT '地址2',
  district VARCHAR(100) COMMENT '地區',
  area VARCHAR(100) COMMENT '區域',
  country VARCHAR(100) COMMENT '国家',
  province VARCHAR(100) COMMENT '省',
  city VARCHAR(100) COMMENT '城市',
  address VARCHAR(500) COMMENT '完整地址',

  -- 业务信息
  industry_type VARCHAR(100) COMMENT '(e.g. Technology, Healthcare) 行業類型',
  business_type VARCHAR(100) COMMENT '業務類型（政府或相關組織、企業、基金會、非政府組織、其他）',
  other_business VARCHAR(200) COMMENT '其他業務類型',

  -- 社交媒体
  linkedin_profile_url VARCHAR(500) COMMENT 'linkedin profile url',
  facebook_page_url VARCHAR(500) COMMENT 'facebook page url',
  instagram_url VARCHAR(500) COMMENT 'instagram url',
  wechat_id VARCHAR(100) COMMENT 'wechat id',
  social_media VARCHAR(1000) COMMENT '社交媒体信息',
  website VARCHAR(500) COMMENT '网址',
  instant_messaging VARCHAR(200) COMMENT '即时消息',

  -- 扩展信息
  communication_history TEXT COMMENT '會面詳情',
  custom_tags VARCHAR(500) COMMENT '標籤',
  status VARCHAR(20) COMMENT '状态（Active:活跃，Inactive:非活跃）',
  notes TEXT COMMENT '備註',
  description TEXT COMMENT '简介',
  anniversary VARCHAR(100) COMMENT '纪念日',
  exchange_date VARCHAR(100) COMMENT '交换日期',

  -- 图片信息
  image_url VARCHAR(500) COMMENT '图片头像地址',
  back_url VARCHAR(500) COMMENT '反面名片的URL',
  image_id BIGINT COMMENT '正面图片ID',
  back_id BIGINT COMMENT '反面图片ID',
  double_sided BOOLEAN COMMENT '是否双面：true 为双面，false 为单面',

  -- 权限控制
  user_id VARCHAR(500) COMMENT '有权查看的同事',
  dept_id VARCHAR(500) COMMENT '有权查看的部门',

  -- 审计字段
  create_time DATETIME COMMENT '创建时间',
  update_time DATETIME COMMENT '最后更新时间',
  create_user_id BIGINT COMMENT '创建者id',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
);
```

#### 4.1.2 名片原始数据表 (pcm_card_raw)
**表名**: `pcm_card_raw`
**说明**: 存储名片OCR识别的原始数据和结构化结果

**字段详情**:
```sql
CREATE TABLE pcm_card_raw (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '名片ID',
  image_id BIGINT COMMENT '圖片ID，對應infra_file中的ID',
  raw_ocr_text TEXT COMMENT '原始識別文本',
  ocr_result_json TEXT COMMENT '引擎返回的結構化 JSON 結果',
  direction_flag BOOLEAN COMMENT '正反面標識，1:正面,0反面',
  api_source VARCHAR(50) COMMENT '數據來源，tencent_ocr:騰訊ocr,ali_qwen：阿里千問',

  -- 审计字段
  create_time DATETIME COMMENT '建立時間',
  update_time DATETIME COMMENT '更新時間',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否刪除'
);
```

#### 4.1.3 导出记录表 (pcm_card_export_records)
**表名**: `pcm_card_export_records`
**说明**: 记录名片导出申请和审批流程

**字段详情**:
```sql
CREATE TABLE pcm_card_export_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '記錄ID',
  user_id BIGINT COMMENT '發起導出請求的用戶ID',
  requester_name VARCHAR(100) COMMENT '發起導出請求的用戶名',
  export_count INT COMMENT '導出名片數',
  status VARCHAR(20) COMMENT '導出請求狀態，待審批(PENDING)、已通過(APPROVED)、已拒絕(REJECTED)',
  requester_comment TEXT COMMENT '申請人備註',
  approver_id BIGINT COMMENT '審批人ID',
  approver_name VARCHAR(100) COMMENT '審批人名',
  approve_time DATETIME COMMENT '審批時間',
  approver_comment TEXT COMMENT '審批人備註',
  card_ids TEXT COMMENT '名片主鍵ID列表',
  guid VARCHAR(100) COMMENT 'GUID',
  export_status VARCHAR(20) COMMENT '導出狀態：未導出(UNEXPORTED)、已導出(EXPORTED)',

  -- 审计字段
  create_time DATETIME COMMENT '建立時間',
  update_time DATETIME COMMENT '更新時間',
  creator VARCHAR(64) COMMENT '創建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否刪除'
);
```

#### 4.1.4 导出累计统计表 (pcm_card_export_totals)
**表名**: `pcm_card_export_totals`
**说明**: 统计用户30天内的导出累计数量，用于导出限制控制

**字段详情**:
```sql
CREATE TABLE pcm_card_export_totals (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '累計記錄編號',
  user_id BIGINT COMMENT '使用者編號',
  total_count INT COMMENT '30天內累計匯出名片數量',
  period_start DATE COMMENT '統計週期開始日期',
  period_end DATE COMMENT '統計週期結束日期',

  -- 审计字段
  create_time DATETIME COMMENT '建立時間',
  update_time DATETIME COMMENT '更新時間',
  creator VARCHAR(64) COMMENT '創建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否刪除'
);
```

#### 4.1.5 公司表 (pcm_company)
**表名**: `pcm_company`
**说明**: 存储公司/机构信息，支持树形层级结构

**字段详情**:

```sql
CREATE TABLE pcm_company (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '公司ID',
  name VARCHAR(200) COMMENT '公司名称',
  parent_id BIGINT DEFAULT 0 COMMENT '父公司ID，0表示顶级公司',
  language_type VARCHAR(10) COMMENT '公司語言類型: zh-中文公司, en-英文公司',
  address VARCHAR(500) COMMENT '公司地址',
  phone VARCHAR(20) COMMENT '公司电话',

  -- 审计字段
  create_time DATETIME COMMENT '创建时间',
  update_time DATETIME COMMENT '最后更新时间',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
);
```

#### 4.1.6 商谈记录表 (pcm_meeting)
**表名**: `pcm_meeting`
**说明**: 记录与名片相关的商谈/会议信息

**字段详情**:
```sql
CREATE TABLE pcm_meeting (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商谈记录ID',
  card_id BIGINT COMMENT '名片ID',
  type VARCHAR(50) COMMENT '商谈类型',
  title VARCHAR(200) COMMENT '商谈主题',
  content TEXT COMMENT '商谈内容',
  occasion VARCHAR(200) COMMENT '会议场合',
  meeting_time VARCHAR(100) COMMENT '商谈时间',
  image_url VARCHAR(500) COMMENT '图片',
  user_name VARCHAR(100) COMMENT '商谈人姓名',
  job_title VARCHAR(100) COMMENT '商谈人职位',
  user_id VARCHAR(100) COMMENT '用户ID',
  dept_id VARCHAR(100) COMMENT '部门ID',
  create_user_id BIGINT COMMENT '创建人ID',

  -- 审计字段
  create_time DATETIME COMMENT '创建时间',
  update_time DATETIME COMMENT '最后更新时间',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
);
```

#### 4.1.7 商谈记录权限表 (pcm_meeting_permission)
**表名**: `pcm_meeting_permission`
**说明**: 控制商谈记录的查看权限

**字段详情**:
```sql
CREATE TABLE pcm_meeting_permission (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
  meeting_id BIGINT COMMENT '商谈记录ID',
  user_id BIGINT COMMENT '可见用户ID',
  dept_id BIGINT COMMENT '可见部门ID',
  role_id BIGINT COMMENT '可见角色ID',

  -- 审计字段
  create_time DATETIME COMMENT '创建时间',
  update_time DATETIME COMMENT '更新时间',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
);
```

#### 4.1.8 名片权限表 (pcm_card_permission)
**表名**: `pcm_card_permission`
**说明**: 控制名片的查看和编辑权限

**字段详情**:
```sql
CREATE TABLE pcm_card_permission (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
  card_id BIGINT COMMENT '名片ID',
  user_id BIGINT COMMENT '用户ID',
  field_name VARCHAR(1000) COMMENT '以数组格式，存储字段权限',
  is_edit BOOLEAN COMMENT '是否可编辑名片：1 可编辑, 0 不可编辑',
  dept_id BIGINT COMMENT '部门ID',
  role_id BIGINT COMMENT '角色ID',

  -- 审计字段
  create_time DATETIME COMMENT '创建时间',
  update_time DATETIME COMMENT '更新时间',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
);
```

#### 4.1.9 标签表 (pcm_tags)
**表名**: `pcm_tags`
**说明**: 存储标签信息，用于名片分类

**字段详情**:

```sql
CREATE TABLE pcm_tags (
  id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '标签ID',
  company_id BIGINT COMMENT '公司ID',
  name VARCHAR(100) COMMENT '标签名称',
  description VARCHAR(500) COMMENT '描述/备注',

  -- 审计字段
  create_time DATETIME COMMENT '创建时间',
  update_time DATETIME COMMENT '更新时间',
  creator VARCHAR(64) COMMENT '创建者',
  updater VARCHAR(64) COMMENT '更新者',
  deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除'
);
```

### 4.2 表关系图
```
Company (公司)
    ↓ 1:N
Card (名片) ←→ CardRaw (原始数据)
    ↓ 1:N        ↓ 1:N
Meeting (商谈记录) ←→ MeetingPermission (商谈权限)
    ↓
ExportRecord (导出记录) → ExportTotal (导出统计)

Card ←→ CardPermission (名片权限)
Card ←→ Tags (标签) [N:N关系通过custom_tags字段实现]
```

### 4.3 索引设计
```sql
-- 名片表索引
CREATE INDEX idx_pcm_card_group_id ON pcm_card(group_id);
CREATE INDEX idx_pcm_card_company_id ON pcm_card(company_id);
CREATE INDEX idx_pcm_card_create_time ON pcm_card(create_time);
CREATE INDEX idx_pcm_card_full_name ON pcm_card(full_name);
CREATE INDEX idx_pcm_card_status ON pcm_card(status);

-- 公司表索引
CREATE INDEX idx_pcm_company_parent_id ON pcm_company(parent_id);
CREATE INDEX idx_pcm_company_name ON pcm_company(name);

-- 商谈记录表索引
CREATE INDEX idx_pcm_meeting_card_id ON pcm_meeting(card_id);
CREATE INDEX idx_pcm_meeting_create_time ON pcm_meeting(create_time);

-- 导出记录表索引
CREATE INDEX idx_pcm_export_record_user_id ON pcm_card_export_records(user_id);
CREATE INDEX idx_pcm_export_record_status ON pcm_card_export_records(status);
CREATE INDEX idx_pcm_export_record_create_time ON pcm_card_export_records(create_time);

-- 权限表索引
CREATE INDEX idx_pcm_card_permission_card_id ON pcm_card_permission(card_id);
CREATE INDEX idx_pcm_card_permission_user_id ON pcm_card_permission(user_id);
CREATE INDEX idx_pcm_meeting_permission_meeting_id ON pcm_meeting_permission(meeting_id);
```

### 4.4 数据字典

#### 4.4.1 导出状态枚举
- **PENDING**: 待审批
- **APPROVED**: 已通过
- **REJECTED**: 已拒绝

#### 4.4.2 导出状态枚举
- **UNEXPORTED**: 未导出
- **EXPORTED**: 已导出

#### 4.4.3 名片状态枚举
- **Active**: 活跃
- **Inactive**: 非活跃

#### 4.4.4 API来源枚举
- **tencent_ocr**: 腾讯OCR
- **ali_qwen**: 阿里千问

#### 4.4.5 语言类型枚举
- **zh**: 中文
- **en**: 英文

#### 4.4.6 业务类型枚举
- **政府或相關組織**: 政府机构
- **企業**: 企业
- **基金會**: 基金会
- **非政府組織**: NGO
- **其他**: 其他类型

## 5. 前端架构

### 5.1 技术栈
- **框架**: Vue.js 2.7.14
- **UI库**: Element UI 2.15.14
- **路由**: Vue Router
- **状态管理**: Vuex
- **HTTP客户端**: Axios
- **构建工具**: Vue CLI

### 5.2 目录结构
```
src/
├── api/                    # API接口定义
│   └── pcm/               # PCM相关API
├── components/            # 公共组件
├── views/                 # 页面组件
│   └── pcm/              # PCM页面
│       ├── card/         # 名片管理
│       ├── company/      # 公司管理
│       ├── meeting/      # 商谈记录
│       └── tool/         # 工具模块
├── router/               # 路由配置
├── store/                # Vuex状态管理
├── utils/                # 工具函数
└── styles/               # 样式文件
```

### 5.3 主要页面组件

#### 5.3.1 名片管理
- **列表页**: `/views/pcm/card/index.vue`
- **详情页**: `/views/pcm/card/detail/CardDetail.vue`
- **编辑页**: `/views/pcm/card/edit/CardEdit.vue`

#### 5.3.2 公司管理
- **列表页**: `/views/pcm/company/index.vue`
- **表单组件**: `/views/pcm/company/CompanyForm.vue`

#### 5.3.3 工具模块
- **导出审批**: `/views/pcm/tool/approval/`
- **数据导入**: `/views/pcm/tool/import/`
- **回收站**: `/views/pcm/tool/recycle/`

## 6. API接口设计

### 6.1 名片管理接口 (CardController)
**基础路径**: `/pcm/card`

| 方法 | 路径 | 功能描述 | 参数 |
|------|------|----------|------|
| POST | `/page` | 分页查询名片 | CardPageReqVO |
| POST | `/getCardPageByCompanyId` | 根据公司ID获得名片分页 | CardPageReqVO |
| GET | `/get` | 获取名片详情 | id |
| POST | `/create` | 创建名片 | CardSaveReqVO |
| PUT | `/update` | 更新名片 | CardSaveReqVO |
| DELETE | `/delete` | 删除名片 | id |
| GET | `/getCardFieldNames` | 获得名片所有字段名 | - |
| POST | `/getDistinctByGroupIdPage` | 根据GroupId去重获得名片分页 | PcmCardPageReqVO |
| POST | `/batchUpdateField` | 批量更新字段 | ids, fieldName, fieldValue |
| POST | `/mergeCards` | 合并名片 | ids |
| POST | `/getCardTrash` | 获取回收站名片分页 | CardPageReqVO |
| POST | `/permanentlyDelete` | 彻底删除名片 | ids |
| POST | `/restore` | 恢复名片 | ids |
| POST | `/import` | 批量导入名片 | file, updateSupport |
| GET | `/export-excel` | 导出名片Excel | CardPageReqVO |
| GET | `/getCardsByGroudId` | 根据分组ID获取名片列表 | groupId |
| GET | `/checkCardUnique` | 检查名片是否唯一 | CardPageReqVO |

### 6.2 公司管理接口 (CompanyController)
**基础路径**: `/pcm/company`

| 方法 | 路径 | 功能描述 | 参数 |
|------|------|----------|------|
| GET | `/page` | 获得公司分页 | CompanyPageReqVO |
| GET | `/get` | 获得公司详情 | id |
| POST | `/create` | 创建公司 | CompanySaveReqVO |
| PUT | `/update` | 更新公司 | CompanySaveReqVO |
| DELETE | `/delete` | 删除公司 | id |
| GET | `/tree/{companyId}` | 获取所有子机构及名片 | companyId |
| GET | `/list-all-simple` | 获取公司精简信息列表 | type |
| GET | `/export-excel` | 导出公司Excel | CompanyPageReqVO |

### 6.3 商谈记录接口 (MeetingController)
**基础路径**: `/pcm/meeting`

| 方法 | 路径 | 功能描述 | 参数 |
|------|------|----------|------|
| POST | `/create` | 创建商谈记录 | MeetingSaveReqVO |
| PUT | `/update` | 更新商谈记录 | MeetingSaveReqVO |
| DELETE | `/delete` | 删除商谈记录 | id |
| GET | `/get` | 获得商谈记录详情 | id |
| POST | `/page` | 获得商谈记录分页 | MeetingPageReqVO |
| GET | `/getMeetingByCardId` | 根据cardId获得商谈记录 | cardId |
| GET | `/export-excel` | 导出商谈记录Excel | MeetingPageReqVO |

### 6.4 导出管理接口 (ExportController)
**基础路径**: `/pcm/export`

| 方法 | 路径 | 功能描述 | 参数 |
|------|------|----------|------|
| POST | `/request` | 提交名片导出申请 | ExportRequestVO |
| POST | `/approval-list` | 获取审批列表 | ExportPageReqVO |
| POST | `/export-list` | 获取导出列表 | ExportPageReqVO |
| POST | `/approve` | 审批导出请求 | ApprovalRequestVO |
| GET | `/approved` | 导出已审批的名片Excel | guid |
| POST | `/check-restriction` | 检查是否达到导出限制 | CardPageReqVO |

### 6.5 标签管理接口 (TagsController)
**基础路径**: `/pcm/tags`

| 方法 | 路径 | 功能描述 | 参数 |
|------|------|----------|------|
| POST | `/create` | 创建标签 | TagsSaveReqVO |
| PUT | `/update` | 更新标签 | TagsSaveReqVO |
| DELETE | `/delete` | 删除标签 | id |
| GET | `/get` | 获得标签详情 | id |
| GET | `/page` | 获得标签分页 | TagsPageReqVO |
| GET | `/getTagByCompanyId` | 根据公司ID获得标签 | companyId |
| GET | `/getTagsAll` | 获取所有标签 | TagsPageReqVO |
| GET | `/export-excel` | 导出标签Excel | TagsPageReqVO |

### 6.6 权限管理接口
**名片权限**: `/pcm/card-permission`
**商谈权限**: `/pcm/meeting-permission`

| 方法 | 路径 | 功能描述 | 参数 |
|------|------|----------|------|
| POST | `/create` | 创建权限 | PermissionSaveReqVO |
| PUT | `/update` | 更新权限 | PermissionSaveReqVO |
| DELETE | `/delete` | 删除权限 | id |
| GET | `/get` | 获得权限详情 | id |
| GET | `/page` | 获得权限分页 | PermissionPageReqVO |

## 7. 业务逻辑设计

### 7.1 核心业务服务类

#### 7.1.1 名片服务 (CardService)
**主要功能**:
- **CRUD操作**: 创建、读取、更新、删除名片
- **分页查询**: 支持多条件分页查询和排序
- **批量操作**: 批量更新字段、批量删除、批量导入
- **名片合并**: 将多张名片合并为一个分组
- **回收站管理**: 软删除、恢复、彻底删除
- **数据导出**: Excel格式导出
- **唯一性检查**: 检查名片是否重复
- **权限控制**: 基于用户和部门的数据权限

**核心方法**:
```java
// 基础CRUD
Long createCard(CardSaveReqVO cardSaveReqVO);
CommonResult<Boolean> updateCard(CardSaveReqVO updateReqVO);
void deleteCard(Long id);
CommonResult<CardRespVO> getCard(Long id);

// 分页查询
PageResult<CardRespVO> getCardPage(CardPageReqVO pageReqVO);
PageResult<CardRespVO> getCardPageByCompanyId(CardPageReqVO pageReqVO);
PageResult<Map<String, Object>> getDistrictPage(PcmCardPageReqVO pageReqVO);

// 批量操作
void batchUpdateField(List<Long> ids, String fieldName, String fieldValue);
void mergeCards(List<Long> ids);
CardImportRespVO importCardList(List<CardImportExcelVO> importCards, boolean isUpdateSupport);

// 回收站管理
PageResult<CardDO> getCardTrash(CardPageReqVO pageReqVO);
void permanentlyDelete(List<Long> ids);
void restore(List<Long> ids);

// 工具方法
List<Map<String, Object>> getTableFieldNamesWithDesc();
Long checkCardUnique(CardPageReqVO cardPageReqVO);
PageResult<CardRespVO> exportCardExcel(CardPageReqVO pageReqVO);
```

#### 7.1.2 公司服务 (CompanyService)
**主要功能**:
- **层级管理**: 支持公司树形结构管理
- **关联查询**: 获取公司及其下属名片
- **语言支持**: 支持中英文公司分类

**核心方法**:
```java
// 基础CRUD
Long createCompany(CompanySaveReqVO createReqVO);
void updateCompany(CompanySaveReqVO updateReqVO);
void deleteCompany(Long id);
CompanyDO getCompany(Long id);

// 分页和列表
CommonResult<PageResult<CompanyRespVO>> getCompanyPage(CompanyPageReqVO pageReqVO);
CommonResult<List<CompanySimpleRespVO>> getCompanyList(String type);

// 树形结构
CompanyCardDTO getCompanyTreeWithCards(Long id);
CompanyDO getCompanyByName(String name);
```

#### 7.1.3 商谈记录服务 (MeetingService)
**主要功能**:
- **记录管理**: 商谈记录的增删改查
- **关联查询**: 根据名片ID查询相关商谈记录
- **权限控制**: 基于用户和部门的查看权限

**核心方法**:
```java
// 基础CRUD
Long createMeeting(MeetingSaveReqVO createReqVO);
void updateMeeting(MeetingSaveReqVO updateReqVO);
void deleteMeeting(Long id);
MeetingDO getMeeting(Long id);

// 查询方法
CommonResult<PageResult<MeetingRespVO>> getMeetingPage(MeetingPageReqVO pageReqVO);
PageResult<MeetingDO> getMeetingByCardId(Long cardId);
Long countMeetingByUserId(Long userId);
```

#### 7.1.4 导出服务 (ExportService)
**主要功能**:
- **申请流程**: 提交导出申请
- **审批流程**: 审批导出请求
- **限制控制**: 30天内导出次数限制
- **文件生成**: 生成Excel导出文件

**核心方法**:
```java
// 导出申请和审批
CommonResult<ExportRecordDO> requestExport(Long userId, int count, String requesterComment, String cardIds);
CommonResult<ExportRecordDO> approveExport(Long recordId, Long approverId, boolean isApproved, String approverComment);

// 查询方法
ExportRecordDO getExportRecord(Long recordId);
AdminUserRespDTO getApprover(AdminUserRespDTO user);
```

### 7.2 业务流程设计

#### 7.2.1 名片导入流程
```
1. 上传Excel文件
2. 解析Excel数据
3. 数据验证和清洗
4. 检查重复名片
5. 批量插入数据库
6. 返回导入结果统计
```

#### 7.2.2 名片导出审批流程
```
1. 用户提交导出申请
2. 系统检查导出限制（30天内累计次数）
3. 生成导出记录（状态：PENDING）
4. 审批人审批（APPROVED/REJECTED）
5. 审批通过后生成Excel文件
6. 用户下载导出文件
7. 更新导出统计数据
```

#### 7.2.3 名片合并流程
```
1. 选择要合并的名片列表
2. 第一个名片作为主名片
3. 其他名片的数据合并到主名片
4. 生成统一的groupId
5. 更新所有相关名片的groupId
6. 保留所有名片记录（软合并）
```

### 7.3 权限设计

#### 7.3.1 权限模型
系统采用RBAC（基于角色的访问控制）模型：
- **用户 (User)**: 系统使用者
- **角色 (Role)**: 权限的集合
- **权限 (Permission)**: 具体的操作权限
- **菜单 (Menu)**: 前端菜单权限

#### 7.3.2 数据权限
- **名片权限**: 通过CardPermissionDO控制字段级权限
- **商谈记录权限**: 通过MeetingPermissionDO控制可见性
- **导出权限**: 基于用户角色和累计导出次数限制
- **部门权限**: 支持按部门控制数据访问范围

#### 7.3.3 字段级权限控制
```java
// 名片权限表结构
CardPermissionDO {
    Long cardId;        // 名片ID
    Long userId;        // 用户ID
    String fieldName;   // 可访问的字段列表（JSON数组）
    Boolean isEdit;     // 是否可编辑
    Long deptId;        // 部门ID
    Long roleId;        // 角色ID
}
```

## 8. 技术实现细节

### 8.1 OCR识别集成
**支持的OCR服务**:
- **腾讯OCR**: `tencent_ocr`
- **阿里千问**: `ali_qwen`

**实现流程**:
```
1. 用户上传名片图片
2. 图片存储到infra_file表
3. 调用OCR服务识别文本
4. 原始文本存储到raw_ocr_text字段
5. 结构化结果存储到ocr_result_json字段
6. 解析结构化数据填充名片字段
```

### 8.2 多语言支持
**字段设计**:
- 中文字段：`title`, `firstName`, `lastName`, `companyName`, `jobTitle`等
- 英文字段：`titleEn`, `firstNameEn`, `lastNameEn`, `companyNameEn`, `jobTitleEn`等
- 地址字段：支持中英文双语地址信息

**公司语言类型**:
- `zh`: 中文公司
- `en`: 英文公司

### 8.3 分组合并机制
**GroupId设计**:
- 每个名片都有一个`groupId`字段
- 相同`groupId`的名片属于同一个分组
- 合并时生成新的UUID作为`groupId`
- 支持分组内名片的统一管理

### 8.4 导出限制机制
**30天限制逻辑**:
```java
// 检查用户30天内导出总数
ExportTotalDO exportTotal = exportTotalMapper.selectByUserId(userId);
if (exportTotal != null && exportTotal.getTotalCount() >= 50) {
    // 需要审批
    return submitForApproval();
} else {
    // 直接导出
    return directExport();
}
```

## 9. 安全设计

### 9.1 认证授权
- **JWT Token**: 基于JWT的无状态认证
- **Spring Security**: 统一的安全框架
- **租户隔离**: 支持多租户数据隔离
- **权限注解**: 使用`@PreAuthorize`进行方法级权限控制

### 9.2 数据安全
- **SQL注入防护**: 使用MyBatis Plus参数化查询
- **XSS防护**: 前端输入验证和后端数据过滤
- **CSRF防护**: 基于Token的CSRF保护
- **敏感数据加密**: 重要字段加密存储

### 9.3 接口安全
- **请求限流**: 防止接口被恶意调用
- **参数验证**: 使用`@Valid`注解进行参数校验
- **异常处理**: 统一异常处理，避免敏感信息泄露

## 10. 性能优化

### 10.1 数据库优化
- **索引设计**: 为常用查询字段建立复合索引
- **分页查询**: 使用MyBatis Plus分页插件，避免全表扫描
- **连接池**: 使用HikariCP连接池，优化连接管理
- **查询优化**: 避免N+1查询，使用批量查询

### 10.2 缓存策略
- **Redis缓存**: 缓存热点数据和会话信息
- **本地缓存**: 使用Spring Cache注解缓存字典数据
- **查询缓存**: 缓存复杂查询结果

### 10.3 前端优化
- **懒加载**: 路由和组件按需加载
- **虚拟滚动**: 大数据量列表使用虚拟滚动
- **图片优化**: 名片图片压缩和CDN加速
- **接口优化**: 减少不必要的API调用

## 11. 部署架构

### 11.1 环境配置
**配置文件**:
- `application-local.yaml`: 本地开发环境
- `application-dev.yaml`: 开发测试环境
- `application-prod.yaml`: 生产环境

**Docker支持**:
```dockerfile
FROM openjdk:8-jre-alpine
COPY yudao-server.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 11.2 监控运维
- **日志管理**: 基于Logback的分级日志
- **健康检查**: Spring Boot Actuator端点
- **性能监控**: 集成Micrometer监控指标
- **链路追踪**: 支持分布式链路追踪

## 12. 扩展性设计

### 12.1 模块化设计
- **插件化架构**: 支持功能模块的热插拔
- **API版本控制**: 支持多版本API并存
- **国际化支持**: 多语言界面和数据
- **主题定制**: 支持UI主题切换

### 12.2 集成能力
- **OCR集成**: 支持多种OCR服务提供商
- **文件存储**: 支持本地存储、阿里云OSS、腾讯云COS
- **消息队列**: 支持Redis、RabbitMQ等消息中间件
- **第三方登录**: 支持微信、钉钉等第三方登录

## 13. 开发规范

### 13.1 代码规范
- **命名规范**: 遵循Java和JavaScript命名约定
- **注释规范**: 完善的类和方法注释
- **异常处理**: 统一的异常处理机制
- **日志规范**: 统一的日志输出格式

### 13.2 数据库规范
- **表命名**: 使用`pcm_`前缀
- **字段命名**: 使用下划线分隔的小写字母
- **索引命名**: `idx_表名_字段名`格式
- **外键约束**: 明确的外键关系定义

### 13.3 API规范
- **RESTful设计**: 遵循REST API设计原则
- **统一响应格式**: 使用CommonResult包装响应
- **错误码规范**: 统一的错误码定义
- **接口文档**: 使用Swagger生成API文档

## 14. 测试策略

### 14.1 单元测试
- **Service层测试**: 业务逻辑单元测试
- **Mapper层测试**: 数据访问层测试
- **工具类测试**: 公共工具方法测试

### 14.2 集成测试
- **API测试**: 接口功能和性能测试
- **数据库测试**: 数据一致性和事务测试
- **权限测试**: 访问控制和数据权限测试

## 15. 总结

PCM系统是一个功能完善的名片管理系统，具有以下特点：

### 15.1 技术特色
- **现代化架构**: Spring Boot + Vue.js前后端分离
- **多语言支持**: 中英文双语名片管理
- **智能识别**: OCR自动识别名片信息
- **权限控制**: 细粒度的数据权限管理
- **审批流程**: 完整的导出审批机制

### 15.2 业务价值
- **提高效率**: 自动化名片信息录入和管理
- **数据安全**: 完善的权限控制和审批流程
- **易于扩展**: 模块化设计支持功能扩展
- **用户友好**: 直观的界面和便捷的操作流程

### 15.3 发展方向
- **AI增强**: 集成更多AI能力提升识别准确率
- **移动端**: 开发移动端应用支持
- **云原生**: 向云原生架构演进
- **大数据**: 集成大数据分析能力

这份详细的设计文档为PCM系统的开发、维护和扩展提供了全面的技术指导，确保系统的稳定性、安全性和可扩展性。
