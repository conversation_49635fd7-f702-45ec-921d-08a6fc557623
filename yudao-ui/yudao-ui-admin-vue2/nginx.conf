server {
    #listen       81 default_server;
    listen       81;
    #server_name  ************; ## 重要！！！修改成你的外网 IP/域名 prod
    #server_name  ************; ## 重要！！！修改成你的外网 IP/域名 uat
    server_name  ************; ## 重要！！！修改成你的外网 IP/域名 dev

    gzip on;
    gzip_min_length 1k;     # 设置允许压缩的页面最小字节数
    gzip_buffers 4 16k;     # 用来存储 gzip 的压缩结果
    gzip_http_version 1.1;  # 识别 HTTP 协议版本
    gzip_comp_level 2;      # 设置 gzip 的压缩比 1-9。1 压缩比最小但最快，而 9 相反
    gzip_types text/plain application/x-javascript text/css application/xml application/javascript; # 指定压缩类型
    gzip_proxied any;       # 无论后端服务器的 headers 头返回什么信息，都无条件启用压缩
    client_max_body_size 10M; # 设置为所需大小，例如 50MB

    location / { ## 前端项目
        root   /usr/share/nginx/html/;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    location /prod-api/ { ## 后端项目 - 管理后台
        #proxy_pass http://************:58080/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
        #proxy_pass http://************:58080/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
        proxy_pass http://**************:58080/; ## 重要！！！proxy_pass 需要设置为后端项目所在服务器的 IP
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
