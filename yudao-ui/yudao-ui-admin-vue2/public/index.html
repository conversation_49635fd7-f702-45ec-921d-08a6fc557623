<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="referrer" content="no-referrer">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title><%= webpackConfig.name %></title>
  <!--[if lt IE 11]><script>window.location.href='html/ie.html';</script><![endif]-->
  <style>
    html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
      font-family: 'PingFang HK', 'Noto Sans TC', 'Microsoft YaHei', sans-serif;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    /* 加载器容器 */
    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 999999;
    }

    /* 加载内容容器 */
    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 32px;
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.07);
      max-width: 420px;
      width: 90%;
    }

    /* Logo样式 */
    .loading-logo {
      width: 320px;
      height: auto;
      filter: drop-shadow(0 8px 12px rgba(0, 0, 0, 0.05));
      transition: transform 0.3s ease;
    }
    
    .loading-logo:hover {
      transform: scale(1.03);
    }

    /* 水平滚动圆点容器 */
    .loading-dots {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      width: 100%;
    }

    /* 圆点样式 */
    .dot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: rgb(237 108 0 / var(--tw-bg-opacity, 1)); /* 蓝色主色调 */
      animation: wave 1.4s infinite ease-in-out both;
    }

    /* 为不同圆点设置不同的动画延迟 */
    .dot:nth-child(1) { animation-delay: -0.32s; }
    .dot:nth-child(2) { animation-delay: -0.16s; }
    .dot:nth-child(3) { animation-delay: 0s; }

    /* 加载完成时的动画 */
    .loaded #loader-wrapper {
      opacity: 0;
      visibility: hidden;
      transition: all 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
    }

    /* 波浪动画 */
    @keyframes wave {
      0%, 80%, 100% { transform: scale(0.6); opacity: 0.5; }
      40% { transform: scale(1.0); opacity: 1; }
    }
  </style>
</head>
<body>
  <div id="app">
    <div id="loader-wrapper">
      <div class="loading-content">
        <!-- 载入logo -->
        <img src="./org.png" alt="Organization Logo" class="loading-logo">
        
        <!-- 水平排列的圆点加载动画 -->
        <div class="loading-dots">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
    