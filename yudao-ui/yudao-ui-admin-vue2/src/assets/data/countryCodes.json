[{"name": "中國大陸", "name_en": "China", "name_tw": "中國大陸", "code": "CN", "dial_code": "+86", "phone_pattern": "^1[3-9]\\d{9}$", "example": "13800138000"}, {"name": "中國香港", "name_en": "Hong Kong", "name_tw": "中國香港", "code": "HK", "dial_code": "+852", "phone_pattern": "^[2-9]\\d{7,8}$", "example": "21234567"}, {"name": "中國澳門", "name_en": "Macau", "name_tw": "中國澳門", "code": "MO", "dial_code": "+853", "phone_pattern": "^[6]\\d{7}$", "example": "66123456"}, {"name": "中國台灣", "name_en": "Taiwan", "name_tw": "中國台灣", "code": "TW", "dial_code": "+886", "phone_pattern": "^[0-9]{8,9}$", "example": "*********"}, {"name": "美國/加拿大", "name_en": "United States/Canada", "name_tw": "美國/加拿大", "code": "US", "dial_code": "+1", "phone_pattern": "^[2-9]\\d{9}$", "example": "2015550123"}, {"name": "日本", "name_en": "Japan", "name_tw": "日本", "code": "JP", "dial_code": "+81", "phone_pattern": "^0[789]0\\d{8}$", "example": "09012345678"}, {"name": "韓國", "name_en": "South Korea", "name_tw": "韓國", "code": "KR", "dial_code": "+82", "phone_pattern": "^01[016789]\\d{7,8}$", "example": "***********"}, {"name": "新加坡", "name_en": "Singapore", "name_tw": "新加坡", "code": "SG", "dial_code": "+65", "phone_pattern": "^[689]\\d{7}$", "example": "81234567"}, {"name": "英國", "name_en": "United Kingdom", "name_tw": "英國", "code": "GB", "dial_code": "+44", "phone_pattern": "^[1-9]\\d{9,10}$", "example": "7400123456"}, {"name": "澳大利亞", "name_en": "Australia", "name_tw": "澳大利亞", "code": "AU", "dial_code": "+61", "phone_pattern": "^[2-478]\\d{8}$", "example": "*********"}, {"name": "馬來西亞", "name_en": "Malaysia", "name_tw": "馬來西亞", "code": "MY", "dial_code": "+60", "phone_pattern": "^1[0-9]{8,9}$", "example": "*********"}, {"name": "泰國", "name_en": "Thailand", "name_tw": "泰國", "code": "TH", "dial_code": "+66", "phone_pattern": "^[689]\\d{8}$", "example": "*********"}, {"name": "越南", "name_en": "Vietnam", "name_tw": "越南", "code": "VN", "dial_code": "+84", "phone_pattern": "^[1-9]\\d{8,9}$", "example": "*********"}, {"name": "印度", "name_en": "India", "name_tw": "印度", "code": "IN", "dial_code": "+91", "phone_pattern": "^[6789]\\d{9}$", "example": "7*********"}, {"name": "德國", "name_en": "Germany", "name_tw": "德國", "code": "DE", "dial_code": "+49", "phone_pattern": "^[1-9]\\d{9,11}$", "example": "***********"}, {"name": "法國", "name_en": "France", "name_tw": "法國", "code": "FR", "dial_code": "+33", "phone_pattern": "^[67]\\d{8}$", "example": "612345678"}, {"name": "俄羅斯", "name_en": "Russia", "name_tw": "俄羅斯", "code": "RU", "dial_code": "+7", "phone_pattern": "^[3489]\\d{9}$", "example": "*********9"}, {"name": "巴西", "name_en": "Brazil", "name_tw": "巴西", "code": "BR", "dial_code": "+55", "phone_pattern": "^[1-9]{2}9?[6-9]\\d{7}$", "example": "11987654321"}, {"name": "菲律賓", "name_en": "Philippines", "name_tw": "菲律賓", "code": "PH", "dial_code": "+63", "phone_pattern": "^[89]\\d{9}$", "example": "*********9"}, {"name": "印尼", "name_en": "Indonesia", "name_tw": "印尼", "code": "ID", "dial_code": "+62", "phone_pattern": "^[28]\\d{9,11}$", "example": "*********9"}]