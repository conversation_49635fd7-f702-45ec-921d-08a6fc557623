// localRoutes.js
export const localRoutes = {
    Card: {
      path: "card",
      component: () => import('@/views/pcm/card/index.vue'),
      name: "Card",
      meta: {
        icon: require('@/assets/pcm/left-bar/card.png'),
        activeIcon: require('@/assets/pcm/left-bar/card_selected.png'),
        title: 'Card'
      }
    },
    Company: {
      path: "company",
      component: () => import('@/views/pcm/company/index.vue'),
      name: "Company",
      meta: {
        icon: require('@/assets/pcm/left-bar/company.png'),
        activeIcon: require('@/assets/pcm/left-bar/company_selected.png'),
        title: 'Company'
      }
    },
    Meeting: {
      path: "meeting",
      component: () => import('@/views/pcm/meeting/index.vue'),
      name: "Meeting",
      meta: {
        icon: require('@/assets/pcm/left-bar/chatLog.png'),
        activeIcon: require('@/assets/pcm/left-bar/chatLog_selected.png'),
        title: 'Meeting'
      }
    },
    Colleague: {
      path: "colleague",
      component: () => import('@/views/pcm/colleague/index.vue'),
      name: "Colleague",
      meta: {
        icon: require('@/assets/pcm/left-bar/colleague.png'),
        activeIcon: require('@/assets/pcm/left-bar/colleague_selected.png'),
        title: 'Colleague'
      }
    },
    Setting: {
      path: "setting",
      component: () => import('@/views/pcm/setting/index.vue'),
      name: "Setting",
      meta: {
        icon: require('@/assets/pcm/left-bar/setting.png'),
        activeIcon: require('@/assets/pcm/left-bar/setting_selected.png'),
        title: 'Setting'
      }
    },
    Tool: {
      path: "tool",
      hidden: true,
      component: () => import('@/views/pcm/tool/index.vue'),
      name: "Tool",
      meta: {
        title: 'Tool'
      }
    }
  }