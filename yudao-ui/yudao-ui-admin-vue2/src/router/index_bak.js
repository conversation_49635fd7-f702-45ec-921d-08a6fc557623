import Vue from 'vue'
import Router from 'vue-router'
import Layout from "@/views/pcm/Dashboard.vue";
Vue.use(Router)

// 公共路由
export const constantRoutes = [{
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path(.*)',
      component: (resolve) => require(['@/views/redirect'], resolve)
    }]
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login.vue'], resolve),
    hidden: true
  },
  {
    path: "/",
    component: Layout,
    redirect: '/card',
    hidden: true,
    children: [{
        //一级菜单，从数据库拉取匹配
        path: "card",
        component: (resolve) => require(['@/views/pcm/card/index.vue'], resolve),
        name: "Card",
        meta: {
          icon: require('@/assets/pcm/left-bar/card.png'),
          activeIcon: require('@/assets/pcm/left-bar/card_selected.png'),
          title: 'Card' // 使用翻译键
        },
      },
      {
        path: "company",
        component: (resolve) => require(['@/views/pcm/company/index.vue'], resolve),
        name: "Company",
        meta: {
          icon: require('@/assets/pcm/left-bar/company.png'),
          activeIcon: require('@/assets/pcm/left-bar/company_selected.png'),
          title: 'Company' // 使用翻译键
        }
      },
      {
        path: "meeting",
        component: (resolve) => require(['@/views/pcm/meeting/index.vue'], resolve),
        name: "Meeting",
        meta: {
          icon: require('@/assets/pcm/left-bar/chatLog.png'),
          activeIcon: require('@/assets/pcm/left-bar/chatLog_selected.png'),
          title: 'Meeting' // 使用翻译键
        }
      },
      {
        path: "colleague",
        component: (resolve) => require(['@/views/pcm/colleague/index.vue'], resolve),
        name: "Colleague",
        meta: {
          icon: require('@/assets/pcm/left-bar/colleague.png'),
          activeIcon: require('@/assets/pcm/left-bar/colleague_selected.png'),
          title: 'Colleague' // 使用翻译键
        }
      },
      {
        path: "setting",
        component: (resolve) => require(['@/views/pcm/setting/index.vue'], resolve),
        name: "Setting",
        meta: {
          icon: require('@/assets/pcm/left-bar/setting.png'),
          activeIcon: require('@/assets/pcm/left-bar/setting_selected.png'),
          title: 'Setting' // 使用翻译键
        }
      },
      {
        path: 'tool',
        hidden: true,
        component: (resolve) => require(['@/views/pcm/tool/index.vue'], resolve),
        name: 'Tool',
        meta: {
          title: 'Tool' // 使用翻译键
        }
      },
      //以下是二级菜单，隐藏掉
      {
        path: 'card/detail/:cardId(\\d+)',
        hidden: true,
        component: (resolve) => require(['@/views/pcm/card/detail/CardDetail'], resolve),
        name: 'CardDetail',
        meta: {
          title: 'CardDetail' // 使用翻译键
        }
      },
      {
        path: 'card/edit/:cardId(\\d+)',
        hidden: true,
        component: (resolve) => require(['@/views/pcm/card/edit/CardEdit'], resolve),
        name: 'CardEdit',
        meta: {
          title: 'CardEdit' // 使用翻译键
        }
      },
      {
        path: 'message',
        hidden: true,
        component: (resolve) => require(['@/views/pcm/msg/index.vue'], resolve),
        name: 'Message',
        meta: {
          title: 'Message' // 使用翻译键
        }
      },
    ]
  }
]

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}

export default new Router({
  base: process.env.VUE_APP_APP_NAME ? process.env.VUE_APP_APP_NAME : "/",
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})