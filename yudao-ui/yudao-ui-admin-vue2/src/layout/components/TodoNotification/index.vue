<template>
  <transition name="fade-slide-up">
    <div v-if="visible" class="ios-notification">
      <button class="close-btn" @click="handleIgnore">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M1 1L11 11M1 11L11 1" stroke="#8E8E93" stroke-width="1.5" stroke-linecap="round"/>
        </svg>
      </button>
      <div class="notification-content">
        <div class="notification-header">
          <h3>{{ $t('todoNotification.title') }}</h3>
        </div>
        <div class="notification-body">
          <div v-html="displayMessage"></div>
        </div>
        <div class="notification-actions">
          <button class="secondary-btn" @click="handleIgnore">{{ $t('todoNotification.ignore') }}</button>
          <button class="primary-btn" @click="handleAction">{{ $t('todoNotification.takeAction') }}</button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'IOSNotification',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    message: {
      type: [String, Object],
      default: ''
    },
    actionRoute: {
      type: String,
      default: '/'
    }
  },
  computed: {
    displayMessage() {
      if (typeof this.message === 'string') {
        return this.message
      }
      return this.message[this.$i18n.locale] || this.message['en'] || ''
    }
  },
  methods: {
    handleAction() {
      this.$emit('action')
      this.$router.push(this.actionRoute)
    },
    handleIgnore() {
      this.$emit('ignore')
    }
  }
}
</script>

<style scoped>
.ios-notification {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 320px;
  max-width: calc(100% - 40px);
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 24px;
  height: 24px;
  border-radius: 12px;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  z-index: 1;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

.notification-content {
  padding: 20px;
}

.notification-header {
  margin-bottom: 12px;
}

.notification-header h3 {
  margin: 0;
  font-size: 17px;
  font-weight: 600;
  color: #000000;
  text-align: center;
}

.notification-body {
  margin-bottom: 20px;
}

.notification-body div {
  margin: 0;
  font-size: 15px;
  line-height: 1.4;
  color: #3C3C43;
  opacity: 0.8;
  text-align: center;
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.primary-btn, .secondary-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.primary-btn {
  background: #007AFF;
  color: #FFFFFF;
}

.primary-btn:hover {
  background: #0062CC;
}

.secondary-btn {
  background: rgba(0, 0, 0, 0.04);
  color: #007AFF;
}

.secondary-btn:hover {
  background: rgba(0, 0, 0, 0.08);
}

/* 动画效果 */
.fade-slide-up-enter-active, .fade-slide-up-leave-active {
  transition: all 0.5s cubic-bezier(0.22, 1, 0.36, 1);
}

.fade-slide-up-enter, .fade-slide-up-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 移动端适配 */
@media (max-width: 576px) {
  .ios-notification {
    right: 10px;
    bottom: 10px;
    width: calc(100% - 20px);
  }
  
  .notification-actions {
    flex-direction: column;
  }
}
</style>