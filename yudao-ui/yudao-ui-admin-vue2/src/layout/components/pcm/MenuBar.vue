<template>
  <div class="menu-bar" :class="{ collapsed: isCollapsed }">
    <div class="menu-header hidden-xs-only">
      <span class="menu-title">{{ $t("menu") }}</span>
      <button class="toggle-button" @click="toggleMenu">
        <i
          class="arrow-icon"
          :class="{
            'el-icon-arrow-left': !isCollapsed,
            'el-icon-arrow-right': isCollapsed,
          }"
        ></i>
      </button>
    </div>
    <div class="menu-header hidden-sm-and-up">
      <div class="mobile-header">
        <div class="left-logo">
          <img
            class="img-instance"
            src="@/assets/pcm/top-bar/logo.png"
            alt=""
          />
        </div>
        <div @click="closeSideMenuFn" class="right-close-icon">
          <i class="el-icon-close" :style="{ fontSize: '30px' }"></i>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="menu-items">
      <router-link
        v-for="item in menuItems"
        :key="item.path"
        :to="item.fullPath"
        class="menu-item"
        :class="{ active: $route.path.includes(item.fullPath) }"
      >
        <!-- 左侧图标和文字 -->
        <div class="menu-left">
          <img
            class="menu-icon"
            :src="
              $route.path.includes(item.fullPath)
                ? item.meta.activeIcon
                : item.meta.icon
            "
          />
          <span class="menu-text" v-if="!isCollapsed">
            {{ $t(item.meta.title) }}
          </span>
        </div>
        <!-- 右侧箭头图标 -->
        <i
          class="el-icon-arrow-right"
          :class="{ 'active-arrow': $route.path.includes(item.fullPath) }"
          v-if="!isCollapsed"
        />
      </router-link>
    </div>

    <TodoNotification
      v-if="showNotification"
      :visible="showNotification"
      :message="notificationMessage"
      :action-route="'/tool?menuName=exportApproval'"
      @action="hideNotification"
      @ignore="hideNotification"
    />
  </div>
</template>

<script>
import TodoNotification from "../TodoNotification/index.vue";
import { notification } from "@/api/pcm/approval";
export default {
  name: "MenuBar",
  components: {
    TodoNotification,
  },
  mounted() {
    this.checkTodoNotification();
  },
  data() {
    return {
      isCollapsed: false, // 控制菜单是否收起
      showNotification: false,
      notificationMessage: "",
    };
  },
  computed: {
    menuItems() {
      // 从 Vuex 获取路由数据
      const dynamicRoutes =
        this.$store.state.permission.routes.find((route) => route.path === "/")
          ?.children || [];

      return dynamicRoutes
        .filter((child) => !child.hidden)
        .map((child) => ({
          ...child,
          fullPath: child.path.startsWith("/") ? child.path : `/${child.path}`,
        }));
    },
  },
  methods: {
    toggleMenu() {
      this.isCollapsed = !this.isCollapsed;
    },
    collapseMenu() {
      // 点击菜单项时收起菜单
      this.isCollapsed = true;
    },
    closeSideMenuFn() {
      this.$emit("closeSideBarFn");
    },
    async checkTodoNotification() {
      try {
        const hasExportPermiss = this.$auth.hasPermi("pcm:card:export");
        const response = await notification();
        const approvalCount = response.data.approvalCount;
        const exportCount = response.data.exportCount;
        if (approvalCount != 0) {
          this.notificationMessage =
            "您有" + approvalCount + "條名片導出申請待審批;<br>";
        }
        if (exportCount != 0) {
          this.notificationMessage =
            this.notificationMessage +
            "您有" +
            exportCount +
            "條名片導出申請待導出;<br>";
        }
        if (approvalCount != 0 || exportCount != 0) {
          setTimeout(() => {
            this.showNotification = true;
          }, 1000); // 1秒后显示通知
        }
      } catch (error) {
        console.error("获取待办事项通知失败:", error);
      }
    },
    hideNotification() {
      this.showNotification = false;
    },
  },
};
</script>

<style scoped>
.mobile-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.left-logo {
  flex: 1;
}
.img-instance {
  object-fit: contain;
  width: 100%;
  height: 100%;
}
.right-close-icon {
  width: 44px;
  display: flex;
  align-items: center;
  /* height: 44px; */
}
.menu-bar {
  width: 240px;
  background-color: #ffffff;
  margin: 10px;
  padding: 5px;
  border-radius: 16px;
  font-weight: 400;
  transition: width 0.3s ease;
  overflow: hidden;
}

.menu-bar.collapsed {
  width: 84px;
}

.menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
}

.menu-title {
  font-size: 14px;
  font-weight: 400;
}

.toggle-button {
  background: none;
  border: none;
  cursor: pointer;
}

.arrow-icon {
  font-size: 20px;
  color: #999999;
}

.divider {
  border-bottom: 0.1px solid #ccc;
  margin: 5px 0;
}

.menu-items {
  padding: 10px;
}

.menu-item {
  display: flex;
  align-items: center;
  height: 50px;
  padding: 5px 10px;
  margin: 5px 0;
  border-radius: 12px;
  text-decoration: none;
  font-size: 16px;
  color: #333;
  background-color: #ffffff;
  justify-content: space-between;
  transition: background-color 0.3s ease, padding 0.3s ease;
}

.menu-item.active {
  background-color: #ed6c00;
  color: #ffffff;
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  object-fit: contain;
  display: block;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  flex-shrink: 0;
  transition: margin 0.3s ease;
}

.menu-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.menu-bar.collapsed .menu-item span {
  display: none;
}

.menu-bar.collapsed .menu-icon {
  margin: 0 auto;
  position: static;
  transform: none;
}

.menu-text {
  transition: opacity 0.3s ease, width 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-icon-arrow-right {
  color: #999999;
  font-size: 16px;
  transition: color 0.3s ease;
}

.active-arrow {
  color: #ffffff !important;
}
</style>