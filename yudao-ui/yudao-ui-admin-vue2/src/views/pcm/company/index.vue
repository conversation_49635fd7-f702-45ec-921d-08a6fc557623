<template>
  <div class="container">
    <!-- 第一列：公司列表 （不是移动端可以显示，或者当前的步骤currentSetup为1）-->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 1)"
      class="card company-list"
    >
      <div class="card-header">
        <div class="title">{{ $t("allCompanies") }}</div>
      </div>
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          :placeholder="$t('enterCompanyName')"
          clearable
          @input="filteredCompanies"
        >
          <template #append>
            <el-button
              slot="append"
              type="success"
              icon="el-icon-search"
            ></el-button>
          </template>
        </el-input>
      </div>
      <div class="company-items">
        <el-radio-group
          @change="handleChangeCompanyFn"
          v-model="selectedCompany"
        >
          <el-radio
            v-for="company in filteredCompanies"
            :key="company.id"
            :label="company.id"
            class="company-item"
          >
            {{ company.name }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <!-- 第二列：组织结构和名片列表 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 2)"
      class="card company-details"
    >
      <div class="card-header">
        <div v-if="!isEditingCompanyName" class="company-title">
          <img
            src="@/assets/pcm/card-detail/back.png"
            alt="返回箭头"
            class="back-arrow hidden-sm-and-up"
            @click="goBack()"
          />
          <div class="title">{{ selectedCompanyName }}</div>
          <div class="company-actions">
            <el-button type="text"  v-hasPermi="['pcm:company:update']" @click="startEditingCompanyName">
              <i class="el-icon-edit"></i>
            </el-button>
            <el-button
            v-hasPermi="['pcm:company:delete']"
              type="text"
              @click="confirmDeleteCompany"
              style="color: #f56c6c"
            >
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
        </div>
        <div v-else class="company-edit">
          <el-input v-model="editedCompanyName" />
          <el-button type="primary" @click="saveCompanyName">{{
            $t("save")
          }}</el-button>
          <el-button @click="cancelEditingCompanyName">{{
            $t("cancel")
          }}</el-button>
        </div>
      </div>
      <!-- 移动端名片创建者 -->
      <div class="hidden-sm-and-up">
        <div class="card-header">
          <div class="title">{{ $t("cardCreator") }}</div>
        </div>
        <div class="mobile-creator-container">
          <div
            v-for="creator in creatorStats"
            :key="creator.id"
            class="creator-item"
          >
            <div class="creator-info">
              <el-avatar :src="require('@/assets/pcm/top-bar/profile.png')" />
              <span class="creator-name">{{ creator.creator }}</span>
            </div>
            <span class="creator-count"
              >{{ creator.cards }} {{ $t("businessCardsUnit") }}</span
            >
          </div>
        </div>
      </div>
      <!-- 移动端名片创建者 -->
      <el-tabs v-model="activeTab" class="tabs" @tab-click="handlePageChange">
        <el-tab-pane :label="$t('organization')" name="structure">
          <div class="company-name-style hidden-sm-and-up">
            {{ selectedCompanyName }}
          </div>
          <el-tree
            :data="companyStructure"
            :props="treeProps"
            :expand-on-click-node="false"
            node-key="id"
            :default-expand-all="true"
          >
            <template slot-scope="{ node, data }">
              <span class="tree-node">
                <i
                  v-if="data.type === 'company'"
                  class="el-icon-office-building"
                ></i>
                <i v-else class="el-icon-user"></i>
                <span>{{ data.name }}</span>
              </span>
            </template>
          </el-tree>
        </el-tab-pane>
        <el-tab-pane :label="$t('businessCards')" name="cards">
          <el-table
            empty-text="暫無數據"
            :header-cell-style="{ background: '#1EA2350D' }"
            class="table"
            :border="true"
            size="small"
            :fit="true"
            v-loading="loading"
            :data="cardList"
            :span-method="handleSpanMethod"
            @row-click="handleRowClick"
            style="width: 100%"
          >
            <el-table-column prop="image" :label="$t('cardImage')" width="200">
            
               <template #default="scope">
              <SafeImage
                :src="scope.row.imageUrl"
                alt="card-image"
                class="avatar"
              />
            </template>
            </el-table-column>
            <el-table-column  :label="$t('name')" width="120" >
                <template #default="scope">
              <div :style="{ lineHeight: '16px' }">
                <p>
                  {{ (scope.row.firstName ?? "") + (scope.row.lastName ?? "") }}
                </p>
                <p>
                  {{
                    (scope.row.lastNameEn ?? "") +
                    " " +
                    (scope.row.firstNameEn ?? "")
                  }}
                </p>
              </div>
            </template>
            </el-table-column>
            <el-table-column :label="$t('companyPosition')" width="180">
              <template #default="scope">
                <div>
                  {{ scope.row.companyName || scope.row.companyNameEn }} <br />
                  {{ scope.row.jobTitle }}
                </div>
              </template></el-table-column
            >
            <el-table-column prop="phoneMobile" :label="$t('contactInfo')" />
            <el-table-column
              prop="creator"
              :label="$t('creator')"
              width="120"
            />
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getCardList"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 第三列：名片创建者统计 -->
    <div class="card creator-stats hidden-xs-only">
      <div class="card-header">
        <div class="title">{{ $t("cardCreator") }}</div>
      </div>
      <div class="creator-list">
        <div
          v-for="creator in creatorStats"
          :key="creator.id"
          class="creator-item"
        >
          <div class="creator-info">
            <el-avatar :src="require('@/assets/pcm/top-bar/profile.png')" />
            <span class="creator-name">{{ creator.creator }}</span>
          </div>
          <span class="creator-count"
            >{{ creator.cards }} {{ $t("businessCardsUnit") }}</span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as CompanyApi from "@/api/pcm/company";
import {
  getCardPageByCompanyId,
  countCardsByCreatorInDept,
} from "@/api/pcm/card";
export default {
  name: "CompanyCardView",
  watch: {
    selectedCompany: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.getCompanyTree(newVal);
          this.refreshTabel(newVal);
          this.cardStatistics(newVal);
        }
      },
    },
  },
  data() {
    return {
      // 模拟公司数据
      companies: [],
      searchQuery: "", // 搜索关键词
      selectedCompany: null, // 选中的公司ID
      isEditingCompanyName: false, // 是否正在编辑公司名称
      editedCompanyName: "", // 编辑的公司名称
      activeTab: "structure", // 当前选中的tab
      // 组织结构数据
      companyStructure: [],
      treeProps: {
        label: "name",
        children: "children",
      },
      // 名片列表数据
      // 总条数
      total: 0,
      loading: false,
      cardList: [],
      pageSize: 10, // 每页显示的名片数量
      // 名片创建者统计数据
      creatorStats: [],
      currentSetup: 1,
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        keyword: "",
        companyId: null, //公司id
        orderBy: "createTime|desc",
      },
    };
  },
  computed: {
    // 过滤后的公司列表
    filteredCompanies() {
      return this.companies.filter((company) =>
        company.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    },
    // 选中的公司名称
    selectedCompanyName() {
      const company = this.companies.find((c) => c.id === this.selectedCompany);
      return company ? company.name : "";
    },
  },
  created() {
    this.getList();
  },
  methods: {
    // 删除公司确认
    confirmDeleteCompany() {
      this.$confirm(
        this.$t("companyManagement.confirmDeleteCompany"),
        this.$t("companyManagement.warning"),
        {
          confirmButtonText: this.$t("companyManagement.deleteButton"),
          cancelButtonText: this.$t("companyManagement.cancelButton"),
          type: "warning",
        }
      )
        .then(() => {
          this.deleteCompany();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: this.$t("companyManagement.deleteCancelled"),
          });
        });
    },

    // 执行删除公司
   async deleteCompany() {
  try {
    await CompanyApi.deleteCompany(this.selectedCompany);
    this.$message.success(this.$t('companyManagement.deleteSuccess'));
    
    await this.getList();
    
    if (this.companies.length > 0) {
      this.selectedCompany = this.companies[0].id;
    } else {
      this.selectedCompany = null;
    }
  } catch (error) {
    this.$message.error(this.$t('companyManagement.deleteFailed'));
  }
},

    goBack() {
      this.currentSetup = 1;
    },
    handleChangeCompanyFn(e) {
      if (!this.$isMobile()) return;
      this.currentSetup = 2;
    },
    confirmCurrentCompanyFn() {
      this.selectedCompany = null;
    },
    // 开始编辑公司名称
    startEditingCompanyName() {
      this.editedCompanyName = this.selectedCompanyName;
      this.isEditingCompanyName = true;
    },
    // 保存公司名称
    saveCompanyName() {
      const company = this.companies.find((c) => c.id === this.selectedCompany);
      if (company) {
        //调用api update
        company.name = this.editedCompanyName;
        CompanyApi.updateCompany(company).then((res) => {
          this.isEditingCompanyName = false;
        });
      }
    },
    // 取消编辑公司名称
    cancelEditingCompanyName() {
      this.isEditingCompanyName = false;
    },
    // 分页切换
    handlePageChange(page) {
      console.log("切换到第", page.index, "页");
      if (page.index === "1") {
        this.refreshTabel(this.selectedCompany);
      }
    },
    refreshTabel(companyId) {
      //加载名片表格
      this.queryParams.companyId = companyId;
      this.queryParams.pageNo = 1;
      this.getCardList();
    },
    // 获取标签列表
    async getList() {
      // 执行查询
      CompanyApi.getCompanyPage().then((response) => {
        this.companies = response.data.list;
        if (this.companies.length > 0) {
          this.selectedCompany = this.companies[0].id;
        }
      });
    },
    getCardList() {
      this.loading = true;
      // 执行查询
      getCardPageByCompanyId(this.queryParams).then((response) => {
        this.cardList = response.data.list;
        this.total = response.data.total;
        this.loading = false;
        this.computeSpanData(); // 计算合并数据
      });
    },

    //名片创建信息统计
    cardStatistics(companyId) {
      countCardsByCreatorInDept(companyId).then((res) => {
        this.creatorStats = res.data;
      });
    },
    //获取公司组织结构
    getCompanyTree(companyId) {
      CompanyApi.getCompanyTree(companyId).then((res) => {
        this.companyStructure = [];
        this.companyStructure.push(res.data);
      });
    },
    handleRowClick(row, column, event) {
      this.$router.push({ name: "CardDetail", params: { cardId: row.id } });
    },
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 只对 fullName 列进行合并 （只合并PC端）
      if (columnIndex === 1 && !this.$isMobile()) {
        // fullName 列的索引是 1（从 0 开始）
        const rowspan = this.spanArr[rowIndex];
        const colspan = rowspan > 0 ? 1 : 0;
        return {
          rowspan: rowspan,
          colspan: colspan,
        };
      }
    },
    // 计算需要合并的行
    computeSpanData() {
      // 用于存储合并信息的数组
      this.spanArr = [];
      // 记录当前位置
      let pos = 0;
      // 遍历数据，计算相同 groupId 的行数
      for (let i = 0; i < this.cardList.length; i++) {
        if (i === 0) {
          // 第一行默认合并 1 行
          this.spanArr.push(1);
          pos = 0;
        } else {
          // 如果当前行的 groupId 和上一行相同
          if (this.cardList[i].groupId === this.cardList[i - 1].groupId) {
            // 上一行的合并数 +1
            this.spanArr[pos] += 1;
            // 当前行的合并数设为 0（表示不显示）
            this.spanArr.push(0);
          } else {
            // 不同 groupId，合并数为 1
            this.spanArr.push(1);
            pos = i;
          }
        }
      }
    },
  },
  mounted() {
    this.confirmCurrentCompanyFn();
  },
};
</script>

<style scoped>
.mobile-creator-container {
  max-height: 30vh;
  overflow-y: scroll;
}

.company-name-style {
  font-size: 16px;
  font-weight: 700;
}

.back-arrow {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}

.container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
  gap: 10px;
}

.card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow-y: auto;
}

.company-list {
  flex: 1;
  overflow-y: hidden;
}

.company-details {
  flex: 2;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.creator-stats {
  flex: 1;
}

.card-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-box {
  margin-bottom: 20px;
}

.company-items {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.company-item {
  display: block;
  margin-bottom: 20px;
}

.company-item /deep/ .el-radio__label {
  font-size: 16px !important;
  color: #1f1f1f;
}

.company-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  width: 100%;
}

.company-title-text {
  margin: 0;
  margin-right: 10px;
}

.company-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.company-actions .el-button {
  padding: 0;
  margin-left: 10px;
}

.company-edit {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.company-edit .el-input {
  flex: 1;
}

.tabs {
  margin-top: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tabs >>> .el-tabs__content {
  flex: 1;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  font-size: 16px;
  align-items: center;
}

.tree-node i {
  margin-right: 5px;
}

.creator-list {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.creator-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e0e0e0;
}

.creator-info {
  display: flex;
  align-items: center;
}

.creator-name {
  margin-left: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #1f1f1f;
}

.creator-count {
  font-weight: 400;
  color: #666666;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .container {
    flex-direction: column;
    gap: 10px;
  }

  .company-list,
  .company-details,
  .creator-stats {
    width: 100%;
    flex: none;
  }

  .company-details {
    height: auto;
  }

  .card-header {
    padding: 10px 0;
  }

  .company-edit {
    flex-direction: column;
    align-items: flex-start;
  }

  .company-edit .el-input {
    width: 100%;
    margin-bottom: 10px;
  }

  .company-edit .el-button {
    align-self: flex-end;
  }
}

/* 表格样式 */
.table {
  width: 100%;
  margin-top: 10px;
}
.avatar {
  width: 80px;
  height: 50px;
  margin-left: 15px;
  object-fit: contain;
}
.table >>> .el-table__body-wrapper {
  overflow-x: auto;
}

/* 删除按钮悬停效果 */
.el-button--text:hover {
  opacity: 0.8;
}

/* 确认对话框样式 */
.el-message-box {
  max-width: 90%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>