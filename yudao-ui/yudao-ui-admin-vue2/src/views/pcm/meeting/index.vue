<template>
  <div class="container">
    <!-- 左侧卡片 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSrtup === 1)"
      class="left-card"
    >
      <div :style="{ marginBottom: '25px' }" class="title">
        {{ $t("negotiationRecords") }}
      </div>
      <div @click="changeSetupFn('all')" class="filter-item-without-expand">
        <img
          :src="require('@/assets/pcm/meeting/all-icon.png')"
          alt="all"
          class="filter-icon"
        />
        <span class="filter-item-all">{{ $t("all") }}</span>
        <span class="count">{{ allTotal }}</span>
      </div>
      <div
        @click="changeSetupFn('myNegotiations')"
        class="filter-item-without-expand"
      >
        <span>{{ $t("myNegotiations") }}</span>
        <span class="count">{{ selfTotal }}</span>
      </div>
      <div class="filter-item">
        <div class="filter-header" @click="toggleSubordinate">
          <span>{{ $t("subordinateNegotiations") }}</span>
          <span class="toggle-icon">
            <img
              :src="
                isSubordinateOpen
                  ? require('@/assets/pcm/meeting/down-arrow.png')
                  : require('@/assets/pcm/meeting/right-arrow.png')
              "
              alt="toggle"
              class="arrow-icon"
            />
          </span>
        </div>
        <div v-if="isSubordinateOpen" class="filter-content">
          <div
            v-for="(sub, index) in subordinates"
            :key="index"
            class="subordinate-item"
            @click="handleColleague(sub)"
          >
            <img
              v-if="sub.avatar"
              :src="sub.avatar"
              alt="avatar"
              class="avatar"
            />
            <div
              v-else
              class="avatar-placeholder"
              :style="{ backgroundColor: getRandomColor(sub.nickname) }"
            >
              {{ getFirstChar(sub.nickname) }}
            </div>
            <span>{{ sub.nickname }}</span>
          </div>
        </div>
      </div>
      <div class="filter-item">
        <div class="filter-header" @click="toggleDepartment">
          <span>{{ $t("viewByDepartment") }}</span>
          <span class="toggle-icon">
            <img
              :src="
                isDepartmentOpen
                  ? require('@/assets/pcm/meeting/down-arrow.png')
                  : require('@/assets/pcm/meeting/right-arrow.png')
              "
              alt="toggle"
              class="arrow-icon"
            />
          </span>
        </div>
        <div v-if="isDepartmentOpen" class="filter-content">
          <el-tree
            :data="departmentTree"
            :props="treeProps"
            @node-click="handleNodeClick"
          />
        </div>
      </div>
    </div>

    <!-- 右侧卡片 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSrtup === 2)"
      class="right-card"
    >
      <div class="header">
        <div class="mobile-width-full">
          <img
            src="@/assets/pcm/card-detail/back.png"
            alt="返回箭头"
            class="back-arrow hidden-sm-and-up"
            @click="goBack()"
          />
          <div class="title mobile-center-text">
            {{ $t(currentTitle) || currentTitle }}
          </div>
        </div>
        <el-input
          v-model="queryParams.keyword"
          :placeholder="$t('searchNegotiations')"
          clearable
          style="width: 30%"
          type="text"
          class="mobile-width-full margin-t-10"
          @keyup.enter="search"
        >
          <template #append>
            <el-button
              @click="search"
              slot="append"
              type="success"
              icon="el-icon-search"
            ></el-button>
          </template>
        </el-input>
      </div>
      <div class="divider"></div>
      <div class="record-list">
        <!-- 当数组为空时显示提示 -->
        <div v-if="records.length === 0" class="empty-state">
          <img
            :src="require('@/assets/pcm/meeting/empty-state.png')"
            alt="暫無記錄"
            class="empty-image"
          />
          <div class="empty-text">暫無會面紀錄</div>
         
        </div>

        <!-- 当数组不为空时显示记录列表 -->
        <div v-else class="record-list-content">
          <div
            v-for="(record, index) in records"
            :key="index"
            class="record-item"
          >
            <div class="main-content">
              <!-- 左侧内容 -->
              <div class="left-content">
                <div class="name">{{ record.userName }}</div>
                <div class="position">{{ record.jobTitle }}</div>
                <div class="content">{{ record.content }}</div>
                <p class="location">
                  <img
                    :src="require('@/assets/pcm/meeting/location-icon.png')"
                    alt="location"
                    class="location-icon"
                  />
                  <span style="color: #1a66fd">{{ record.occasion }}</span>
                </p>
                <div class="meta">
                  <dict-tag
                    :style="{ color: getTypeColor(record.type) }"
                    :type="DICT_TYPE.MEETING_TYPE"
                    :value="record.type"
                  />
                  <span class="user">{{ record.creator }}</span>
                  <span class="time">{{ record.meetingTime }}</span>
                </div>
              </div>

              <!-- 右侧内容 - 图片 -->
              <div v-if="record.imageUrl" class="right-content">
                <el-image
                  :src="record.imageUrl"
                  alt="record-image"
                  class="record-image"
                  :preview-src-list="[record.imageUrl]"
                  fit="cover"
                  @click="handleImageClick"
                />
              </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-buttons">
              <!-- PC端操作按钮 -->
              <div class="pc-actions hidden-sm-and-up">
                <el-button
                  v-if="isSuperAdmin || currentUserId === record.createUserId"
                  size="small"
                  type="primary"
                  @click.stop="handleEdit(record)"
                  class="action-btn"
                >
                  {{ $t("edit") }}
                </el-button>
                <el-button
                  v-if="isSuperAdmin || currentUserId === record.createUserId"
                  size="small"
                  type="danger"
                  @click.stop="handleDelete(record)"
                  class="action-btn"
                >
                  {{ $t("delete") }}
                </el-button>
              </div>

              <!-- 移动端操作按钮 -->
              <div class="mobile-actions hidden-xs-only">
                <el-button
                  size="small"
                  type="primary"
                  v-if="isSuperAdmin || currentUserId === record.createUserId"
                  @click.stop="handleEdit(record)"
                  class="action-btn"
                >
                  {{ $t("edit") }}
                </el-button>
                <el-button
                  size="small"
                  v-if="isSuperAdmin || currentUserId === record.createUserId"
                  type="danger"
                  @click.stop="handleDelete(record)"
                  class="action-btn"
                >
                  {{ $t("delete") }}
                </el-button>
              </div>
            </div>
          </div>

          <!-- 分页组件 -->
          <el-pagination
            v-if="records.length > 0"
            :current-page="queryParams.pageNo"
            :page-size="queryParams.pageSize"
            :total="total"
            layout="total, prev, pager, next, jumper"
            @current-change="handlePageChange"
            style="margin-top: 20px; text-align: center"
          />
        </div>
      </div>

      <!-- 图片预览弹窗 -->
      <el-dialog
        v-model="dialogVisible"
        :title="$t('imagePreview')"
        width="80%"
        top="5vh"
      >
        <el-image
          :src="previewImage"
          style="width: 100%; max-height: 70vh"
          fit="contain"
        />
      </el-dialog>
    </div>

    <!-- 编辑商谈记录弹窗 -->
    <EditMeetingForm
      ref="meetingForm"
      @confirm="updateMeeting"
    ></EditMeetingForm>
  </div>
</template>

<script>
import EditMeetingForm from "@/views/pcm/card/detail/AddMeetingForm.vue";
import { listSimpleDepts } from "@/api/system/dept";
import { listSimpleUsers, getAllSubordinateUsers } from "@/api/system/user";
import {
  getMeetingPage,
  countMeetingByUserId,
  getMeeting,
  deleteMeeting,
} from "@/api/pcm/meeting"; // 确保导入deleteMeeting
import { DICT_TYPE, getDictDatas } from "@/utils/dict";
export default {
  components: { EditMeetingForm },
  data() {
    return {
      isSubordinateOpen: false,
      isDepartmentOpen: false,
      currentUserId: this.$store.getters.userId,
      queryParams: {
        pageSize: 10,
        pageNo: 1,
        keyword: "",
        cardId: this.$route.query.cardId || null,
      },
      allTotal: 0, // 全部会议总数
      selfTotal: 0, // 我的会议条数
      total: 0, // 表格总条数
      subordinates: [],
      departmentTree: [],
      treeProps: {
        children: "children",
        label: "name",
      },
      records: [],
      currentSrtup: 1,
      currentTitle: this.$t("all"), // 当前标题
      dialogVisible: false, // 图片预览弹窗
      previewImage: "", // 预览的图片URL
      editingRecordId: null, // 存储正在编辑的记录ID
    };
  },
  computed: {
    isSuperAdmin() {
      return this.$store.getters.roles?.includes("super_admin");
    },
  },
  created() {
    this.getMeetingCount();
    this.getMeetingList();
    this.getSubordinateList();
    this.getTreeselect();
  },
  methods: {
    changeSetupFn(title) {
      console.log(title);
      this.currentTitle = title || this.$t("all");
      if (this.$isMobile()) {
        this.currentSrtup = 2;
      }

      if ("all" === title) {
        // 点击全部
        this.queryParams = {
          pageSize: 10,
          pageNo: 1,
          cardId: this.$route.query.cardId || null,
          keyword: this.queryParams.keyword,
        };
      }
      if ("myNegotiations" === title) {
        // 点击我的商谈会议
        this.queryParams = {
          pageSize: 10,
          pageNo: 1,
          cardId: this.$route.query.cardId || null,
          keyword: this.queryParams.keyword,
        };
        this.queryParams.createUserId = this.currentUserId;
      }
      this.getMeetingList();
    },
    goBack() {
      if (!this.$isMobile()) return;
      this.currentSrtup = 1;
    },
    toggleSubordinate() {
      this.isSubordinateOpen = !this.isSubordinateOpen;
    },
    toggleDepartment() {
      this.isDepartmentOpen = !this.isDepartmentOpen;
    },
    getTypeColor(type) {
      const colorMap = {
        0: "#42b983",
        1: "#ff6b6b",
        2: "#1A66FD",
        3: "#f9a825",
      };
      return colorMap[type] || "#666"; // 默认颜色
    },

    /** 获取下属列表 */
    getSubordinateList() {
      getAllSubordinateUsers(this.currentUserId).then((res) => {
        this.subordinates = res.data;
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listSimpleDepts().then((response) => {
        // 处理 deptOptions 参数
        this.departmentTree = [];
        this.departmentTree.push(...this.handleTree(response.data, "id"));
      });
    },
    // 点击下属同事过滤
    handleColleague(user) {
      this.queryParams = {
        pageSize: 10,
        pageNo: 1,
        cardId: this.$route.query.cardId || null,
        keyword: this.queryParams.keyword,
      };
      this.queryParams.createUserId = user.id;
      this.changeSetupFn(user.nickname);
    },
    // 部门树节点点击
    handleNodeClick(data) {
      this.queryParams = {
        pageSize: 10,
        pageNo: 1,
        cardId: this.$route.query.cardId || null,
        keyword: this.queryParams.keyword,
      };
      // 根据机构id过滤商谈会议
      this.queryParams.deptId = data.id;
      this.changeSetupFn(data.name);
    },
    search() {
      this.queryParams.pageNo = 1;
      this.getMeetingList();
    },
    // 分页变化
    handlePageChange(page) {
      this.queryParams.pageNo = page;
      this.getMeetingList();
    },
    // 图片点击
    handleImageClick() {
      // 使用el-image的预览功能，这里不需要额外处理
    },
    getFirstChar(nickname) {
      return nickname ? nickname.charAt(0).toUpperCase() : "";
    },

    getRandomColor(nickname) {
      if (!nickname) return;
      let hash = 0;
      for (let i = 0; i < nickname.length; i++) {
        hash = nickname.charCodeAt(i) + ((hash << 5) - hash);
      }
      const hue = hash % 360;
      return `hsl(${hue}, 70%, 65%)`;
    },

    getMeetingCount() {
      countMeetingByUserId(this.currentUserId, this.queryParams.cardId).then(
        (res) => {
          this.allTotal = res.data.allCount;
          this.selfTotal = res.data.userCount;
        }
      );
    },

    getMeetingList() {
      getMeetingPage(this.queryParams).then((res) => {
        this.records = res.data.list;
        this.total = res.data.total;
      });
    },
    // 添加新的方法
    handleEdit(record) {
      this.editingRecordId = record.id; // 存储当前编辑的记录ID
      this.$refs["meetingForm"].open(record.cardId, record);
    },

    async updateMeeting() {
      try {
        if (!this.editingRecordId) return;

        // 使用新API获取单条记录
        const response = await getMeeting(this.editingRecordId);
        const updatedRecord = response.data;

        // 更新列表中的记录
        const index = this.records.findIndex(
          (r) => r.id === this.editingRecordId
        );
        if (index !== -1) {
          this.$set(this.records, index, updatedRecord);
        }
      } catch (error) {
      } finally {
        this.editingRecordId = null;
      }
    },

    async handleDelete(record) {
      try {
        await this.$confirm("確認刪除該會面紀錄嗎？", "提示", {
          confirmButtonText: this.$t("confirm"),
          cancelButtonText: this.$t("cancel"),
          type: "warning",
        });

        // 调用删除API
        await deleteMeeting(record.id); // 确保deleteMeeting已正确导入
        this.$message.success("刪除成功!");

        // 重新加载数据
        this.getMeetingList();
        this.getMeetingCount();
      } catch (error) {
        if (error !== "cancel") {
          this.$message.error("已取消刪除");
        }
      }
    },
  },
};
</script>
<style scoped>
@media screen and (max-width: 768px) {
  .margin-t-10 {
    margin-top: 10px;
  }
  .mobile-width-full {
    width: 100% !important;
  }
  .mobile-center-text {
    width: 100%;
    text-align: center;
  }
  .record-image {
    width: 100%;
    height: auto;
    max-height: 200px;
  }

  .hidden-sm-and-up .action-btn {
    padding: 6px;
  }

  .hidden-sm-and-up .action-btn span {
    display: none;
  }
  .main-content {
    flex-direction: column; /* 改为垂直布局 */
    width: 100%; /* 占据全部宽度 */
  }

  .right-content {
    padding-left: 0; /* 移除左边距 */
    margin-top: 15px; /* 添加上边距 */
    justify-content: flex-start; /* 左对齐图片 */
  }

  .record-image {
    width: 100%; /* 图片宽度填满 */
    max-width: 300px; /* 限制最大宽度 */
    height: auto; /* 高度自适应 */
  }
}

.back-arrow {
  position: absolute;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}
.container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
}

.left-card,
.right-card {
  background-color: white;
  padding: 8px 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.left-card {
  flex: 1;
  margin-right: 10px;
}

.right-card {
  flex: 4.5;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.filter-item-without-expand {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-bottom: 20px;
  cursor: pointer;
  font-size: 16px; /* 设置字体大小为 16px */
}

.filter-item-all {
  color: #1f1f1f;
  font-weight: bold;
}

.filter-icon {
  width: 16px; /* 约束图标大小为 16px */
  height: 16px;
  margin-right: 8px;
}

.filter-item {
  margin-bottom: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
}

.filter-content {
  margin-top: 5px;
  color: #1f1f1f;
  font-size: 14px;
  padding-left: 20px; /* 展开内容靠左显示 */
}

.count {
  color: #999999;
  margin-left: auto;
}

.toggle-icon {
  cursor: pointer;
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

.subordinate-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 10px;
}

.avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.header {
  margin-bottom: 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

.divider {
  border-bottom: 1px solid #ddd;
  margin-bottom: 20px;
}

.record-list {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 启用滚动条 */
  padding-right: 10px; /* 防止滚动条遮挡内容 */
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

.empty-image {
  width: 200px; /* 根据需要调整图片大小 */
  height: 200px;
  margin-bottom: 5px;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

.record-list-content {
  display: flex;
  flex-direction: column;
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0 15px 0;
  border-bottom: 1px solid #eee;
  width: 100%;
  position: relative;
}
.main-content {
  display: flex;
  width: 80%;
}
.right-content {
  padding-left: 30px;
  display: flex;
  align-items: center;
}
.left-content {
  flex: 1;
}

.name {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.position {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.content {
  font-size: 14px;
  color: #1f1f1f;
  margin-bottom: 10px;
}

.location {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-bottom: 10px;
}

.location-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}

.meta {
  font-size: 14px;
  color: #999;
}

.meta span {
  margin-right: 10px;
}

.user,
.time {
  color: #666666;
}

.record-image {
  margin-top: 10px;
  width: 200px;
  height: 120px;
  border-radius: 4px;
  margin-bottom: 10px;
  object-fit: cover; /* 保持图片比例不变，同时填充满容器 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  transition: transform 0.3s; /* 添加过渡效果 */
}
.record-image:hover {
  transform: scale(1.02); /* 鼠标悬停时轻微放大 */
}

/* 分页样式微调 */
.el-pagination {
  padding: 20px 0;
}

/* 新增操作按钮样式 */
.pc-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  gap: 8px;
}

.mobile-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 10px;
  gap: 8px;
}

.action-btn {
  padding: 6px 10px;
}
</style>