<template>
  <base-dialog
    v-model="dialogVisible"
    :title="$t(title)"
    width="800px"
    @confirm="submitForm"
    @cancel="cancel"
  >
    <template v-slot:content>
      <div class="user-edit-container">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="120px"
          label-position="right"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item :label="$t('nickname')" prop="nickname">
                <el-input
                  v-model="form.nickname"
                  :placeholder="$t('enterNickname')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('affiliatedDepartment')" prop="deptId">
                <treeselect
                  v-model="form.deptId"
                  :options="deptOptions"
                  :show-count="true"
                  :clearable="false"
                  :placeholder="$t('selectAffiliatedDept')"
                  :normalizer="normalizer"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
          <el-col :span="12">
              <el-form-item :label="$t('userGender')">
                <el-select v-model="form.sex" :placeholder="$t('pleaseSelect')">
                  <el-option
                    v-for="dict in sexDictDatas"
                    :key="parseInt(dict.value)"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$t('email')" prop="email">
                <el-input
                  v-model="form.email"
                  :placeholder="$t('enterEmail')"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item :label="$t('remark')">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入内容"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
  </base-dialog>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
  import {
    updateUser,getUser
  } from "@/api/system/user";
 import {
    DICT_TYPE,
    getDictDatas
  } from "@/utils/dict";

  import {
    listSimpleDepts
  } from "@/api/system/dept";

export default {
  name: "UserEditDialog",
  components: { BaseDialog, Treeselect },
  props: {
    title: {
      type: String,
      default: "editUser",
    },
    userData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        id: undefined,
        nickname: "",
        deptId: null,
        mobile: "",
        email: "",
        username: "",
        password: "",
        sex: 0,
        avatar: "",
        remark: "",
      },
        defaultProps: {
          children: "children",
          label: "name",
        },
      rules: {
        nickname: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        deptId: [
          { required: true, message: "归属部门不能为空", trigger: "blur" },
        ],
        mobile: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
        email: [
          { required: true, message: "邮箱地址不能为空", trigger: "blur" },
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        username: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
        ],
      
      },
      deptOptions: [],
      // 数据字典
        statusDictDatas: getDictDatas(DICT_TYPE.COMMON_STATUS),
        sexDictDatas: getDictDatas(DICT_TYPE.SYSTEM_USER_SEX),
    };
  },
  created() {
    this.getDeptList();
  },
  methods: {
    normalizer(node) {
     return {
          id: node.id,
          label: node.name,
          children: node.children,
        };
    },

    async getDeptList() {
      /** 查询部门下拉树结构 */
        listSimpleDepts().then((response) => {
          // 处理 deptOptions 参数
          this.deptOptions = [];
          this.deptOptions.push(...this.handleTree(response.data, "id"));
        });
     
    },

    open() {
      
      this.resetForm();
      if (this.userData && this.userData.id) {
         getUser(this.userData.id).then((response) => {
          this.form = response.data;
         this.dialogVisible = true;
        });
      }
    },

    resetForm() {
      this.form = {
        id: undefined,
        nickname: "",
        deptId: null,
        mobile: "",
        email: "",
        username: "",
        sex: 0,
        remark: "",
      };
      this.$refs.form && this.$refs.form.resetFields();
    },

    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
           updateUser(this.form).then((response) => {
                this.$modal.msgSuccess("修改成功");
                this.dialogVisible = false;
              });
        } else {
          return false;
        }
      });
    },

    cancel() {
      this.dialogVisible = false;
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped lang="scss">

.el-form-item {
  margin-bottom: 22px;
}

.el-select {
  width: 100%;
}

.el-textarea {
  ::v-deep .el-textarea__inner {
    min-height: 80px !important;
  }
}

.dialog-footer {
  text-align: right;
  padding: 10px 20px 0;
  border-top: 1px solid #e8e8e8;
}
</style>