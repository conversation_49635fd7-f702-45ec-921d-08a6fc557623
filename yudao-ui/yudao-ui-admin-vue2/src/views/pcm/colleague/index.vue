<template>
  <div class="container">
    <!-- 左侧卡片 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 1)"
      class="left-card"
    >
      <div class="title">{{ $t("colleague") }}</div>
      <div class="colleague-list">
        <div
          v-for="(colleague, index) in colleagues"
          :key="index"
          class="colleague-item"
          @mouseover="hoverIndex = index"
          @mouseleave="hoverIndex = -1"
          @click="selectColleague(index, colleague.id)"
          :style="{
            backgroundColor:
              hoverIndex === index
                ? '#ED6C001A'
                : selectedIndex === index
                ? '#ED6C001A'
                : 'transparent',
            color:
              hoverIndex === index
                ? '#ED6C00'
                : selectedIndex === index
                ? '#ED6C00'
                : '#1F1F1F',
          }"
        >
          <img
            v-if="colleague.avatar"
            :src="colleague.avatar"
            alt="avatar"
            class="avatar"
          />
          <div
            v-else
            class="avatar-placeholder"
            :style="{ backgroundColor: getRandom<PERSON>olor(colleague.nickname) }"
          >
            {{ getFirst<PERSON><PERSON>(colleague.nickname) }}
          </div>
          <span class="name">{{ colleague.nickname }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧卡片 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 2)"
      class="right-card"
    >
      <div class="nav-bar-custom hidden-sm-and-up">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow"
          @click="goBack()"
        />
        <span>{{ $t("profile") }}</span>
      </div>
      <div class="header">
        <div class="profile">
          <img
            v-if="currentUser.avatar"
            :src="currentUser.avatar"
            alt="avatar"
            class="avatar"
          />
          <div
            v-else
            class="avatar-placeholder"
            :style="{ backgroundColor: getRandomColor(currentUser.nickname) }"
          >
            {{ getFirstChar(currentUser.nickname) }}
          </div>
          <span class="profile-name">{{ currentUser.name }}</span>
        </div>
        <div class="tags">
          <div class="tag">
            <span class="tag-number"> {{cardCount}}</span>
            <span class="tag-text">{{ $t("colleagueBusinessCard") }}</span>
          </div>
          <div class="tag">
            <span class="tag-number">{{meetingCount}}</span>
            <span class="tag-text">{{ $t("negotiationRecords") }}</span>
          </div>
        </div>
      </div>

      <div class="personal-info">
        <div class="info-header">
          <span class="info-title">{{ $t("profile") }}</span>
          <el-button
            v-if="enableEdit"
            class="edit-button"
            icon="el-icon-edit"
            @click="handleEdit"
          >
            {{ $t("edit") }}
          </el-button>
        </div>
        <div class="divider"></div>
        <div class="info-content">
          <!-- <div class="info-item">
            <img
              :src="require('@/assets/pcm/colleague/phone.png')"
              alt="phone"
              class="info-icon"
            />
            <span>{{ currentUser.mobile }}</span>
          </div> -->
          <div class="info-item">
            <img
              :src="require('@/assets/pcm/colleague/mail.png')"
              alt="email"
              class="info-icon"
            />
            <span>{{ currentUser.email }}</span>
          </div>
          <div class="info-item">
            <img
              :src="require('@/assets/pcm/colleague/company.png')"
              alt="company"
              class="info-icon"
            />
            <span>{{ currentUser.deptName }}</span>
          </div>
        </div>
      </div>
      <!-- <div class="actions" v-if="isCurrentUser">
        <button class="action-button">
          <img
            :src="require('@/assets/pcm/colleague/qrcode.png')"
            alt="qrcode"
            class="action-icon"
          />
          <span> {{ $t("myQRCode") }}</span>
        </button>
        <button class="action-button">
          <img
            :src="require('@/assets/pcm/colleague/link.png')"
            alt="link"
            class="action-icon"
          />
          <span>{{ $t("myECardLink") }}</span>
        </button>
      </div> -->
    </div>

    <EditForm  ref="userEditDialog"  :user-data="currentUser"/>
  </div>
</template>

<script>
import { listSimpleUsers, getUser } from "@/api/system/user";
import {countCardByUserId} from "@/api/pcm/card";
import { countMeetingByUserId } from "@/api/pcm/meeting";
import  EditForm from "./EditForm.vue"

export default {
  components:{EditForm},
  data() {
    return {
      hoverIndex: -1,
      selectedIndex: 0, // 默认选中第一个
      currentUserId:this.$store.getters.userId,
      currentUser: {},
      colleagues: [],
      currentSetup: 1,
      cardCount: 0, //卡片数量
      meetingCount: 0, //商谈记录数量
    };
  },
  computed: {
    isCurrentUser() {
      return this.currentUser.id === this.currentUserId;
    },
     enableEdit(){
      return this.$store.getters.roles?.includes("super_admin") || this.currentUser.id === this.currentUserId;
    }
  },
  mounted() {
    this.handleMobileDefaultConfigFn();
    this.getList();
  },
  methods: {
    goBack() {
      this.currentSetup = 1;
    },
    getFirstChar(nickname) {
      return nickname ? nickname.charAt(0).toUpperCase() : "";
    },

    getRandomColor(nickname) {
      if(!nickname) return
      let hash = 0;
      for (let i = 0; i < nickname.length; i++) {
        hash = nickname.charCodeAt(i) + ((hash << 5) - hash);
      }
      const hue = hash % 360;
      return `hsl(${hue}, 70%, 65%)`;
    },

    handleMobileDefaultConfigFn() {
      if (!this.$isMobile()) return;
      this.selectedIndex = null;
    },
    gotoCardDetailFn() {
      this.currentSetup = 2;
    },
    selectColleague(index, id) {
      if (this.$isMobile()) {
        this.gotoCardDetailFn();
      }
      this.selectedIndex = index;
      this.getUserById(id);
    },
    getUserById(id) {
      getUser(id).then((res) => {
        this.currentUser = res.data;
      });
      this.getCardCount(id);
      this.getMeetingCount(id);
    },
    /**
     * 获取名片数量
     */
    getCardCount(userId) {
      countCardByUserId(userId).then((res) => {
        this.cardCount = res.data;
      });
    },
    /**
     * 获取商谈记录数量
     */
    getMeetingCount(userId) {
      countMeetingByUserId(userId,null).then((res) => {
        this.meetingCount = res.data.userCount;
      });
    },
    /** 查询用户列表 */
    getList() {
      listSimpleUsers().then((response) => {
        this.colleagues = response.data;
        this.getUserById(this.colleagues[0].id);
      });
    },
    handleEdit(){
this.$refs.userEditDialog.open();
    }
  },
};
</script>

<style scoped>
.nav-bar-custom {
  position: relative;
  text-align: center;
  font-size: 20px;
  margin-bottom: 15px;
  font-weight: 700;
}
.back-arrow {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}

.container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
  overflow: hidden;
}

.left-card,
.right-card {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.left-card {
  flex: 2;
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内部滚动影响布局 */
  height: 100%; /* 继承父容器高度 */
}

.right-card {
  flex: 8;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内部滚动影响布局 */
  height: 100%; /* 继承父容器高度 */
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.colleague-list {
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动 */
  flex-grow: 1; /* 占据剩余空间 */
  max-height: calc(100vh - 80px); /* 减去标题和padding的高度 */
  padding-right: 5px; /* 为滚动条留出空间 */
}
/* 可选：美化滚动条 */
.colleague-list::-webkit-scrollbar {
  width: 6px;
}

.colleague-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.colleague-list::-webkit-scrollbar-track {
  background-color: transparent;
}

.colleague-item {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 5px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 10px;
}
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}
.name {
  font-size: 16px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.profile {
  display: flex;
  align-items: center;
}

.profile-avatar {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin-right: 10px;
}

.profile-name {
  font-size: 24px;
  font-weight: bold;
}

.tags {
  display: flex;
}

.tag {
  height: 80px;
  width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 20px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.tag-number {
  font-size: 20px;
  font-weight: bold;
  color: #000000;
}

.tag-text {
  font-size: 14px;
  color: #666666;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 15px 0;
}

.personal-info {
  margin-bottom: 20px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.info-title {
  font-size: 18px;
  font-weight: bold;
}

.edit-button {
  background-color: transparent;
  border: 1px solid #e0e0e0;
  color: #1f1f1f;
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
}

.info-content {
  display: flex;
  flex-direction: column;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #1f1f1f;
}

.info-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.actions {
  display: flex;
  gap: 10px;
}

.action-button {
  display: flex;
  align-items: center;
  background-color: transparent;
  border: 1px solid #e0e0e0;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #1f1f1f;
}

.action-icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}
</style>