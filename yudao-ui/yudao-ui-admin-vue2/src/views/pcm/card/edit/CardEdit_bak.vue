<template>
  <div class="container">
    <!-- 顶部标题卡片 -->
    <div class="header-card">
      <div class="header-left">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回图标"
          class="back-icon"
          @click="goBack()"
        />
        <span class="title">{{ $t("editCard") }}</span>
      </div>
      <div class="header-right">
        <button class="btn cancel hidden-xs-only" @click="goBack">
          {{ $t("cancel") }}
        </button>
        <button class="btn save-next" @click="saveAndNext">
          {{ $t("saveAndEditNext") }}
        </button>
        <button class="btn save" @click="save">{{ $t("save") }}</button>
      </div>
    </div>

    <!-- 下方左右卡片布局 -->
    <div class="content">
      <!-- 名片实图卡片 -->
      <div class="card image-card hidden-xs-only">
        <BusinessCardLabel
          :handleTag="handleTag"
          :cardInfo="cardInfo"
        ></BusinessCardLabel>
      </div>

      <!-- 名片字段信息卡片 -->
      <div class="card info-card">
        <el-form ref="form" :model="cardInfo">
          <!-- 伙伴資訊 -->
          <div class="info-section">
            <div class="section-title">{{ $t("partnerInfo") }}</div>
            <div class="divider"></div>
            <div class="fields">
              <div class="field-column">
                <div class="field">
                  <span class="field-name">稱謂</span>
                  <el-select
                    key="select"
                    class="field-select"
                    v-model="cardInfo.title"
                    clearable
                    filterable
                    placeholder="請選擇稱謂"
                  >
                    <el-option
                      v-for="item in cardTitleCn"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="field">
                  <span class="field-name">姓氏</span>
                  <el-input
                    type="text"
                    placeholder="梁"
                    v-model="cardInfo.firstName"
                  />
                </div>
                <div class="field">
                  <span class="field-name">名字</span>
                  <el-input
                    type="text"
                    placeholder="俊杰"
                    v-model="cardInfo.lastName"
                  />
                </div>
                <div class="field">
                  <span class="field-name">勳銜</span>
                  <el-input
                    type="text"
                    placeholder="勳銜"
                    v-model="cardInfo.honour"
                  />
                </div>
                <div class="field">
                  <span class="field-name">
                    Mobile Phone<br />
                    流動電話
                  </span>
                  <div class="field-input-container">
                    <el-form-item prop="phoneMobile" :rules="rules.phoneMobile">
                      <el-input
                        type="text"
                        placeholder="流動電話"
                        v-model="cardInfo.phoneMobile"
                      />
                    </el-form-item>
                  </div>
                </div>
              </div>
              <div class="field-column">
                <div class="field">
                  <span class="field-name">Title</span>
                  <!-- <input
                    type="text"
                    placeholder="Mr, Ms, Mrs, Others"
                    v-model="cardInfo.titleEn"
                  /> -->
                  <el-select
                    key="select"
                    class="field-select"
                    v-model="cardInfo.titleEn"
                    clearable
                    filterable
                    placeholder="Please select Title"
                  >
                    <el-option
                      v-for="item in cardTitleEn"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="field">
                  <span class="field-name">First Name</span>
                  <el-input
                    type="text"
                    placeholder="Tai-man"
                    v-model="cardInfo.firstNameEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Last Name</span>
                  <el-input
                    type="text"
                    placeholder="CHAN"
                    v-model="cardInfo.lastNameEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Honour</span>
                  <el-input
                    type="text"
                    placeholder="MH, JP"
                    v-model="cardInfo.honourEn"
                  />
                </div>

                <div class="field">
                  <span class="field-name"> 內地號碼 </span>
                  <div class="field-input-container">
                    <el-form-item
                      prop="phoneMainland"
                      :rules="rules.phoneMainland"
                    >
                      <el-input
                        type="text"
                        placeholder="請輸入內地號碼"
                        v-model="cardInfo.phoneMainland"
                      />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">{{ $t("organizationInfo") }}</div>
            <div class="divider"></div>
            <div class="fields">
              <div class="field-column">
                <div class="field">
                  <span class="field-name">機構名稱</span>
                  <el-input
                    type="text"
                    placeholder="機構名稱"
                    v-model="cardInfo.companyName"
                  />
                </div>
                <div class="field">
                  <span class="field-name">部門</span>
                  <el-input
                    type="text"
                    placeholder="部門"
                    v-model="cardInfo.department"
                  />
                </div>
                <div class="field">
                  <span class="field-name">職位</span>
                  <el-input
                    type="text"
                    placeholder="職位"
                    v-model="cardInfo.jobTitle"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Email Address 電郵</span>
                  <div class="field-input-container">
                    <el-form-item prop="email" :rules="rules.email">
                      <el-input
                        type="text"
                        placeholder="Email Address"
                        v-model="cardInfo.email"
                      />
                    </el-form-item>
                  </div>
                </div>
                <div class="field">
                  <span class="field-name">
                    Phone (Direct Line)<br />
                    電話（直線）
                  </span>
                  <div class="field-input-container">
                    <el-form-item
                      prop="phoneDirectLine"
                      :rules="rules.phoneDirectLine"
                    >
                      <el-input
                        type="text"
                        placeholder="Phone (Direct Line)"
                        v-model="cardInfo.phoneDirectLine"
                      />
                    </el-form-item>
                  </div>
                </div>
                <div class="field">
                  <span class="field-name">Industry Type行業類型</span>
                  <el-input
                    type="text"
                    placeholder="Technology, Healthcare"
                    v-model="cardInfo.industryType"
                  />
                </div>

                <div class="field">
                  <span class="field-name">地址1</span>
                  <el-input
                    type="text"
                    placeholder="地址1"
                    v-model="cardInfo.addressLine1"
                  />
                </div>
                <div class="field">
                  <span class="field-name">地址2</span>
                  <el-input
                    type="text"
                    placeholder="地址2"
                    v-model="cardInfo.addressLine2"
                  />
                </div>
                <div class="field">
                  <span class="field-name">地區</span>
                  <el-input
                    type="text"
                    placeholder="地區"
                    v-model="cardInfo.district"
                  />
                </div>
                <div class="field">
                  <span class="field-name">區域</span>
                  <el-input
                    type="text"
                    placeholder="區域"
                    v-model="cardInfo.area"
                  />
                </div>
                <div class="field">
                  <span class="field-name">城市</span>
                  <el-input
                    type="text"
                    placeholder="城市"
                    v-model="cardInfo.city"
                  />
                </div>
              </div>
              <div class="field-column">
                <div class="field">
                  <span class="field-name">Name of Organisation</span>
                  <el-input
                    type="text"
                    placeholder="Name of Organisation"
                    v-model="cardInfo.companyNameEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Department</span>
                  <el-input
                    type="text"
                    placeholder="Department"
                    v-model="cardInfo.departmentEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Job Title</span>
                  <el-input
                    type="text"
                    placeholder="Job Title"
                    v-model="cardInfo.jobTitleEn"
                  />
                </div>

                <div class="field">
                  <span class="field-name">
                    Phone (Office)<br />電話（辦公室）
                  </span>
                  <div class="field-input-container">
                    <el-form-item prop="phoneOffice" :rules="rules.phoneOffice">
                      <el-input
                        type="text"
                        placeholder="Phone (Office)"
                        v-model="cardInfo.phoneOffice"
                      />
                    </el-form-item>
                  </div>
                </div>
                <div class="field">
                  <span class="field-name"
                    >Fax Number (if applicable)傳真（如適用）</span
                  >
                  <el-input
                    type="text"
                    placeholder="Fax Number (if applicable)"
                    v-model="cardInfo.faxNumber"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Business Type 業務類型</span>
                  <transition name="fade" mode="out-in">
                    <!-- 下拉框 -->
                    <el-select
                      v-if="!showCustomInput"
                      key="select"
                      class="field-select"
                      v-model="cardInfo.businessType"
                      clearable
                      filterable
                      placeholder="请选择业务类型"
                      @change="handleBusinessTypeChange"
                    >
                      <el-option
                        v-for="item in businessTypes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>

                    <!-- 输入框 -->
                    <el-input
                      v-else
                      key="input"
                      ref="customInput"
                      v-model="cardInfo.otherBusiness"
                      class="field-select"
                      placeholder="请输入具体业务类型"
                      clearable
                      @blur="handleCustomInputBlur"
                      @clear="resetToSelect"
                    />
                  </transition>
                </div>

                <div class="field">
                  <span class="field-name">Address Line 1</span>
                  <el-input
                    type="text"
                    placeholder="Address Line 1"
                    v-model="cardInfo.addressLine1En"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Address Line 2</span>
                  <el-input
                    type="text"
                    placeholder="Address Line 2"
                    v-model="cardInfo.addressLine2En"
                  />
                </div>
                <div class="field">
                  <span class="field-name">District</span>
                  <el-input
                    type="text"
                    placeholder="e.g. Tsim Sha Tsui"
                    v-model="cardInfo.districtEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Area</span>
                  <el-input
                    type="text"
                    placeholder="e.g. Kowloon"
                    v-model="cardInfo.areaEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">country</span>
                  <el-input
                    type="text"
                    placeholder="country"
                    v-model="cardInfo.countryEn"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 社交媒體 -->
          <div class="info-section">
            <div class="section-title">{{ $t("socialMedia") }}</div>
            <div class="divider"></div>
            <div class="fields">
              <div class="field-column">
                <div class="field">
                  <span class="field-name">LinkedIn Profile URL</span>
                  <el-input
                    type="text"
                    placeholder="LinkedIn Profile URL"
                    v-model="cardInfo.linkedinProfileUrl"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Facebook Page URL</span>
                  <el-input
                    type="text"
                    placeholder="Facebook Page URL"
                    v-model="cardInfo.facebookPageUrl"
                  />
                </div>
              </div>
              <div class="field-column">
                <div class="field">
                  <span class="field-name">Instagram URL</span>
                  <el-input
                    type="text"
                    placeholder="Instagram URL"
                    v-model="cardInfo.instagramUrl"
                  />
                </div>
                <div class="field">
                  <span class="field-name">WeChat ID</span>
                  <el-input
                    type="text"
                    placeholder="WeChat ID"
                    v-model="cardInfo.wechatId"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form>
        <template>
          <BusinessCardLabel
            :handleTag="handleTag"
            :cardInfo="cardInfo"
            class="hidden-sm-and-up"
          ></BusinessCardLabel>
        </template>
      </div>
    </div>

    <!-- 显示标签选择框 -->
    <TagSelect
      ref="tagSelect"
      @confirm="updateTags"
      :initialSelectedTags="splitTag"
    ></TagSelect>
  </div>
</template>

<script>
import * as CardApi from "@/api/pcm/card";
import TagSelect from "../table/TagSelect.vue";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";
import BusinessCardLabel from "./BusinessCardLabel.vue";
export default {
  components: { TagSelect, BusinessCardLabel },
  name: "BusinessCardEditor",
  // 香港电话校验规则

  data() {
    const validateHongKongPhone = (rule, value, callback) => {
      if (!value) {
        callback(); // 空值不校验
        return;
      }

      // 香港电话格式: 8位或9位数字，可能以+852开头
      // 清理空格、括号、横线等干扰符号
      const cleanedValue = value.replace(/[\s\-()]/g, "");
      // 匹配：
      // 1. ***********（852开头 + 8位数字）
      // 2. 37557025（纯8位数字）
      // 3. +***********（+852开头 + 8位数字）
      // 4. 9位数字的情况（如某些新号码）
      const regex = /^(\+?852|852)?[0-9]{8,9}$/;

      if (regex.test(cleanedValue)) {
        callback();
      } else {
        callback(new Error(this.$t("phoneFormatError")));
      }
    };

    const validateMainlandPhone = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      }

      // 先清理用户可能输入的空格/横线等字符
      const cleanedValue = value.replace(/[\s-]/g, "");

      const regex = /^(\+86)?1[3-9]\d{9}$/;
      regex.test(cleanedValue)
        ? callback()
        : callback(new Error("请输入正确的大陆手机号"));
    };

    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback(); // 空值不校验
        return;
      }

      // 基本的邮箱格式验证
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (regex.test(value)) {
        callback();
      } else {
        callback(new Error(this.$t("emailFormatError")));
      }
    };
    return {
      cardId: this.$route.params.cardId,
      cardInfo: {},
      businessTypes: getDictDatas(DICT_TYPE.BUSINESS_TYPE),
      cardTitleCn: getDictDatas(DICT_TYPE.CARD_TITLE_CN),
      cardTitleEn: getDictDatas(DICT_TYPE.CARD_TITLE_EN),

      //业务类型相关
      showCustomInput: false,
      previousSelection: "",
      rules: {
        phoneMobile: [{ validator: validateHongKongPhone, trigger: "blur" }],
        phoneDirectLine: [
          { validator: validateHongKongPhone, trigger: "blur" },
        ],
        phoneOffice: [{ validator: validateHongKongPhone, trigger: "blur" }],
        phoneMainland: [{ validator: validateMainlandPhone, trigger: "blur" }],
        email: [{ validator: validateEmail, trigger: "blur" }], // 添加email验证规则
      },
    };
  },
  computed: {
    splitTag() {
      if (this.cardInfo.customTags) {
        return this.cardInfo.customTags.split(";");
      }
      return [];
    },
  },
  mounted() {
    this.getCardInfo();
  },
  methods: {
    getCardInfo() {
      CardApi.getCard(this.cardId).then((res) => {
        this.cardInfo = res.data;
      });
    },
    goBack() {
      this.$router.go(-1); // 或者 this.$router.back();
    },
    handleTag() {
      this.$refs["tagSelect"].open();
    },
    updateTags(tags) {
      CardApi.updateCard({
        id: this.cardId,
        customTags: tags.join(";"),
      }).then((res) => {
        this.getCardInfo();
        this.$modal.msgSuccess("成功修改标签!");
      });
    },
    /**
     * 处理业务类型变化
     */
    handleBusinessTypeChange(value) {
      if (value === "other") {
        this.showCustomInput = true;
        this.cardInfo.otherBusiness = ""; // 清空之前可能存在的自定义输入

        // 自动聚焦到输入框
        this.$nextTick(() => {
          this.$refs.customInput?.focus();
        });
      } else {
        // 选择非"other"时，清空otherBusiness
        this.cardInfo.otherBusiness = "";
        this.showCustomInput = false;
      }
    },
    /**
     * 处理输入框失去焦点
     */
    handleCustomInputBlur() {
      if (!this.cardInfo.otherBusiness.trim()) {
        // 如果没有输入内容，重置为选择状态
        this.resetToSelect();
      }
    },

    /**
     * 重置为下拉选择
     */
    resetToSelect() {
      this.showCustomInput = false;
      this.cardInfo.otherBusiness = "";
      this.cardInfo.businessType = this.previousSelection || "";
    },
    //保存
    save() {
      this.submitForm("save");
    },
    //保存并下一个
    saveAndNext() {
      this.submitForm("saveAndNext");
    },
    goBack() {
      this.$router.go(-1);
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 如果选择了"other"但没有输入内容，则清空otherBusiness
          if (
            this.cardInfo.businessType === "other" &&
            !this.cardInfo.otherBusiness.trim()
          ) {
            this.cardInfo.otherBusiness = "";
          }
          CardApi.saveAndReturnPrevious(this.cardInfo).then((response) => {
            if ("saveAndNext" === type) {
              //保存并下一张
              this.$modal.msgSuccess("保存成功並加載下一張");
              this.cardId = response.data.previousCardId;
              this.getCardInfo();
            } else {
              //仅保存刷新
              this.$modal.msgSuccess("保存成功");
              this.getCardInfo();
            }
          });
        }
      });
    },
  },
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f0f4fa;
  overflow-y: auto; /* 启用滚动条 */
}

.header-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止标题卡片被压缩 */
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin-right: 10px;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
}

.btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 5px;
  margin-left: 10px;
  cursor: pointer;
}

.cancel,
.save-next {
  background-color: transparent;
  border: 1px solid #1f1f1f;
  color: #1f1f1f;
}

.save {
  background-color: #1ea235;
  color: #ffffff;
  border: none;
}

.content {
  display: flex;
  flex: 1;
  margin-top: 10px;
  overflow: hidden; /* 防止内容溢出 */
}

.card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 卡片内容超出时启用滚动条 */
}

.image-card {
  flex: 1;
  margin-right: 10px;
}

.info-card {
  flex: 2;
  margin-left: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
}

.edit-tags {
  background-color: transparent;
  border: none;
  color: #1ea235;
  cursor: pointer;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 10px 0;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.tag {
  background-color: transparent;
  color: #1f1f1f;
  padding: 5px 10px;
  border-radius: 8px;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid #0000001a;
}

.card-image {
  width: 100%;
  max-height: 776px;
  border-radius: 10px;
  border: 1px solid #0000001a;
  object-fit: cover;
}

.info-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.fields {
  display: flex;
  flex-wrap: wrap;
}

.field-column {
  flex: 1;
  margin-right: 20px;
}

.field {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

@media screen and (max-width: 768px) {
  .field {
    flex-direction: column;
    align-items: normal;
  }
  .info-card {
    margin-left: 0;
  }
  .field-name {
    padding-bottom: 5px;
    padding-left: 10px;
  }
}

.field-name {
  width: 150px;
  font-size: 12px;
  color: #333333;
}

.field-select {
  width: 100%;
  border-radius: 5px;
}

input {
  flex: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;

  margin-right: 10px;
}

.add-remove {
  background-color: transparent;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  margin-left: 5px;
}

/* 仅针对联系方式区域的样式调整 */
.contact-info .fields {
  display: flex;
  flex-direction: column;
}

.contact-info .field {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.contact-info .field-name {
  width: 80px;
  font-size: 14px;
  color: #333333;
  margin-right: 10px;
}

.contact-info input {
  flex: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.contact-info .button-group {
  display: flex;
  margin-left: 10px;
}

.contact-info .add-remove {
  background-color: transparent;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  margin-left: 5px;
}
/* 添加过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.field-input-container {
  width: 100%;
  position: relative; /* 为错误提示定位 */
}

.field-input-container /deep/ .el-form-item {
  margin-bottom: 0px;
}

.field-input-container /deep/ .el-form-item__error {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  white-space: normal; /* 允许换行 */
  font-size: 12px !important;
  padding-top: 2px;
  color: #f56c6c;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .el-form-item__error {
    position: static; /* 移动端错误信息显示在下方 */
    margin-top: 4px;
  }
}
</style>