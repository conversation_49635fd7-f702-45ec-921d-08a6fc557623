<template>
  <div class="container">
    <!-- 顶部标题卡片 -->
    <div class="header-card">
      <div class="header-left">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回图标"
          class="back-icon"
          @click="goBack()"
        />
        <span class="title">{{
          isEditMode ? $t("editCard") : $t("addCard")
        }}</span>
      </div>
      <div class="header-right">
        <button class="btn cancel hidden-xs-only" @click="goBack">
          {{ $t("cancel") }}
        </button>
        <button class="btn save-next" @click="saveAndNext" v-if="isEditMode">
          {{ $t("saveAndEditNext") }}
        </button>
        <button class="btn save" @click="save">{{ $t("save") }}</button>
      </div>
    </div>

    <!-- 下方左右卡片布局 -->
    <div class="content">
      <!-- 名片实图卡片 -->
      <div class="card image-card hidden-xs-only">
        <BusinessCardLabel
          :handleTag="handleTag"
          :cardInfo="cardInfo"
          @update:cardInfo="cardInfo = $event"
        ></BusinessCardLabel>
      </div>

      <!-- 名片字段信息卡片 -->
      <div class="card info-card">
        <el-form ref="form" :model="cardInfo">
          <!-- 伙伴資訊 -->
          <div class="info-section">
            <div class="section-title">{{ $t("partnerInfo") }}</div>
            <div class="divider"></div>
            <div class="fields">
              <div class="field-column">
                <div class="field">
                  <span class="field-name">稱謂</span>
                  <el-select
                    key="select"
                    class="field-select"
                    v-model="cardInfo.title"
                    clearable
                    filterable
                    placeholder="請選擇稱謂"
                  >
                    <el-option
                      v-for="item in cardTitleCn"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="field">
                  <span class="field-name">姓氏</span>
                  <el-input
                    type="text"
                    placeholder="梁"
                    v-model="cardInfo.firstName"
                  />
                </div>
                <div class="field">
                  <span class="field-name">名字</span>
                  <el-input
                    type="text"
                    placeholder="俊杰"
                    v-model="cardInfo.lastName"
                  />
                </div>
                <div class="field">
                  <span class="field-name">勳銜</span>
                  <el-input
                    type="text"
                    placeholder="勳銜"
                    v-model="cardInfo.honour"
                  />
                </div>
                <div class="field">
                  <span class="field-name"> Mobile Phone<br />流動電話 </span>
                  <div class="field-input-container">
                    <el-form-item prop="phoneMobile" :rules="rules.phoneMobile">
                      <div class="phone-input-group">
                         <el-select
                            v-model="cardInfo.phoneMobileAreaCode"
                            filterable
                            allow-create
                            :clearable="true"
                            default-first-option
                            placeholder="區號"
                            class="area-code-input"
                          >
                            <el-option
                              v-for="code in countryCodes"
                              :key="code.dial_code"
                              :label="code.dial_code+code.name"
                              :value="code.dial_code"
                            />
                          </el-select>
                        <el-input
                          type="text"
                          placeholder="流動電話"
                          v-model="cardInfo.phoneMobile"
                          class="phone-number-input"
                        />
                      </div>
                    </el-form-item>
                  </div>
                </div>
              </div>
              <div class="field-column">
                <div class="field">
                  <span class="field-name">Title</span>
                  <!-- <input
                    type="text"
                    placeholder="Mr, Ms, Mrs, Others"
                    v-model="cardInfo.titleEn"
                  /> -->
                  <el-select
                    key="select"
                    class="field-select"
                    v-model="cardInfo.titleEn"
                    clearable
                    filterable
                    placeholder="Please select Title"
                  >
                    <el-option
                      v-for="item in cardTitleEn"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="field">
                  <span class="field-name">First Name</span>
                  <el-input
                    type="text"
                    placeholder="Tai-man"
                    v-model="cardInfo.firstNameEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Last Name</span>
                  <el-input
                    type="text"
                    placeholder="CHAN"
                    v-model="cardInfo.lastNameEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Honour</span>
                  <el-input
                    type="text"
                    placeholder="MH, JP"
                    v-model="cardInfo.honourEn"
                  />
                </div>

                <div class="field">
                  <span class="field-name">內地號碼</span>
                  <div class="field-input-container">
                    <el-form-item
                      prop="phoneMainland"
                      :rules="rules.phoneMainland"
                    >
                      <div class="phone-input-group">
                        <el-input
                          v-model="cardInfo.phoneMainlandAreaCode"
                          placeholder="+86"
                          class="area-code-input"
                        />
                        <el-input
                          type="text"
                          placeholder="請輸入內地號碼"
                          v-model="cardInfo.phoneMainland"
                          class="phone-number-input"
                        />
                      </div>
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <div class="section-title">{{ $t("organizationInfo") }}</div>
            <div class="divider"></div>
            <div class="fields">
              <div class="field-column">
                <div class="company-field">
                  <span class="field-name">機構名稱</span>

                  <!-- 编辑模式：显示输入框 -->
                  <div v-if="isEditingCompany" class="edit-mode">
                    <el-input
                      v-model="tempCompanyName"
                      clearable
                      @blur="saveCompanyName"
                      @keyup.enter="saveCompanyName"
                      @keyup.esc="cancelEdit"
                      placeholder="請輸入機構名稱"
                    />
                    <div class="edit-actions">
                      <el-button
                        size="mini"
                        type="success"
                        @click="saveCompanyName"
                      >
                       儲存</el-button>
                      <el-button size="mini"    type="warning" @click="cancelEdit"
                        > 取消</i
                      ></el-button
                      >
                    </div>
                  </div>

                  <!-- 选择模式：显示下拉选择器 -->
                  <div v-else class="select-mode">
                    <el-select
                      width="100%"
                      v-model="cardInfo.companyName"
                      clearable
                      filterable
                      allow-create
                      default-first-option
                      placeholder="請選擇機構名稱"
                      @change="onCompanySelected"
                    >
                      <el-option
                        v-for="company in companyList"
                        :key="company.id"
                        :label="company.name"
                        :value="company.name"
                      />
                    </el-select>
                    <el-button
                      v-if="cardInfo.companyName"
                      size="mini"
                      type="primary"
                      @click="startEditingCompany"
                    >
                      <i class="el-icon-edit-outline"></i>
                    </el-button>
                  </div>
                </div>
                <div class="field">
                  <span class="field-name">部門</span>
                  <el-input
                    type="text"
                    placeholder="部門"
                    v-model="cardInfo.department"
                  />
                </div>
                <div class="field">
                  <span class="field-name">職位</span>
                  <el-input
                    type="text"
                    placeholder="職位"
                    v-model="cardInfo.jobTitle"
                  />
                </div>
                <!-- <div class="field">
                  <span class="field-name">Email Address 電郵</span>
                  <div class="field-input-container">
                    <el-form-item prop="email" :rules="rules.email">
                      <el-input
                        type="text"
                        placeholder="Email Address"
                        v-model="cardInfo.email"
                      />
                    </el-form-item>
                  </div>
                </div> -->
       <div class="field">
            <span class="field-name">Email Address 電郵</span>
            <div class="email-fields-container">
              <!-- 主邮箱 -->
              <div class="email-field">
                <el-form-item prop="email" :rules="rules.email">
                  <div class="email-input-row">
                    <el-input
                      type="text"
                      placeholder="Email Address"
                      v-model="cardInfo.email"
                      class="email-input"
                    ></el-input>
                    <el-button
                      size="mini"
                      v-if="!showEmail2 && !showEmail3"
                      class="email-action-btn"
                      icon="el-icon-plus"
                      @click="showEmail2 = true"
                      title="Add additional email"
                    ></el-button>
                  </div>
                </el-form-item>
              </div>

              <!-- 第二个邮箱 -->
              <div class="email-field" v-if="showEmail2">
                <el-form-item prop="email1" :rules="rules.email1">
                  <div class="email-input-row">
                    <el-input
                      type="text"
                      placeholder="Email Address1"
                      v-model="cardInfo.email1"
                      class="email-input"
                    ></el-input>
                    <div class="email-action-group">
                      <el-button
                        size="mini"
                        v-if="!showEmail3"
                        class="email-action-btn"
                        icon="el-icon-plus"
                        @click="showEmail3 = true"
                        title="Add more email"
                      ></el-button>
                      <el-button
                        size="mini"
                        class="email-action-btn"
                        type="danger" 
                        icon="el-icon-minus"
                        @click="removeEmailField(2)"
                        title="Remove this email"
                      ></el-button>
                    </div>
                  </div>
                </el-form-item>
              </div>

              <!-- 第三个邮箱 -->
              <div class="email-field" v-if="showEmail3">
                <el-form-item prop="email2" :rules="rules.email2">
                  <div class="email-input-row">
                    <el-input
                      type="text"
                      placeholder="Email Address2"
                      v-model="cardInfo.email2"
                      class="email-input"
                    ></el-input>
                    <el-button
                      size="mini"
                      class="email-action-btn"
                      type="danger" 
                      icon="el-icon-minus"
                      @click="removeEmailField(3)"
                      title="Remove this email"
                    ></el-button>
                  </div>
                </el-form-item>
              </div>
            </div>
          </div>
                <div class="field">
                  <span class="field-name">
                    Phone (Direct Line)<br />
                    電話（直線）
                  </span>
                  <div class="field-input-container">
                    <el-form-item
                      prop="phoneDirectLine"
                      :rules="rules.phoneDirectLine"
                    >
                      <div class="phone-input-group">
                         <el-select
                            v-model="cardInfo.phoneDirectLineAreaCode"
                            filterable
                             :clearable="true"
                            allow-create
                            default-first-option
                            placeholder="區號"
                            class="area-code-input"
                          >
                            <el-option
                              v-for="code in countryCodes"
                              :key="code.dial_code"
                              :label="code.dial_code+code.name"
                              :value="code.dial_code"
                            />
                          </el-select>
                        <el-input
                          type="text"
                          placeholder="Phone (Direct Line)"
                          v-model="cardInfo.phoneDirectLine"
                          class="phone-number-input"
                        />
                      </div>
                    </el-form-item>
                  </div>
                </div>
                <div class="field">
                  <span class="field-name">Industry Type行業類型</span>
                  <el-input
                    type="text"
                    placeholder="Technology, Healthcare"
                    v-model="cardInfo.industryType"
                  />
                </div>

                <div class="field">
                  <span class="field-name">地址1</span>
                  <el-input
                    type="text"
                    placeholder="地址1"
                    v-model="cardInfo.addressLine1"
                  />
                </div>
                <div class="field">
                  <span class="field-name">地址2</span>
                  <el-input
                    type="text"
                    placeholder="地址2"
                    v-model="cardInfo.addressLine2"
                  />
                </div>
                <div class="field">
                  <span class="field-name">地區</span>
                  <el-input
                    type="text"
                    placeholder="地區"
                    v-model="cardInfo.district"
                  />
                </div>
                <div class="field">
                  <span class="field-name">區域</span>
                  <el-input
                    type="text"
                    placeholder="區域"
                    v-model="cardInfo.area"
                  />
                </div>
                <div class="field">
                  <span class="field-name">城市</span>
                  <el-input
                    type="text"
                    placeholder="城市"
                    v-model="cardInfo.city"
                  />
                </div>
              </div>
              <div class="field-column">
               <div class="field">
                <span class="field-name">Name of Organisation</span>
                
                <!-- 编辑模式：显示输入框 -->
                <div v-if="isEditingCompanyEn" class="edit-mode">
                  <el-input
                    v-model="tempCompanyNameEn"
                    clearable
                    @blur="saveCompanyNameEn"
                    @keyup.enter="saveCompanyNameEn"
                    @keyup.esc="cancelEditEn"
                    placeholder="Please enter organization name"
                  />
                  <div class="edit-actions">
                    <el-button
                      size="mini"
                      type="success"
                      @click="saveCompanyNameEn"
                    >Save</el-button>
                    <el-button 
                      size="mini"
                      type="warning"
                      @click="cancelEditEn"
                    >Cancel</el-button>
                  </div>
                </div>

                <!-- 选择模式：显示下拉选择器 -->
                <div v-else class="select-mode">
                  <el-select
                    v-model="cardInfo.companyNameEn"
                    clearable
                    filterable
                    allow-create
                    default-first-option
                    placeholder="Please select organization"
                    @change="onCompanySelectedEn"
                    class="field-select"
                  >
                    <el-option
                      v-for="company in companyEnList"
                      :key="company.id"
                      :label="company.name"
                      :value="company.name"
                    />
                  </el-select>
                  <el-button
                    v-if="cardInfo.companyNameEn"
                    size="mini"
                    type="primary"
                    @click="startEditingCompanyEn"
                  >
                    <i class="el-icon-edit-outline"></i>
                  </el-button>
                </div>
              </div>
                <div class="field">
                  <span class="field-name">Department</span>
                  <el-input
                    type="text"
                    placeholder="Department"
                    v-model="cardInfo.departmentEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Job Title</span>
                  <el-input
                    type="text"
                    placeholder="Job Title"
                    v-model="cardInfo.jobTitleEn"
                  />
                </div>

                <div class="field">
                  <span class="field-name">
                    Phone (Office)<br />電話（辦公室）
                  </span>
                  <div class="field-input-container">
                    <el-form-item prop="phoneOffice" :rules="rules.phoneOffice">
                      <div class="phone-input-group">
                        <!-- <el-input
                          v-model="cardInfo.phoneOfficeAreaCode"
                          placeholder="+852"
                          class="area-code-input"
                        /> -->
                          <el-select
                            v-model="cardInfo.phoneOfficeAreaCode"
                            filterable
                             :clearable="true"
                            allow-create
                            default-first-option
                            placeholder="區號"
                            class="area-code-input"
                          >
                            <el-option
                              v-for="code in countryCodes"
                              :key="code.dial_code"
                              :label="code.dial_code+code.name"
                              :value="code.dial_code"
                            />
                          </el-select>
                        <el-input
                          type="text"
                          placeholder="Phone (Office)"
                          v-model="cardInfo.phoneOffice"
                          class="phone-number-input"
                        />
                      </div>
                    </el-form-item>
                  </div>
                </div>
                <div class="field">
                  <span class="field-name"
                    >Fax Number (if applicable)傳真（如適用）</span
                  >
                  <el-input
                    type="text"
                    placeholder="Fax Number (if applicable)"
                    v-model="cardInfo.faxNumber"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Business Type 業務類型</span>
                  <transition name="fade" mode="out-in">
                    <!-- 下拉框 -->
                    <el-select
                      v-if="!showCustomInput"
                      key="select"
                      class="field-select"
                      v-model="cardInfo.businessType"
                      clearable
                      filterable
                      placeholder="请选择业务类型"
                      @change="handleBusinessTypeChange"
                    >
                      <el-option
                        v-for="item in businessTypes"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>

                    <!-- 输入框 -->
                    <el-input
                      v-else
                      key="input"
                      ref="customInput"
                      v-model="cardInfo.otherBusiness"
                      class="field-select"
                      placeholder="请输入具体业务类型"
                      clearable
                      @blur="handleCustomInputBlur"
                      @clear="resetToSelect"
                    />
                  </transition>
                </div>

                <div class="field">
                  <span class="field-name">Address Line 1</span>
                  <el-input
                    type="text"
                    placeholder="Address Line 1"
                    v-model="cardInfo.addressLine1En"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Address Line 2</span>
                  <el-input
                    type="text"
                    placeholder="Address Line 2"
                    v-model="cardInfo.addressLine2En"
                  />
                </div>
                <div class="field">
                  <span class="field-name">District</span>
                  <el-input
                    type="text"
                    placeholder="e.g. Tsim Sha Tsui"
                    v-model="cardInfo.districtEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Area</span>
                  <el-input
                    type="text"
                    placeholder="e.g. Kowloon"
                    v-model="cardInfo.areaEn"
                  />
                </div>
                <div class="field">
                  <span class="field-name">country</span>
                  <el-input
                    type="text"
                    placeholder="country"
                    v-model="cardInfo.countryEn"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 社交媒體 -->
          <div class="info-section">
            <div class="section-title">{{ $t("socialMedia") }}</div>
            <div class="divider"></div>
            <div class="fields">
              <div class="field-column">
                <div class="field">
                  <span class="field-name">LinkedIn Profile URL</span>
                  <el-input
                    type="text"
                    placeholder="LinkedIn Profile URL"
                    v-model="cardInfo.linkedinProfileUrl"
                  />
                </div>
                <div class="field">
                  <span class="field-name">Facebook Page URL</span>
                  <el-input
                    type="text"
                    placeholder="Facebook Page URL"
                    v-model="cardInfo.facebookPageUrl"
                  />
                </div>
              </div>
              <div class="field-column">
                <div class="field">
                  <span class="field-name">Instagram URL</span>
                  <el-input
                    type="text"
                    placeholder="Instagram URL"
                    v-model="cardInfo.instagramUrl"
                  />
                </div>
                <div class="field">
                  <span class="field-name">WeChat ID</span>
                  <el-input
                    type="text"
                    placeholder="WeChat ID"
                    v-model="cardInfo.wechatId"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-form>
        <template>
          <BusinessCardLabel
            :handleTag="handleTag"
            :cardInfo="cardInfo"
            class="hidden-sm-and-up"
            @update:cardInfo="cardInfo = $event"
          ></BusinessCardLabel>
        </template>
      </div>
    </div>

    <!-- 显示标签选择框 -->
    <TagSelect
      ref="tagSelect"
      @confirm="updateTags"
      :initialSelectedTags="splitTag"
    ></TagSelect>
  </div>
</template>

<script>
import * as CardApi from "@/api/pcm/card";
import * as CompanyApi from "@/api/pcm/company";
import { listAllSimple } from "@/api/pcm/company";
import TagSelect from "../table/TagSelect.vue";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";
import BusinessCardLabel from "./BusinessCardLabel.vue";
import { parsePhoneNumberFromString, AsYouType } from "libphonenumber-js";
import countryCodes from "@/assets/data/countryCodes.json";
import { mapActions, mapGetters } from "vuex";
export default {
  components: { TagSelect, BusinessCardLabel },
  name: "BusinessCardEditor",
  // 香港电话校验规则

  data() {
    // 电话号码验证方法
    const validatePhoneNumber = (fieldName, isMainland = false) => {
      return (rule, value, callback) => {
        const areaCodeField = `${fieldName}AreaCode`;
        const areaCode = this.cardInfo[areaCodeField] || "";
        const phoneNumber = value || "";

        // 两者都为空则不校验
        // if (!areaCode && !phoneNumber) {
        //   callback();
        //   return;
        // }
        // 如果号码为空，则不校验（无论是否有区号）
        if (!phoneNumber) {
          callback();
          return;
        }
        // 内地号码特殊处理
        if (isMainland) {
          // 强制设置为+86
          this.cardInfo[areaCodeField] = "+86";

          // 验证中国大陆手机号
          if (/^1[3-9]\d{9}$/.test(phoneNumber)) {
            callback();
          } else {
            callback(new Error(this.$t("errors.phone.mainlandNumber")));
          }
          return;
        }

        // 使用libphonenumber-js验证国际号码
        try {
          const phoneNumberObj = parsePhoneNumberFromString(
            areaCode + phoneNumber
          );

          if (phoneNumberObj && phoneNumberObj.isValid()) {
            // 自动格式化电话号码
            const formattedNumber = new AsYouType().input(
              areaCode + phoneNumber
            );
            this.cardInfo[fieldName] = formattedNumber.replace(areaCode, "");
            callback();
          } else {
            callback(new Error(this.$t("errors.phone.invalidNumber")));
          }
        } catch (error) {
          callback(new Error(this.$t("errors.phone.invalidNumber")));
        }
      };
    };

    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback(); // 空值不校验
        return;
      }
      // 基本的邮箱格式验证
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (regex.test(value)) {
        callback();
      } else {
        callback(new Error(this.$t("emailFormatError")));
      }
    };

    return {
      countryCodes, // 添加国家区号数据
      cardId: this.$route.params.cardId || null, // 修改为可能为null
      isEditMode: !!this.$route.params.cardId, // 新增标志位判断是编辑还是新增
      cardInfo: {
        // 香港电话（流动电话）
        phoneMobile: "", // 流动电话
        phoneMobileAreaCode: "+852", // 流动电话区号（默认+852）

        // 内地电话
        phoneMainland: "", // 内地号码
        phoneMainlandAreaCode: "+86", // 内地号码区号（默认+86）

        // 办公电话
        phoneOffice: "", // 办公电话
        phoneOfficeAreaCode: "+852", // 办公电话区号

        // 直线电话
        phoneDirectLine: "", // 直线电话
        phoneDirectLineAreaCode: "+852", // 直线电话区号
        // 确保包含这三个email字段
        email: "",
        email1: "",
        email2: "",
      },
      businessTypes: getDictDatas(DICT_TYPE.BUSINESS_TYPE),
      cardTitleCn: getDictDatas(DICT_TYPE.CARD_TITLE_CN),
      cardTitleEn: getDictDatas(DICT_TYPE.CARD_TITLE_EN),

      //业务类型相关
      showCustomInput: false,
      previousSelection: "",
      companyList: null, //公司下拉列表(中文)
      companyEnList: null, //公司下拉列表(英文)
      isEditingCompany: false, // 是否處於編輯模式
      tempCompanyName: "", // 臨時保存編輯中的機構名稱
      originalCompanyName: "", // 新增：保存编辑前的原始机构名称
      isEditingCompanyEn: false, // 英文机构名称编辑状态
      tempCompanyNameEn: "", // 临时保存的英文机构名称
      originalCompanyNameEn: "", // 原始英文机构名称
      showEmail2: false, // 控制第二个附加邮箱显示
      showEmail3: false, // 控制第三个附加邮箱显示

      rules: {
        phoneMobile: [
          { required: false },
          {
            validator: validatePhoneNumber("phoneMobile"),
            trigger: ["blur"],
          },
        ],
        phoneMainland: [
          { required: false },
          {
            validator: validatePhoneNumber("phoneMainland", true),
            trigger: ["blur"],
          },
        ],
        phoneOffice: [
          { required: false },
          {
            validator: validatePhoneNumber("phoneOffice"),
            trigger: ["blur"],
          },
        ],
        phoneDirectLine: [
          { required: false },
          {
            validator: validatePhoneNumber("phoneDirectLine"),
            trigger: ["blur"],
          },
        ],
        email: [{ validator: validateEmail, trigger: "blur" }], // 添加email验证规则
        email1: [{ validator: validateEmail, trigger: "blur" }],
        email2: [{ validator: validateEmail, trigger: "blur" }],
      },
    };
  },
  watch: {
    "cardInfo.phoneMobileAreaCode"(newVal) {
      this.$refs.form.validateField("phoneMobile");
    },
    "cardInfo.phoneMainlandAreaCode"(newVal) {
      this.$refs.form.validateField("phoneMainland");
    },
    "cardInfo.phoneOfficeAreaCode"(newVal) {
      this.$refs.form.validateField("phoneOffice");
    },
    "cardInfo.phoneDirectLineAreaCode"(newVal) {
      this.$refs.form.validateField("phoneDirectLine");
    },
  },
  computed: {
    ...mapGetters("pcm", [
      "nextCardId",
      "prevCardId",
      "isLastCard",
      "isFirstCard",
      "cardProgress",
    ]),
    splitTag() {
      if (this.cardInfo.customTags) {
        return this.cardInfo.customTags.split(";");
      }
      return [];
    },
  },
  mounted() {
    console.log(JSON.stringify(this.cardProgress))
    if (this.isEditMode) {
      this.getCardInfo();
    }
    this.getCompanyList();
    this.getCompanyEnList();
  },
  methods: {
    ...mapActions("pcm", ["setCardIds", "nextCard", "prevCard", "goToCard"]),
    getCardInfo() {
      CardApi.getCard(this.cardId).then((res) => {
        this.cardInfo = res.data;
        // 初始化email显示状态
        this.showEmail2 = !!this.cardInfo.email1;
        this.showEmail3 = !!this.cardInfo.email2;
      });
      // 如果有当前cardId，定位到对应索引
      if (this.cardId) {
        this.goToCard(this.cardId);
      }
    },
    getCompanyList() {
      listAllSimple("zh").then((res) => {
        this.companyList = res.data;
      });
    },
    getCompanyEnList() {
      listAllSimple("en").then((res) => {
        this.companyEnList = res.data;
      });
    },
    goBack() {
      this.$router.go(-1); // 或者 this.$router.back();
    },
    handleTag() {
      this.$refs["tagSelect"].open();
    },
    updateTags(tags) {
      CardApi.updateCard({
        id: this.cardId,
        customTags: tags.join(";"),
      }).then((res) => {
        this.getCardInfo();
        this.$modal.msgSuccess("成功修改標籤!");
      });
    },
    /**
     * 处理业务类型变化
     */
    handleBusinessTypeChange(value) {
      if (value === "other") {
        this.showCustomInput = true;
        this.cardInfo.otherBusiness = ""; // 清空之前可能存在的自定义输入

        // 自动聚焦到输入框
        this.$nextTick(() => {
          this.$refs.customInput?.focus();
        });
      } else {
        // 选择非"other"时，清空otherBusiness
        this.cardInfo.otherBusiness = "";
        this.showCustomInput = false;
      }
    },
    /**
     * 处理输入框失去焦点
     */
    handleCustomInputBlur() {
      if (!this.cardInfo.otherBusiness.trim()) {
        // 如果没有输入内容，重置为选择状态
        this.resetToSelect();
      }
    },

    /**
     * 重置为下拉选择
     */
    resetToSelect() {
      this.showCustomInput = false;
      this.cardInfo.otherBusiness = "";
      this.cardInfo.businessType = this.previousSelection || "";
    },
    //保存
    save() {
      this.submitForm("save");
    },
    //保存并下一个
    saveAndNext() {
      this.submitForm("saveAndNext");
    },
    loadNextCard() {
      const nextId = this.nextCardId;
      if (nextId) {
        this.$modal.msgSuccess("保存成功！");
        this.cardId = nextId;
        this.nextCard(); // 更新store中的索引
        this.getCardInfo();
      } else {
        this.$modal.msgWarning("已经是最后一张了");
      }
    },
    goBack() {
      this.$router.go(-1);
    },

    /** 提交按钮 */
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 如果选择了"other"但没有输入内容，则清空otherBusiness

          if (
            this.cardInfo.businessType === "other" &&
            !this.cardInfo.otherBusiness.trim()
          ) {
            this.cardInfo.otherBusiness = "";
          }
          const apiMethod = this.isEditMode
            ? CardApi.saveAndReturnPrevious
            : CardApi.createCard;

          apiMethod(this.cardInfo).then((response) => {
            if (this.isEditMode && "saveAndNext" === type) {
              // this.cardId = response.data.previousCardId;
              this.loadNextCard();
            } else {
              this.$modal.msgSuccess(this.isEditMode ? "保存成功" : "新增成功");
              if (!this.isEditMode) {
                // 新增成功后，可以跳转到编辑页面或者返回列表
                // this.$router.push(`/card/edit/${response.data.id}`);
                this.cardId = response.data.id;
                this.isEditMode = true;
              } else {
                this.getCardInfo();
              }
            }
          });
        }
      });
    },

    //動態機構
    // 開始編輯機構名稱
    startEditingCompany() {
      this.originalCompanyName = this.cardInfo.companyName || ""; // 保存原始名称
      this.tempCompanyName = this.cardInfo.companyName || "";
      this.isEditingCompany = true;
    },

    // 保存編輯的機構名稱
    saveCompanyName() {
      const newName = this.tempCompanyName.trim();
      if (newName && newName !== this.cardInfo.companyName) {
        this.cardInfo.companyName = newName;

        // 使用原始名称查找现有机构（关键修改）
        const existingCompany = this.companyList.find(
          (company) => company.name === this.originalCompanyName
        );

        if (existingCompany) {
          // 更新现有机构
          this.updateExistingCompany(existingCompany.id, newName);
        } else {
          // 创建新机构
          this.createNewCompany(newName);
        }
      }

      this.isEditingCompany = false;
    },

    // 取消編輯
    cancelEdit() {
      this.isEditingCompany = false;
    },

    // 處理下拉選擇事件
    onCompanySelected(value) {
      // 可選：在選擇後自動填充相關字段
      if (value) {
        const company = this.companyList.find((c) => c.name === value);
        if (company) {
          // 例如：自動填充公司相關信息
          console.log("原列表有item");
          // this.cardInfo.department = company.defaultDepartment;
        } else {
          console.log("原列表無item");
          this.createNewCompany(value);
        }
      }
    },

    // 更新現有機構（調用後端API）
    async updateExistingCompany(companyId, newName) {
      try {
        // 調用後端API更新機構名稱
        await CompanyApi.updateCompany({ id: companyId, name: newName });
        this.$message.success("機構名稱已更新");

        // 刷新公司列表
        this.getCompanyList();
      } catch (error) {
        this.$message.error("更新失敗：" + error.message);
      }
    },

    // 創建新機構（可選）
    async createNewCompany(name) {
      try {
        // 調用後端API創建新機構
        await CompanyApi.createCompany({ name: name });
        this.$message.success("新機構已創建");

        // 刷新公司列表
        this.getCompanyList();
      } catch (error) {
        this.$message.error("創建失敗：" + error.message);
      }
    },

    // 开始编辑英文机构名称
    startEditingCompanyEn() {
      this.originalCompanyNameEn = this.cardInfo.companyNameEn || "";
      this.tempCompanyNameEn = this.cardInfo.companyNameEn || "";
      this.isEditingCompanyEn = true;
    },

    // 保存编辑的英文机构名称
    saveCompanyNameEn() {
      const newName = this.tempCompanyNameEn.trim();
      if (newName && newName !== this.cardInfo.companyNameEn) {
        this.cardInfo.companyNameEn = newName;

        // 使用原始名称查找现有机构
        const existingCompany = this.companyEnList.find(
          (company) => company.name === this.originalCompanyNameEn
        );

        if (existingCompany) {
          // 更新现有机构
          this.updateExistingCompanyEn(existingCompany.id, newName);
        } else {
          // 创建新机构
          this.createNewCompanyEn(newName);
        }
      }
      this.isEditingCompanyEn = false;
    },

    // 取消编辑英文机构名称
    cancelEditEn() {
      this.isEditingCompanyEn = false;
    },

    // 处理英文机构下拉选择
    onCompanySelectedEn(value) {
      if (value) {
        const company = this.companyEnList.find((c) => c.name === value);
        if (company) {
          // 可以自动填充相关字段
        } else {
          this.createNewCompanyEn(value);
        }
      }
    },

    // 更新现有英文机构
    async updateExistingCompanyEn(companyId, newName) {
      try {
        await CompanyApi.updateCompany({ id: companyId, name: newName });
        this.$message.success("Organization name updated");
        this.getCompanyEnList();
      } catch (error) {
        this.$message.error("Update failed: " + error.message);
      }
    },

    // 创建新英文机构
    async createNewCompanyEn(name) {
      try {
        await CompanyApi.createCompany({ compannameNameEn: name });
        this.$message.success("New organization created");
        this.getCompanyEnList();
      } catch (error) {
        this.$message.error("Creation failed: " + error.message);
      }
    },

    // 区号变化处理
    handleAreaCodeChange(value) {
      // 可以在这里添加区号变化时的逻辑
    },

    removeEmailField(fieldNumber) {
      if (fieldNumber === 2) {
        // 如果第三个邮箱存在，则上移
        if (this.showEmail3) {
          this.cardInfo.email1 = this.cardInfo.email2;
          this.cardInfo.email2 = "";
          this.showEmail3 = false;
        } else {
          // 否则直接关闭第二个邮箱
          this.showEmail2 = false;
          this.cardInfo.email1 = "";
        }
      } else if (fieldNumber === 3) {
        // 直接关闭第三个邮箱
        this.showEmail3 = false;
        this.cardInfo.email2 = "";
      }
    },
  },
};
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f0f4fa;
  overflow-y: auto; /* 启用滚动条 */
}

.header-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0; /* 防止标题卡片被压缩 */
}

.header-left {
  display: flex;
  align-items: center;
}

.back-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  margin-right: 10px;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
}

.btn {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 5px;
  margin-left: 10px;
  cursor: pointer;
}

.cancel,
.save-next {
  background-color: transparent;
  border: 1px solid #1f1f1f;
  color: #1f1f1f;
}

.save {
  background-color: #1ea235;
  color: #ffffff;
  border: none;
}

.content {
  display: flex;
  flex: 1;
  margin-top: 10px;
  overflow: hidden; /* 防止内容溢出 */
}

.card {
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 卡片内容超出时启用滚动条 */
}

.image-card {
  flex: 1;
  margin-right: 10px;
}

.info-card {
  flex: 2;
  margin-left: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
}

.edit-tags {
  background-color: transparent;
  border: none;
  color: #1ea235;
  cursor: pointer;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 10px 0;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.tag {
  background-color: transparent;
  color: #1f1f1f;
  padding: 5px 10px;
  border-radius: 8px;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid #0000001a;
}

.card-image {
  width: 100%;
  max-height: 776px;
  border-radius: 10px;
  border: 1px solid #0000001a;
  object-fit: cover;
}

.info-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.fields {
  display: flex;
  flex-wrap: wrap;
}

.field-column {
  flex: 1;
  margin-right: 20px;
}

.field {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

@media screen and (max-width: 768px) {
  .field {
    flex-direction: column;
    align-items: normal;
  }
  .info-card {
    margin-left: 0;
  }
  .field-name {
    padding-bottom: 5px;
    padding-left: 10px;
  }
}

.field-name {
  width: 150px;
  font-size: 12px;
  color: #333333;
}

.field-select {
  width: 100%;
  border-radius: 5px;
}

input {
  flex: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;

  margin-right: 10px;
}

.add-remove {
  background-color: transparent;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  margin-left: 5px;
}

/* 仅针对联系方式区域的样式调整 */
.contact-info .fields {
  display: flex;
  flex-direction: column;
}

.contact-info .field {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.contact-info .field-name {
  width: 80px;
  font-size: 14px;
  color: #333333;
  margin-right: 10px;
}

.contact-info input {
  flex: 1;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.contact-info .button-group {
  display: flex;
  margin-left: 10px;
}

.contact-info .add-remove {
  background-color: transparent;
  border: 1px solid #e0e0e0;
  color: #666666;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  margin-left: 5px;
}
/* 添加过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.field-input-container {
  width: 100%;
  position: relative; /* 为错误提示定位 */
}

.field-input-container /deep/ .el-form-item {
  margin-bottom: 0px;
}

.field-input-container /deep/ .el-form-item__error {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  white-space: normal; /* 允许换行 */
  font-size: 12px !important;
  padding-top: 2px;
  color: #f56c6c;
  z-index: 9;
}

.phone-input-group {
  display: flex;
  width: 100%;
}

.area-code-input {
  width: 40%;
  margin-right: 8px;
}

.phone-number-input {
  flex: 1;
}

.company-field {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.field-name {
  width: 150px;
  font-size: 12px;
  color: #333;
}

.select-mode {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 10px;
}

.select-mode /deep/ .el-select {
  width: 100%;
}

.edit-mode {
  display: flex;
  flex-direction: row;
  gap: 5px;
  width: 100%;
  align-items: center;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}

.email-fields-container {
  width: 100%;
}

.email-field {
  margin-bottom: 10px;
}

.email-input-row {
  display: flex;
  align-items: center;
  gap: 10px; /* 控制输入框和按钮之间的间距 */
  width: 100%;
}

.email-fields-container /deep/ .el-form-item__error {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  white-space: normal; /* 允许换行 */
  font-size: 12px !important;
  padding-top: 2px;
  color: #f56c6c;
  z-index: 9;
}

.email-fields-container /deep/ .el-form-item {
  margin-bottom: 15px;
}

.email-input {
  flex: 1; /* 让输入框占据剩余空间 */
}

.email-action-btn {
  flex-shrink: 0; /* 防止按钮被压缩 */
  margin-left: 0; /* 移除默认左边距 */
}

.email-action-group {
  display: flex;
  gap: 5px; /* 按钮之间的间距 */
}

/* 保持原有的附加邮箱缩进效果 */
.email-field:nth-child(2),
.email-field:nth-child(3) {
  margin-left: 20px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .el-form-item__error {
    position: static; /* 移动端错误信息显示在下方 */
    margin-top: 4px;
  }
  .email-input-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .email-action-group {
    align-self: flex-end;
  }

  .email-field:nth-child(2),
  .email-field:nth-child(3) {
    margin-left: 10px;
  }
}
</style>