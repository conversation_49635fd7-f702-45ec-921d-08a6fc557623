<template>
  <div class="label-page">
    <div class="card-header" v-if="cardInfo.id != null">
      <span class="card-title">{{ $t("tag") }}</span>
      <button class="edit-tags" @click="handleTag">
        {{ $t("editTags") }}
      </button>
    </div>
    <div class="divider"></div>
    <div class="tags">
      <span class="tag" v-for="(tag, index) in cardInfo.tags" :key="index"
        ><i class="el-icon-price-tag" />{{ tag.name }}</span
      >
    </div>
    <!-- <img :src="cardInfo.imageUrl" alt="名片實圖" class="card-image" /> -->
    <BusinessCardImage
      :frontImage="cardInfo.imageUrl"
      :backImage="cardInfo.backUrl"
      @update:frontImage="updateImage('imageUrl', $event)"
      @update:backImage="updateImage('backUrl', $event)"
    />
  </div>
</template>

<script>
import BusinessCardImage from "./BusinessCardImage.vue";
export default {
  components: { BusinessCardImage },
  name: "BusinessCardLabel",
  props: {
    handleTag: {
      type: Function,
      default: () => {},
    },
    cardInfo: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    updateImage(key, value) {
      this.$set(this.cardInfo, key, value);
      // 或者如果使用 Vue 3 的 reactive
      this.cardInfo[key] = value;
      console.log("Updated image:", key, value);
    },
  },
};
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title {
  font-size: 18px;
  font-weight: bold;
}

.edit-tags {
  background-color: transparent;
  border: none;
  color: #1ea235;
  cursor: pointer;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 10px 0;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}
.card-image {
  width: 100%;
  max-height: 776px;
  border-radius: 10px;
  border: 1px solid #0000001a;
  object-fit: cover;
}
.tag {
  background-color: transparent;
  color: #1f1f1f;
  padding: 5px 10px;
  border-radius: 8px;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid #0000001a;
}
</style>
