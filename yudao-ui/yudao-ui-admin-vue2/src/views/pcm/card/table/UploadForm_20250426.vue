<template>
  <div class="search-form-container">
    <image-cropper ref="cropper" @cropped="handleCroppedFile" />

    <base-dialog
      v-model="uploadDialogVisible"
      :title="$t('uploadCardImage')"
      width="650px"
      @confirm="handleUploadConfirm"
    >
      <template v-slot:content>
        <div class="upload-container">
          <!-- 上传按钮 - 移除了el-upload组件 -->
          <div class="upload-button" @click="triggerFileInput">
            <div class="upload-content">
              <i class="el-icon-upload"></i>
              <div class="upload-text">
                {{ files.length > 0 ? $t("continueUploading") : $t("uploadCard") }}
              </div>
            </div>
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="handleFileChange"
              multiple
            />
          </div>

          <!-- 图片展示区域 -->
          <div class="image-list">
            <div
              v-for="(file, index) in files"
              :key="file.id"
              class="image-item"
            >
              <img :src="file.url" alt="名片图片" class="image-preview" />
              <i
                class="el-icon-check select-icon"
                :class="{ 'selected-icon': isSelected(file.id) }"
                @click="handleSelect(file.id)"
              ></i>
              <i
                class="el-icon-delete delete-icon"
                @click="handleDelete(index, file.id)"
              ></i>
            </div>
          </div>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import ImageCropper from "@/components/ImageCropper";
import BaseDialog from "@/components/PcmDialog";
import { deleteFile } from "@/api/infra/file.js";
import { recognize,carUpload } from "@/api/pcm/card";

export default {
  components: { BaseDialog, ImageCropper },
  data() {
    return {
      uploadDialogVisible: false,
      files: [], // 上传的文件列表
      selectedFiles: [], // 选中的图片数组
      limit: 20,
    };
  },
  methods: {
    open() {
      this.uploadDialogVisible = true;
    },
    
    // 触发文件选择
    triggerFileInput() {
      this.$refs.fileInput.click();
    },
    
    // 处理文件选择
    handleFileChange(event) {
      const files = event.target.files;
      if (files.length === 0) return;
      
      // 校验文件
      const file = files[0];
      if (!this.validateFile(file)) return;
      
      // 显示裁剪对话框
      this.$refs.cropper.open(file);
      event.target.value = ''; // 重置input，允许重复选择同一文件
    },
    
    // 文件校验
    validateFile(file) {
      // 校验文件格式
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!validTypes.includes(file.type.toLowerCase())) {
        this.$message.warning("請檢查上傳圖片格式，僅支援PNG、JPG、JPEG圖片格式");
        return false;
      }
      
      // 校验文件大小
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        this.$message.warning(`請上傳少於${maxSize / (1024 * 1024)}MB的圖片`);
        return false;
      }
      
      return true;
    },
    
    // 处理裁剪后的文件上传
    async handleCroppedFile(croppedFile) {
      try {
        const formData = new FormData();
        formData.append("files", croppedFile);
        const response = await carUpload(formData);
        // 处理上传成功的响应
        this.handleUploadSuccess(response);
      } catch (error) {
        console.error("上传失败:", error);
        this.$message.error(this.$t("uploadFailed"));
      }
    },
    
    // 上传成功处理
    handleUploadSuccess(responseData) {
      // 假设响应格式为 { data: [{ id: '123', url: '...' }] }
      const uploadedFile = responseData.data[0];
      this.files.push(uploadedFile);
      
      // 自动选中最新上传的图片
      this.selectedFiles = [uploadedFile.id];
    },
    
    // 确认上传
    async handleUploadConfirm() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning("请选择要上传的名片！");
        return;
      }
      
      try {
        const res = await recognize(this.selectedFiles);
        this.$router.push({
          name: "CardEdit",
          params: { cardId: res.data[0] },
        });
        this.uploadDialogVisible = false;
      } catch (error) {
        console.error("识别失败:", error);
        this.$message.error("名片识别失败");
      }
    },
    
    // 删除图片
    async handleDelete(index, fileId) {
      try {
        await deleteFile(fileId);
        this.files.splice(index, 1);
        this.selectedFiles = this.selectedFiles.filter((id) => id !== fileId);
      } catch (error) {
        console.error("删除失败:", error);
        this.$message.error("删除图片失败");
      }
    },
    
    // 选择图片
    handleSelect(fileId) {
      const index = this.selectedFiles.findIndex((id) => id === fileId);
      if (index === -1) {
        this.selectedFiles.push(fileId);
      } else {
        this.selectedFiles.splice(index, 1);
      }
    },
    
    // 判断图片是否被选中
    isSelected(fileId) {
      return this.selectedFiles.includes(fileId);
    }
  }
};
</script>

<style scoped>
/* 保持原有的样式不变 */
.upload-container {
  display: flex;
  gap: 16px;
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
  box-sizing: border-box;
}

.upload-button,
.image-item {
  width: 131px;
  height: 87px;
  flex: 0 0 auto;
}

.upload-button {
  border: 1px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #f5f7fa;
  transition: border-color 0.3s;
  position: relative;
}

.upload-button:hover {
  border-color: #409eff;
}

.upload-content {
  text-align: center;
}

.upload-content i {
  font-size: 24px;
  color: #909399;
}

.upload-text {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.select-icon,
.delete-icon {
  position: absolute;
  cursor: pointer;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 4px;
  transition: background-color 0.3s;
}

.select-icon {
  top: 8px;
  right: 8px;
}

.selected-icon {
  background-color: #409eff;
}

.delete-icon {
  bottom: 8px;
  right: 8px;
}

.select-icon:hover,
.delete-icon:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
</style>