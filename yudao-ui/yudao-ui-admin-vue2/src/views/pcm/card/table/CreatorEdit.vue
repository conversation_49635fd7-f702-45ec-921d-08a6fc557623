<template>
  <div>
    <base-dialog
      v-model="userDialogVisible"
      :title="title"
      @confirm="handleConfirm"
    >
      <template v-slot:content>
        <div class="user-selector">
          <el-input
            v-model="searchQuery"
            :placeholder="$t('searchPlaceholder')"
            class="search-input"
            clearable
          >
            <template #prefix >
              <i class="el-icon-search"></i>
            </template>
          </el-input>

          <div class="user-list-container">
            <div class="user-list" ref="userList">
              <div
                v-for="user in filteredUsers"
                :key="user.id"
                class="user-item"
                :class="{ selected: isSelected(user.id) }"
                @click="selectUser(user)"
              >
                <div class="user-avatar">
                  <img v-if="user.avatar" :src="user.avatar" alt="avatar" />
                  <div
                    v-else
                    class="avatar-placeholder"
                    :style="{ backgroundColor: getRandomColor(user.nickname) }"
                  >
                    {{ getFirstChar(user.nickname) }}
                  </div>
                </div>
                <div class="user-info">
                  <div class="nickname">{{ user.nickname }}</div>
                  <div class="dept" v-if="user.deptName">
                    {{ user.deptName }}
                  </div>
                </div>
                <div class="selection-indicator" v-if="isSelected(user.id)">
                  <i class="el-icon-check"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { listSimpleUsers } from "@/api/system/user";

export default {
  name: "SingleUserSelector",
  components: { BaseDialog },
  props: {
    title: {
      type: String,
      default: "selectUser",
    },
    selectedId: {
      type: [Number, String],
      default: null,
    },
  },
  data() {
    return {
      userDialogVisible: false,
      users: [],
      selectedUserId: null,
      searchQuery: "",
    };
  },
  computed: {
    filteredUsers() {
      if (!this.searchQuery) {
        return this.users;
      }
      const query = this.searchQuery.toLowerCase();
      return this.users.filter(
        (user) =>
          user.nickname.toLowerCase().includes(query) ||
          (user.deptName && user.deptName.toLowerCase().includes(query))
      );
    },
  },
  watch: {
    selectedId: {
      immediate: true,
      handler(newVal) {
        this.selectedUserId = newVal;
      },
    },
  },
  methods: {
    async open() {
      this.userDialogVisible = true;
      this.searchQuery = "";
      if (this.users.length === 0) {
        await this.fetchUsers();
      }
    },

    async fetchUsers() {
      try {
        const res = await listSimpleUsers();
        this.users = res.data || [];
      } catch (error) {
        console.error("Failed to fetch users:", error);
        this.users = [];
      }
    },

    handleConfirm() {
      this.$emit("confirm", this.selectedUserId);
      this.userDialogVisible = false;
    },

    selectUser(user) {
      this.selectedUserId = user.id;
    },

    isSelected(userId) {
      return this.selectedUserId === userId;
    },

    getFirstChar(nickname) {
      return nickname ? nickname.charAt(0).toUpperCase() : "";
    },

    getRandomColor(nickname) {
      if (!nickname) return "#409EFF";
      let hash = 0;
      for (let i = 0; i < nickname.length; i++) {
        hash = nickname.charCodeAt(i) + ((hash << 5) - hash);
      }
      const hue = hash % 360;
      return `hsl(${hue}, 70%, 65%)`;
    },

    clearSelected() {
      this.selectedUserId = null;
    },
  },
};
</script>

<style scoped lang="scss">
.user-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.search-input {
  margin-bottom: 12px;
}

.user-list-container {
  flex: 1;
  overflow: hidden;
}

.user-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 12px;
  padding: 8px;
  overflow-y: auto;
  max-height: 400px;
}

.user-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #ebeef5;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #c0e3ff;
  }
}

.user-item.selected {
  background-color: #f5faff;
  border-color: #a0d0ff;
}

.user-avatar {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.user-info {
  width: 100%;
  text-align: center;
}

.nickname {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dept {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #409eff;
  font-size: 16px;
  background: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

</style>