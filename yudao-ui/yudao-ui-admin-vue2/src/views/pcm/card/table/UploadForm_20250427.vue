<template>
  <div class="search-form-container">
    <image-cropper ref="cropper" @cropped="handleCroppedFile" />

    <base-dialog
      v-model="uploadDialogVisible"
      :title="$t('card.uploadCardImage')"
      width="800px"
      @confirm="handleUploadConfirm"
    >
      <template v-slot:content>
        <div class="card-upload-container">
          <!-- 操作工具栏 -->
          <div class="toolbar">
            <el-checkbox 
              v-model="allSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              {{ $t('common.selectAll') }}
            </el-checkbox>
            
            <div class="upload-button" @click="startNewCardUpload">
              <i class="el-icon-plus"></i>
              <span>{{ $t('card.uploadNewCard') }}</span>
            </div>
          </div>
          
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleFileChange"
          />

          <!-- 名片列表 -->
          <div class="card-list">
            <div 
              v-for="(card, index) in cards" 
              :key="index"
              class="card-item"
              :class="{ 
                'selected': isCardSelected(card),
                'invalid': !card.frontFileId
              }"
            >
              <el-checkbox 
                v-model="card.checked"
                :disabled="!card.frontFileId"
                @change="handleCardSelect(card)"
                class="card-checkbox"
              />
              
              <div class="card-images">
                <!-- 正面 -->
                <div class="card-side front-side">
                  <img 
                    v-if="card.frontFileId" 
                    :src="getFileUrl(card.frontFileId)" 
                    :alt="$t('card.frontSide')"
                    @click="editCardSide(index, 'front')"
                  >
                  <div v-else class="empty-side" @click="editCardSide(index, 'front')">
                    <i class="el-icon-picture"></i>
                    <span>{{ $t('card.frontSideRequired') }}</span>
                  </div>
                  <i v-if="!card.frontFileId" class="el-icon-warning required-icon"></i>
                </div>
                
                <!-- 反面 -->
                <div class="card-side back-side">
                  <img 
                    v-if="card.backFileId" 
                    :src="getFileUrl(card.backFileId)" 
                    :alt="$t('card.backSide')"
                    @click="editCardSide(index, 'back')"
                  >
                  <div v-else class="empty-side" @click="editCardSide(index, 'back')">
                    <i class="el-icon-picture"></i>
                    <span>{{ $t('card.backSideOptional') }}</span>
                  </div>
                </div>
              </div>
              
              <i class="el-icon-delete delete-card" @click="deleteCard(index)"></i>
            </div>
          </div>
          
          <!-- 底部提示信息 -->
          <div class="footer-tips">
            <div v-if="hasInvalidCards" class="invalid-tip">
              <i class="el-icon-warning"></i>
              <span>{{ $t('card.completeUploadTip') }}</span>
            </div>
            <div class="selected-count">
              {{ $t('card.selectedCount', { count: selectedCards.length }) }}
            </div>
          </div>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import ImageCropper from "@/components/ImageCropper";
import BaseDialog from "@/components/PcmDialog";
import { deleteFile } from "@/api/infra/file.js";
import { doubleSidedCard, carUpload } from "@/api/pcm/card";

export default {
  components: { BaseDialog, ImageCropper },
  data() {
    return {
      uploadDialogVisible: false,
      cards: [],
      allSelected: false,
      isIndeterminate: false,
      currentUpload: {
        cardIndex: null,
        side: 'front'
      },
      allFiles: {},
    };
  },
  computed: {
    selectedCards() {
      return this.cards
        .filter(card => card.checked && card.frontFileId)
        .map(card => ({
          frontFileId: card.frontFileId,
          backFileId: card.backFileId || null
        }));
    },
    hasInvalidCards() {
      return this.cards.some(card => !card.frontFileId);
    }
  },
  methods: {
    open() {
      this.uploadDialogVisible = true;
      this.cards = [];
      this.allSelected = false;
      this.isIndeterminate = false;
    },
    
    startNewCardUpload() {
      const newCard = { 
        frontFileId: null, 
        backFileId: null,
        checked: false
      };
      this.cards.push(newCard);
      this.currentUpload.cardIndex = this.cards.length - 1;
      this.currentUpload.side = 'front';
      this.$refs.fileInput.click();
    },
    
    editCardSide(cardIndex, side) {
      this.currentUpload.cardIndex = cardIndex;
      this.currentUpload.side = side;
      this.$refs.fileInput.click();
    },
    
    handleFileChange(event) {
      const files = event.target.files;
      if (files.length === 0) return;
      
      const file = files[0];
      if (!this.validateFile(file)) return;
      
      this.$refs.cropper.open(file);
      event.target.value = '';
    },
    
    validateFile(file) {
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!validTypes.includes(file.type.toLowerCase())) {
        this.$message.warning(this.$t('card.invalidFileType'));
        return false;
      }
      
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.warning(this.$t('card.fileSizeExceeded', { size: maxSize / (1024 * 1024) }));
        return false;
      }
      
      return true;
    },
    
    async handleCroppedFile(croppedFile) {
      try {
        const formData = new FormData();
        formData.append("files", croppedFile);
        const response = await carUpload(formData);
        
        const uploadedFile = response.data[0];
        this.allFiles[uploadedFile.id] = uploadedFile;
        
        const { cardIndex, side } = this.currentUpload;
        this.cards[cardIndex][`${side}FileId`] = uploadedFile.id;
        
        if (side === 'front') {
          this.cards[cardIndex].checked = true;
          this.updateSelectStatus();
          
          this.$confirm(this.$t('card.uploadBackSideConfirm'), this.$t('common.tip'), {
            confirmButtonText: this.$t('card.uploadBackSide'),
            cancelButtonText: this.$t('common.notNow'),
            type: 'info'
          }).then(() => {
            this.editCardSide(cardIndex, 'back');
          }).catch(() => {});
        }
      } catch (error) {
        console.error("Upload failed:", error);
        this.$message.error(this.$t('card.uploadFailed'));
      }
    },
    
    getFileUrl(fileId) {
      return this.allFiles[fileId]?.url || '';
    },
    
    isCardSelected(card) {
      return card.checked && card.frontFileId;
    },
    
    handleCardSelect(card) {
      if (!card.checked) {
        this.allSelected = false;
      }
      this.updateSelectStatus();
    },
    
    handleSelectAll(val) {
      this.cards.forEach(card => {
        if (card.frontFileId) {
          card.checked = val;
        }
      });
      this.isIndeterminate = false;
    },
    
    updateSelectStatus() {
      const validCards = this.cards.filter(card => card.frontFileId);
      const checkedCount = validCards.filter(card => card.checked).length;
      
      this.allSelected = checkedCount === validCards.length && validCards.length > 0;
      this.isIndeterminate = checkedCount > 0 && checkedCount < validCards.length;
    },
    
    async deleteCard(index) {
      try {
        await this.$confirm(this.$t('card.deleteConfirm'), this.$t('common.tip'), {
          type: 'warning'
        });
        
        const card = this.cards[index];
        if (card.frontFileId) await deleteFile(card.frontFileId);
        if (card.backFileId) await deleteFile(card.backFileId);
        
        this.cards.splice(index, 1);
        this.updateSelectStatus();
        this.$message.success(this.$t('common.deleteSuccess'));
      } catch (error) {
        if (error !== 'cancel') {
          console.error("Delete failed:", error);
          this.$message.error(this.$t('card.deleteFailed'));
        }
      }
    },
    
    async handleUploadConfirm() {
      if (this.selectedCards.length === 0) {
        this.$message.warning(this.$t('card.selectAtLeastOne'));
        return;
      }
      
      if (this.hasInvalidCards) {
        this.$message.warning(this.$t('card.completeUploadFirst'));
        return;
      }
      
      try {
        const res = await doubleSidedCard({fileIds:this.selectedCards});
        this.$router.push({
          name: "CardEdit",
          params: { cardId: res.data[0] },
        });
        this.uploadDialogVisible = false;
      } catch (error) {
        console.error("Recognition failed:", error);
        this.$message.error(this.$t('card.recognitionFailed'));
      }
    }
  }
};
</script>



<style scoped>
.card-upload-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-height: 600px;
  overflow-y: auto;
  padding: 10px;
}

.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background-color: #409eff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-button:hover {
  background-color: #66b1ff;
}

.upload-button i {
  margin-right: 8px;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.card-item {
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s;
}

.card-item.selected {
  border-color: #409eff;
  box-shadow: 0 2px 12px 0 rgba(64, 158, 255, 0.2);
}

.card-item.invalid {
  background-color: #f5f7fa;
}

.card-checkbox {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 1;
}

.card-images {
  display: flex;
  gap: 10px;
}

.card-side {
  flex: 1;
  height: 150px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  border: 1px dashed #dcdfe6;
}

.front-side::before, .back-side::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 5px;
  padding: 2px 5px;
  font-size: 12px;
  color: white;
  border-radius: 2px;
}

.front-side::before {
  content: '正面';
  background-color: #67c23a;
}

.back-side::before {
  content: '反面';
  background-color: #e6a23c;
}

.card-side img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.empty-side {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
}

.empty-side i {
  font-size: 24px;
  margin-bottom: 8px;
}

.empty-side span {
  font-size: 12px;
}

.required-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  color: #f56c6c;
  opacity: 0.7;
}

.delete-card {
  position: absolute;
  top: 5px;
  right: 5px;
  color: #f56c6c;
  cursor: pointer;
  font-size: 18px;
}

.delete-card:hover {
  color: #f78989;
}

.footer-tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #ebeef5;
}

.invalid-tip {
  color: #e6a23c;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.invalid-tip i {
  margin-right: 5px;
}

.selected-count {
  font-size: 12px;
  color: #909399;
}
</style>