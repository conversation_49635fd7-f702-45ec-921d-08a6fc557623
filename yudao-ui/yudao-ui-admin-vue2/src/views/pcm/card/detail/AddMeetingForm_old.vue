<template>
  <div class="search-form-container">
    <base-dialog
      v-model="meetingDialogVisible"
      :title="$t('addNegotiationRecord')"
      @confirm="handleSearchConfirm"
    >
      <template v-slot:content>
        <div class="form-container">
          <el-form
            ref="form"
            :model="formData"
            label-position="top"
            size="mini"
            :rules="rules"
          >
            <el-form-item :label="$t('type')" prop="type">
              <div class="tag-group">
                <el-tag
                  v-for="(tag, index) in tags"
                  :key="index"
                  :type="formData.type === tag.value ? 'success' : 'info'"
                  @click="selectTag(tag)"
                  :disable-transitions="false"
                  :closable="false"
                >
                  {{ tag.label }}
                </el-tag>
              </div>
            </el-form-item>
            <el-form-item :label="$t('content')" prop="content">
              <el-input
                type="textarea"
                :rows="5"
                v-model="formData.content"
                :placeholder="$t('enterContent')"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item>
              <div class="upload-instructions">{{ $t("uploadHint") }}</div>
              <el-upload
                class="upload-demo"
                ref="upload"
                name="files"
                :action="url"
                :multiple="true"
                :on-success="handleFileSuccess"
                :before-upload="beforeUpload"
                :file-list="files"
                :auto-upload="true"
                :on-exceed="handleExceed"
                :limit="limit"
                :headers="headers"
                list-type="picture-card"
                :on-remove="handleFileRemove"
                :on-preview="handlePictureCardPreview"
              >
                <i v-show="showUploadButton" class="el-icon-plus"></i>
              </el-upload>
              <el-dialog :visible.sync="dialogVisible" append-to-body>
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
            </el-form-item>
            <el-form-item :label="$t('time')" prop="meetingTime">
              <el-date-picker
                v-model="formData.meetingTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                :placeholder="$t('selectTime')"
              />
            </el-form-item>
            <el-form-item :label="$t('location')">
              <el-input
                v-model="formData.occasion"
                :placeholder="$t('addLocationOptional')"
              />
            </el-form-item>
          </el-form>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { DICT_TYPE, getDictDatas } from "@/utils/dict";
import { getAccessToken } from "@/utils/auth";
import { deleteFile } from "@/api/infra/file.js";
import * as MeetingApi from "@/api/pcm/meeting";

export default {
  name: "MeetingRecordForm",
  components: { BaseDialog },
  data() {
    return {
      meetingDialogVisible: false,
      formData: {
        type: "",
        cardId: null,
        content: "",
        imageUrl: "",
        meetingTime: "",
        occasion: "",
      },
      headers: {
        Authorization: "",
      },
      url: process.env.VUE_APP_BASE_API + "/admin-api/pcm/ocr/upload",
      limit: 1,
      tags: getDictDatas(DICT_TYPE.MEETING_TYPE),
      files: [],
      dialogVisible: false,
      dialogImageUrl: "",
      rules: {
        type: [{ required: true, message: "類型不能為空", trigger: "blur" }],
        content: [{ required: true, message: "內容不能為空", trigger: "blur" }],
        meetingTime: [
          { required: true, message: "時間不能為空", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    showUploadButton() {
      return this.files.length < this.limit;
    },
  },
  created() {
    this.headers.Authorization = `Bearer ${getAccessToken()}`;
  },
  methods: {
    selectTag(tag) {
      this.formData.type = tag.value;
    },

    // 优化后的文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code === 0 && response.data?.length) {
        const uploadedFile = response.data[0];
        this.files = [
          {
            ...uploadedFile,
            uid: file.uid,
            name: file.name,
            url: uploadedFile.url,
            status: "success",
          },
        ];
        this.formData.imageUrl = uploadedFile.url;
      } else {
        this.$message.error(response.msg || this.$t("upload.failed"));
      }
    },

    // 优化后的文件删除处理
    handleFileRemove(file, fileList) {
      const deleteFileId = file.response?.data?.[0]?.id || file.id;
      if (!deleteFileId) {
        this.files = [];
        this.formData.imageUrl = "";
        return;
      }

      deleteFile(deleteFileId)
        .then(() => {
          this.files = [];
          this.formData.imageUrl = "";
        })
        .catch((error) => {
          this.$message.error(this.$t("upload.deleteFailed"));
          console.error("Delete file error:", error);
        });
    },

    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url || file.response?.data?.[0]?.url;
      this.dialogVisible = true;
    },

    // 上传前校验
    beforeUpload(file) {
      const isImage = ["image/jpeg", "image/png", "image/gif"].includes(
        file.type
      );
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isImage) {
        this.$message.error(this.$t("upload.imageTypeError"));
        return false;
      }
      if (!isLt5M) {
        this.$message.error(this.$t("upload.imageSizeError", { size: 5 }));
        return false;
      }
      return true;
    },

    // 超出限制处理
    handleExceed(files, fileList) {
      this.$message.warning(
        this.$t("upload.limitExceeded", {
          limit: this.limit,
          selected: fileList.length,
          attempted: files.length,
        })
      );
    },

    open(cardId) {
      this.reset();
      this.formData.cardId = cardId;
      this.meetingDialogVisible = true;
    },

    reset() {
      this.formData = {
        type: "",
        content: "",
        imageUrl: "",
        meetingTime: "",
        occasion: "",
      };
      this.files = [];
      this.$refs.form?.resetFields();
    },

    handleSearchConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          MeetingApi.createMeeting(this.formData)
            .then(() => {
              this.$message.success(this.$t("meeting.addSuccess"));
              this.meetingDialogVisible = false;
              this.$emit("confirm");
            })
            .catch((error) => {
              this.$message.error(
                error.message || this.$t("meeting.addFailed")
              );
            });
        }
      });
    },
  },
};
</script>

<style scoped>
.form-container {
  padding: 8px;
  text-align: left;
}

.el-form-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 18px;
}

.el-form-item__label {
  padding: 0 0 8px 0;
  font-weight: bold;
  line-height: 1.5;
}

.form-container ::v-deep .el-form-item__content {
  width: 100%;
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-group .el-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.tag-group .el-tag:hover {
  opacity: 0.8;
  transform: translateY(-2px);
}

.upload-instructions {
  margin-bottom: 10px;
  color: #999;
  font-size: 12px;
}
</style>