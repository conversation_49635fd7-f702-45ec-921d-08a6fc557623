<template>
  <div>
    <base-dialog
      v-model="userDialogVisible"
      title="selectUser"
      @confirm="handleConfirm"
    >
      <template v-slot:content>
        <div class="user-selector">
          <div class="user-list-container">
            <div class="user-list" ref="userList">
              <div
                v-for="user in filteredUsers"
                :key="user.id"
                class="user-item"
                :class="{ selected: isSelected(user.id) }"
                @click="toggleSelect(user)"
              >
                <div class="user-avatar">
                  <img v-if="user.avatar" :src="user.avatar" alt="avatar" />
                  <div
                    v-else
                    class="avatar-placeholder"
                    :style="{ backgroundColor: getRandomColor(user.nickname) }"
                  >
                    {{ getFirstChar(user.nickname) }}
                  </div>
                </div>
                <div class="user-info">
                  <div class="nickname">{{ user.nickname }}</div>
                </div>
                <div class="selection-indicator" v-if="isSelected(user.id)">
                  <i class="el-icon-check"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="list-footer">
            <el-checkbox v-model="showOnlySelected">
              {{ $t("viewSelectedOnly") }} ({{ selectedUsers.length }})
            </el-checkbox>
          </div>
        </div>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import BaseDialog from "@/components/PcmDialog";
import { listSimpleUsers } from "@/api/system/user";

export default {
  components: { BaseDialog },
  props: {
    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      userDialogVisible: false,
      users: [],
      selectedUserIds: [],
      showOnlySelected: false,
    };
  },
  computed: {
    filteredUsers() {
      if (this.showOnlySelected) {
        return this.users.filter((user) =>
          this.selectedUserIds.includes(user.id)
        );
      }
      return this.users;
    },
    selectedUsers() {
      return this.users.filter((user) =>
        this.selectedUserIds.includes(user.id)
      );
    },
  },
  watch: {
    selectedIds: {
      immediate: true,
      handler(newVal) {
        this.selectedUserIds = [...newVal];
      },
    },
  },
  methods: {
    async open() {
      this.userDialogVisible = true;
      if (this.users.length === 0) {
        await this.fetchUsers();
      }
    },

    async fetchUsers() {
      listSimpleUsers().then((res) => {
        this.users = res.data;
      });
    },

    handleConfirm() {
      this.$emit("confirm", this.selectedUserIds);
      this.userDialogVisible = false;
    },

    toggleSelect(user) {
      const index = this.selectedUserIds.indexOf(user.id);
      if (index === -1) {
        this.selectedUserIds.push(user.id);
      } else {
        this.selectedUserIds.splice(index, 1);
      }
    },

    isSelected(userId) {
      return this.selectedUserIds.includes(userId);
    },

    getFirstChar(nickname) {
      return nickname ? nickname.charAt(0).toUpperCase() : "";
    },

    getRandomColor(nickname) {
      let hash = 0;
      for (let i = 0; i < nickname.length; i++) {
        hash = nickname.charCodeAt(i) + ((hash << 5) - hash);
      }
      const hue = hash % 360;
      return `hsl(${hue}, 70%, 65%)`;
    },

    clearSelected() {
      this.selectedUserIds = [];
    },
  },
};
</script>

<style scoped>
.user-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.user-list-container {
  flex: 1;
  overflow: hidden;
}

.user-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  padding: 8px;
  overflow-y: auto;
  max-height: 400px;
}

.user-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #ebeef5;
  position: relative;
}

.user-item:hover {
  background-color: #f5f7fa;
}

.user-item.selected {
  background-color: #ecf5ff;
  border-color: #c0e3ff;
}

.user-avatar {
  width: 40px;
  height: 40px;
  margin-bottom: 6px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.user-info {
  width: 100%;
  text-align: center;
}

.nickname {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  color: #409eff;
  font-size: 14px;
  background: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-footer {
  padding: 12px;
  border-top: 1px solid #ebeef5;
}
</style>