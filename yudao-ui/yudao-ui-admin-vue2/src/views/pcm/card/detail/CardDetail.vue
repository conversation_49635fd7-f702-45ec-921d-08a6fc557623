<template>
  <div class="container">
    <!-- 第一列：名片列表 -->
    <div class="column left-column hidden-xs-only">
      <div class="header">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow"
          @click="goBack()"
        />
        <span class="title">{{ $t("allCards") }}</span>
      </div>
      <el-input
        class="search-input"
        v-model="keyword"
        :placeholder="$t('searchHint')"
      >
        <template #append>
          <el-button
            slot="append"
            type="success"
            icon="el-icon-search"
            @click="searchCards"
          ></el-button>
        </template>
      </el-input>
      <div class="card-list" @scroll="handleScroll">
        <!-- 名片列表 -->
        <div
          v-for="(card, index) in cards"
          :key="index"
          class="card-item"
          @mouseenter="handleMouseEnter(index)"
          @mouseleave="handleMouseLeave(index)"
          @click="handleCardClick(index, card.id)"
          :class="{
            'is-hover': activeHoverIndex === index,
            'is-selected': activeSelectedIndex === index,
          }"
        >
          <div class="image-container">
            <img
              v-if="card.imageUrl === '*'"
              src="@/assets/pcm/card-detail/access_denied.jpg"
              alt="名片图片"
              class="card-image"
            />
            <SafeImage
              v-else
              :src="card.imageUrl"
              alt="card-image"
              class="card-image"
            />
            <div
              class="corner-badge"
              v-if="card.groupInfo && card.groupInfo.length > 1"
            >
              {{ card.groupInfo.length }}
            </div>
          </div>
          <div class="card-info">
            <div class="card-name">
              {{
                (card.firstName || card.lastNameEn || "") +
                (card.lastName || card.firstNameEn || "")
              }}
            </div>
            <div class="card-position">{{ card.jobTitle || "" }}</div>
            <div class="card-description">
              {{ card.companyName || card.companyNameEn || "" }}
            </div>
          </div>
        </div>

        <!-- 骨架屏 -->
        <div v-if="loading" class="skeleton-container">
          <div v-for="i in 5" :key="'skeleton-' + i" class="skeleton-item">
            <div class="skeleton-image"></div>
            <div class="skeleton-info">
              <div class="skeleton-line"></div>
              <div class="skeleton-line short"></div>
              <div class="skeleton-line shorter"></div>
            </div>
          </div>
        </div>
        <div v-if="noMore" class="no-more">沒有更多了</div>
      </div>
    </div>

    <!-- 第二列：名片信息 -->
    <div
      :style="{ overflowY: 'scroll' }"
      class="column middle-column"
      v-loading="currentCardLoading"
    >
      <div class="header">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow hidden-sm-and-up"
          @click="goBack()"
        />
        <span class="title">{{ $t("cardInformation") }}</span>
        <el-button
          size="mini"
          icon="el-icon-edit"
          class="add-button"
          @click="handleCardEdit"
          v-if="enableEdit"
          >{{ $t("edit") }}</el-button
        >
      </div>

      <el-divider></el-divider>
      <div class="card-details">
        <div class="top-section">
          <div class="left-content">
            <div class="company-name">{{ currentCard.companyName }}</div>

            <div class="tags-container" v-if="currentCard.groupInfo">
              <div
                v-for="(tag, index) in currentCard.groupInfo"
                :key="index"
                class="tag"
                @click="selectTag(tag)"
                @mouseover="hoverTag = tag"
                @mouseout="hoverTag = null"
                :class="{
                  active: currentCard.id === tag.id,
                  hover: hoverTag === tag,
                }"
              >
                {{ tag.jobTitle || tag.jobTitleEn || "N/A" }}
              </div>
            </div>
            <div v-else class="tag">
              {{ currentCard.jobTitle || currentCard.jobTitleEn || "N/A" }}
            </div>

            <div class="name">
              {{ (currentCard.firstName ?? "") + (currentCard.lastName ?? "") }}
            </div>
            <div class="name">
              {{
                (currentCard.lastNameEn ?? "") +
                " " +
                (currentCard.firstNameEn ?? "")
              }}
            </div>
            <div class="info">
              <!-- <div class="info-item">{{ currentCard.jobTitle }}</div> -->
              <div class="info-item">{{ currentCard.department }}</div>
            </div>
            <!-- mobile图片 -->
            <div
              class="mobile-photo hidden-sm-and-up"
              v-if="currentCard.imageUrl"
            >
              <card-flip-preview
                :card-data="currentCard"
                :image-style="{
                  width: '100%',
                  height: '180px',
                  'object-fit': 'contain',
                }"
                :backdrop-opacity="0.5"
                @flip="handleFlipCard"
              />
            </div>
            <!-- mobile图片 -->
            <div class="creation-info">
              <img
                :src="require('@/assets/pcm/card-detail/time.png')"
                alt="创建信息图标"
                class="creation-icon"
              />
              <span
                >{{ parseTime(currentCard.createTime) }} {{ $t("by") }}
                {{ currentCard.creator }}
                {{ $t("addToCompanyCardFolder") }}</span
              >
            </div>
          </div>
          <div class="bottom-image hidden-xs-only" v-if="currentCard.imageUrl">
            <card-flip-preview
              :card-data="currentCard"
              :image-style="{
                width: '100%',
                height: '120px',
                'object-fit': 'contain',
              }"
              :backdrop-opacity="0.5"
              @flip="handleFlipCard"
            />
          </div>
        </div>
        <div class="check-edit" v-if="currentCard.updateTime">
          <img
            src="@/assets/pcm/card-detail/icon-check.png"
            alt="check"
            class="check-icon"
          />
          <span
            >{{ $t("manualVerification") }}：{{ $t("manuallyEdited") }}</span
          >
        </div>
        <el-divider></el-divider>

        <!-- 可滚动区域 -->
        <div class="scrollable-content">
          <!-- 名片详细信息 -->
          <card-field-details :card="currentCard" />
        </div>

        <div class="tags-section">
          <div class="tags-header">
            <span class="title">{{ $t("tag") }}</span>
            <el-button
              type="text"
              size="mini"
              class="edit-tags"
              v-if="enableEdit"
              @click="handleTag"
              >{{ $t("editTags") }}</el-button
            >
          </div>
          <el-divider></el-divider>
          <div class="tags-list">
            <el-tag
              v-for="(tag, index) in currentCard.tags"
              :key="index"
              class="tag"
              >{{ tag.name }}</el-tag
            >
          </div>
        </div>
      </div>
      <div class="hidden-sm-and-up column right-column">
        <div class="card">
          <div class="card-header">
            <span class="title">{{ $t("negotiationRecords") }}</span>
            <el-button
              type="success"
              icon="el-icon-edit-outline"
              size="mini"
              @click="handleAddMeeting"
              v-if="enableEdit"
              class="add-meeting-button"
              >{{ $t("addNegotiationRecord") }}</el-button
            >
          </div>
          <el-divider></el-divider>
          <div class="card-content">
            <div
              v-if="meetingList && meetingList.length === 0"
              class="no-records"
            >
              暂无商谈记录
            </div>
            <div v-else>
              <div
                v-for="(negotiation, index) in meetingList"
                :key="index"
                class="negotiation-item"
              >
                <!-- 内容部分，限制2行显示 -->
                <div class="content-text">
                  <dict-tag
                    class="meeting-type"
                    :type="DICT_TYPE.MEETING_TYPE"
                    :value="negotiation.type"
                  />
                  {{ negotiation.content }}
                </div>
                <!-- 底部信息，创建者和时间 -->
                <div class="item-footer">
                  <span class="creator">{{ negotiation.creator }}</span>
                  <span class="create-time">{{ negotiation.createTime }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <span class="title">{{ $t("colleaguesWithViewPermission") }}</span>
            <el-button
              @click="handleUser"
              size="mini"
              icon="el-icon-plus"
              class="add-button"
              v-if="enableEdit"
              >{{ $t("add_") }}</el-button
            >
          </div>
          <el-divider></el-divider>
          <div class="card-content">
            <!-- <div class="hint">*{{ $t("viewPermissionNote") }}</div> -->
            <div
              v-for="(colleague, index) in currentCard.users"
              :key="index"
              class="colleague-item"
            >
              <div class="colleague-permission">
                <img
                  :src="
                    colleague.avatar === ''
                      ? require('@/assets/pcm/colleague/user1.png')
                      : colleague.avatar
                  "
                  alt="头像"
                  class="avatar"
                />
                <span class="name">{{ colleague.nickname }}</span>
              </div>
              <span
                v-if="colleague.nickname == currentCard.creator"
                class="creator"
                >创建者</span
              >
            </div>
             
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <span class="title">{{ $t("departmentsWithViewPermission") }}</span>
            <el-button
              size="mini"
              icon="el-icon-plus"
              class="add-button"
              v-if="enableEdit"
              @click="handleDept"
              >{{ $t("add_") }}</el-button
            >
          </div>
          <el-divider></el-divider>
          <div class="card-content">
            <div
              v-for="(department, index) in currentCard.depts"
              :key="index"
              class="department-item"
            >
              <i class="el-icon-office-building"></i>
              {{ department.name }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第三列：商谈记录、同事、部门 -->
    <div class="column right-column hidden-xs-only ml-10">
      <div class="card">
        <div class="card-header">
          <span class="title">{{ $t("negotiationRecords") }}</span>
          <el-button
            type="success"
            icon="el-icon-edit-outline"
            size="mini"
            @click="handleAddMeeting"
            class="add-meeting-button"
            v-if="enableEdit"
            >{{ $t("addNegotiationRecord") }}</el-button
          >
        </div>
        <el-divider></el-divider>
        <div class="card-content">
          <div
            v-if="meetingList && meetingList.length === 0"
            class="no-records"
          >
            暫無會面紀錄
          </div>
          <div v-else>
            <div
              v-for="(negotiation, index) in meetingList"
              :key="index"
              @click="handleMeetingPage(negotiation.cardId)"
              class="negotiation-item"
            >
              <!-- 内容部分，限制2行显示 -->
              <div class="content-text">
                <dict-tag
                  class="meeting-type"
                  :type="DICT_TYPE.MEETING_TYPE"
                  :value="negotiation.type"
                />
                {{ negotiation.content }}
              </div>
              <!-- 底部信息，创建者和时间 -->
              <div class="item-footer">
                <span class="creator">{{ negotiation.creator }}</span>
                <span class="create-time">{{ negotiation.createTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card">
        <div class="card-header">
          <span class="title">{{ $t("colleaguesWithViewPermission") }}</span>
          <el-button
            @click="handleUser"
            size="mini"
            icon="el-icon-plus"
            class="add-button"
            v-if="enableEdit"
            >{{ $t("add_") }}</el-button
          >
        </div>
        <el-divider></el-divider>
        <div class="card-content">
          <!-- <div class="hint">*{{ $t("viewPermissionNote") }}</div> -->
          <div
            v-for="(colleague, index) in currentCard.users"
            :key="index"
            class="colleague-item"
          >
            <div class="colleague-permission">
              <img
                :src="
                  colleague.avatar === ''
                    ? require('@/assets/pcm/colleague/user1.png')
                    : colleague.avatar
                "
                alt="头像"
                class="avatar"
              />
              <span class="name">{{ colleague.nickname }}</span>
            </div>
            <span
              v-if="colleague.nickname == currentCard.creator"
              class="creator"
              >創建者</span
            >
          </div>
        </div>
      </div>
      <div class="card">
        <div class="card-header">
          <span class="title">{{ $t("departmentsWithViewPermission") }}</span>
          <el-button
            size="mini"
            icon="el-icon-plus"
            class="add-button"
            v-if="enableEdit"
            @click="handleDept"
            >{{ $t("add_") }}</el-button
          >
        </div>
        <el-divider></el-divider>
        <div class="card-content">
          <div
            v-for="(department, index) in currentCard.depts"
            :key="index"
            class="department-item"
          >
            <i class="el-icon-office-building"></i>
            {{ department.name }}
          </div>
        </div>
      </div>
    </div>

    <!-- 添加商谈记录弹窗 -->
    <AddMeetingForm ref="meetingForm" @confirm="updateMeeting"></AddMeetingForm>

    <!-- 显示标签选择框 -->
    <TagSelect
      ref="tagSelect"
      @confirm="updateTags"
      :initialSelectedTags="splitTag"
    ></TagSelect>

    <!-- 显示用户选择框 -->
    <UserSelect
      ref="userSelect"
      :cardId="currentCard.id"
      :cardCreatorUserId="currentCard.createUserId"
      @confirm="addUserPermission"
    ></UserSelect>

    <!-- 显示用户选择框 -->
    <DeptSelect
      ref="deptSelect"
      :selectedIds="
        currentCard.depts ? currentCard.depts.map((dept) => dept.id) : []
      "
      @confirm="addDeptPermission"
    ></DeptSelect>
  </div>
</template>

<script>
import { debounce } from "lodash-es";
import TagSelect from "../table/TagSelect.vue";
import AddMeetingForm from "./AddMeetingForm.vue";
import UserSelect from "./SelectUser.vue";
import DeptSelect from "./SelectDept.vue";
import CardFieldDetails from "./CardFieldDetails.vue";
import * as CardApi from "@/api/pcm/card";
import { getMeetingByCardId } from "@/api/pcm/meeting";
import CardFlipPreview from "./CardFlipPreview.vue";
import { mapActions } from "vuex";
export default {
  components: {
    AddMeetingForm,
    TagSelect,
    UserSelect,
    DeptSelect,
    CardFieldDetails,
    CardFlipPreview,
  },
  computed: {
    splitTag() {
      if (this.currentCard.customTags) {
        return this.currentCard.customTags.split(";");
      }
      return [];
    },
    enableEdit() {
      return (
        this.$store.getters.roles?.includes("super_admin") ||
        this.$store.getters.userId === this.currentCard.createUserId ||
        this.currentCard.isEdit
      );
    },
  },
  data() {
    return {
      cardId: this.$route.params.cardId, //table跳转过来的Id
      cards: [], // 名片列表数据
      hoverTag: null,
      jumpCardGroupId: null, //跳转过来的名片分组id
      meetingList: [],
      currentCard: {}, // 当前选中的名片
      meetingCache: {},
      cardCache: {}, // 卡片详情缓存
      currentCardLoading: false, // 当前卡片加载状态
      pageNo: 1, // 当前页码
      pageSize: 10, // 每页条数
      keyword: "", //名片搜索关键字
      total: 0, // 总条数
      loading: false, // 是否正在加载
      noMore: false, // 是否没有更多数据
      scrollHandler: null,
      isRequesting: false, // 防止重复请求
      activeHoverIndex: null, // 当前悬停的索引
      activeSelectedIndex: 0, // 当前选中的索引（默认第一个）
    };
  },
  methods: {
    ...mapActions("pcm", ["setCardIds"]),
    async initClickCard() {
      //左侧卡片的第一个为表格选中的卡片
      CardApi.getCard(this.cardId).then((res) => {
        this.cards = [];
        this.cards.push(res.data);
        this.jumpCardGroupId = res.data.groupId;
        this.loadCards(); // 初始化加载第一页数据
      });
    },
    // 初始化加载第一页数据
    async loadCards() {
      if (this.loading || this.noMore) return;
      this.loading = true;
      try {
        const response = await CardApi.getCardDistinctPage({
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          keyword: this.keyword,
          currentCardId: this.cardId,
          groupId: this.jumpCardGroupId,
          orderBy: "createTime|desc",
        });
        if (response.data.list.length > 0) {
          // 使用 Vue.set 确保响应式更新
          this.cards = [...this.cards, ...response.data.list];
          this.total = response.data.total;

          if (this.pageNo === 1 && this.cards.length > 0) {
            // 检查是否需要立即加载更多
            this.$nextTick(() => {
              const list = this.$el.querySelector(".card-list");
              if (
                list &&
                list.scrollHeight <= list.clientHeight &&
                this.cards.length < this.total
              ) {
                this.pageNo += 1;
                this.loadCards();
              }
            });
          }

          this.pageNo += 1;
        } else {
          this.noMore = true;
        }
      } catch (error) {
        console.error("加载数据失败", error);
        this.$message.error("加载名片数据失败");
      } finally {
        // 默认选中第一张卡片
        this.selectCard(0, this.cards[0].id);
        this.loading = false;
      }
    },
    searchCards() {
      this.pageNo = 1;
      this.noMore = false;
      this.cards = [];
      this.initClickCard();
      this.initCardIds();
    },

    // 监听滚动事件
    handleScroll(event) {
      if (this.loading || this.noMore || this.isRequesting) return;
      const list = event.target;
      if (list.scrollHeight - list.scrollTop <= list.clientHeight + 100) {
        this.isRequesting = true;
        this.loadCards().finally(() => {
          this.isRequesting = false;
        });
      }
    },
    selectTag(tag) {
      //更新卡片详情内容
      this.getCardById(tag.id);
      this.getMeetingListByCardId(tag.id);
    },
    handleMouseEnter(index) {
      this.activeHoverIndex = index;
      this.debouncedPreloadCard(index);
    },
    handleMouseLeave(index) {
      // 只有当离开的是当前悬停项时才清除
      if (this.activeHoverIndex === index) {
        this.activeHoverIndex = null;
      }
    },
    handleTag() {
      this.$refs["tagSelect"].open();
    },
    handleUser() {
      this.$refs["userSelect"].open();
    },
    handleDept() {
      this.$refs["deptSelect"].open();
    },
    selectCard(index, cardId) {
      this.activeSelectedIndex = index;
      this.getMeetingListByCardId(cardId);
      // 更新选中状态
      this.cards = this.cards.map((card, i) => ({
        ...card,
        selected: i === index,
      }));

      this.getCardById(cardId);
    },

    // 点击选中卡片
    handleCardClick(index, cardId) {
      this.activeSelectedIndex = index;
      this.selectCard(index, cardId);
    },
    /**
     * 根据卡片id获取会议列表
     */
    getMeetingListByCardId(cardId) {
      if (this.meetingCache[cardId]) {
        this.meetingList = this.meetingCache[cardId];
        return;
      }
      getMeetingByCardId(cardId).then((res) => {
        this.meetingList = res.data.list;
        this.$set(this.meetingCache, cardId, res.data.list);
      });
    },
    async getCardById(cardId) {
      if (this.cardCache[cardId]) {
        this.currentCard = this.cardCache[cardId];
        this.currentCardLoading = false;
        return;
      }

      try {
        this.currentCardLoading = true;
        // 1. 获取卡片详情
        const cardRes = await CardApi.getCard(cardId);

        // 2. 获取分组信息
        const groupRes = await CardApi.getCardsByGroudId(cardRes.data.groupId);

        // 3. 合并数据并缓存
        this.currentCard = {
          ...cardRes.data,
          groupInfo: groupRes.data,
        };
        this.$set(this.cardCache, cardId, this.currentCard);
        // 4. 更新卡片列表中的groupInfo（如果存在）
        const cardIndex = this.cards.findIndex((c) => c.id === cardId);
        if (cardIndex !== -1) {
          this.$set(this.cards[cardIndex], "groupInfo", groupRes.data);
        }
      } catch (error) {
        console.error("加载卡片详情失败", error);
        this.$message.error("加载卡片详情失败");
      } finally {
        this.currentCardLoading = false;
      }
    },

    goBack() {
      this.$router.go(-1);
    },
    // 防抖预加载逻辑
    debouncedPreloadCard: debounce(function (index) {
      const cardId = this.cards[index].id;

      // 如果缓存中已有数据，不再重复加载
      if (this.cardCache[cardId]) return;

      // 并行获取卡片详情和分组信息
      Promise.all([
        CardApi.getCard(cardId),
        CardApi.getCardsByGroudId(this.cards[index].groupId),
      ]).then(([cardRes, groupRes]) => {
        // 合并数据并缓存
        const cardData = {
          ...cardRes.data,
          groupInfo: groupRes.data,
        };

        this.$set(this.cardCache, cardId, cardData);
        this.$set(this.cards[index], "groupInfo", groupRes.data || []);
      });
    }, 150),
    // 图片点击
    handleFlipCard(isFront) {
      console.log(`当前显示: ${isFront ? "正面" : "反面"}`);
      // 这里可以调用API保存正反面状态
      // CardApi.updateCardFlipStatus(this.currentCard.id, isFront).then(res => {
      //   this.$message.success('状态已更新')
      // })
    },
    handleCardEdit() {
      this.$router.push({
        name: "CardEdit",
        params: { cardId: this.currentCard.id },
      });
    },
    handleMeetingPage(cardId) {
      this.$router.push({
        name: "Meeting",
        query: { cardId: cardId },
      });
    },
    handleAddMeeting() {
      this.$refs["meetingForm"].open(this.currentCard.id);
    },
    updateTags(tags) {
      CardApi.updateCard({
        id: this.currentCard.id,
        customTags: tags.join(";"),
      }).then((res) => {
        this.$delete(this.cardCache, this.currentCard.id);
        this.getCardById(this.currentCard.id);
        this.$modal.msgSuccess("成功修改標籤!");
      });
    },
    updateMeeting() {
      // 清空当前卡片的会议缓存
      if (this.currentCard?.id) {
        this.$delete(this.meetingCache, this.currentCard.id);
        console.log(`已清空卡片 ${this.currentCard.id} 的会议緩存`);
      }

      // 重新加载会议列表
      this.getMeetingListByCardId(this.currentCard.id);
    },
    addUserPermission(userPermissions) {
      CardApi.savePermission(this.currentCard.id, userPermissions).then(
        (res) => {
          this.$delete(this.cardCache, this.currentCard.id);
          this.getCardById(this.currentCard.id);
          this.$modal.msgSuccess("成功修改同事權限!");
        }
      );
    },
    addDeptPermission(deptIds) {
      CardApi.updateCard({
        id: this.currentCard.id,
        deptId: deptIds.join(";"),
      }).then((res) => {
        this.$delete(this.cardCache, this.currentCard.id);
        this.getCardById(this.currentCard.id);
        this.$modal.msgSuccess("成功修改部门權限!");
      });
    },
    initCardIds() {
      CardApi.getDistinctIds({
        keyword: this.keyword,
        currentCardId: this.cardId,
        orderBy: "createTime|desc",
      }).then((res) => {
        let arr = res.data;
        arr.splice(0, 0, this.cardId);
        this.setCardIds(arr);
      });
    },
  },
  mounted() {
    this.scrollHandler = debounce(this.handleScroll, 200);
    this.initClickCard();
    this.initCardIds();
  },
  // 在 beforeDestroy 中移除监听
  beforeDestroy() {
    this.cardCache = {};
    this.meetingCache = {};
    const list = this.$el.querySelector(".card-list");
    if (list) {
      list.removeEventListener("scroll", this.scrollHandler);
    }
  },
};
</script>

<style lang="scss" scoped>
.mobile-photo {
  width: 100%;
  height: 180px;
  .img-instance {
    width: 100%;
    height: 100%;
  }
}
.toggle-tag-box {
  flex-shrink: 0;
  overflow-x: scroll;
  display: flex;
  .item-tag {
    padding-right: 10px;
  }
}
.ml-10 {
  margin-left: 10px;
}
.back-arrow {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}
.container {
  display: flex;
  height: calc(100vh - 84px); // 减去可能的顶部导航栏高度
  padding: 10px;
  box-sizing: border-box;

  .column {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .left-column {
    margin-right: 10px;
    flex: 1.5;
    background-color: white;
    border-radius: 8px;
    padding: 16px;

    .header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .back-arrow {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        cursor: pointer;
      }

      .title {
        font-size: 18px;
        font-weight: bold;
      }
    }

    .search-input {
      margin-bottom: 16px;
    }

    .card-list {
      height: calc(100vh - 200px); /* 设置固定高度 */
      overflow-y: auto; /* 启用滚动条 */

      .card-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 4px;
        margin-bottom: 10px;
        cursor: pointer;

        &.hovered {
          background-color: rgba(237, 108, 0, 0.08);
          transform: translateY(-1px);
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        }

        &.selected {
          background-color: #ed6c001a; // 选中时的背景色
        }
        &.is-selected {
          background-color: rgba(237, 108, 0, 0.12);
          border-left: 3px solid #ed6c00;
          box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

          // 选中状态下的悬停效果
          &.is-hover {
            background-color: rgba(237, 108, 0, 0.15);
          }
        }
        .image-container {
          position: relative;
          display: inline-block;
          .card-image {
            max-width: 120px;
            max-height: 80px;
            margin-right: 16px;
          }
          .corner-badge {
            position: absolute;
            top: 10px;
            right: 25px;
            width: 10px;
            height: 10px;
            background-color: #4caf50; /* 绿色背景 */
            color: white;
            font-size: 8px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            border-top-right-radius: 0; /* 右上角不圆角 */
            border-bottom-left-radius: 8px; /* 左下角8px圆角 */
            transform: translate(50%, -50%); /* 让角标部分超出图片边缘 */
            padding: 4px; /* 增加一些内边距让数字居中 */
            min-width: 10px; /* 最小宽度 */
            min-height: 10px; /* 最小高度 */
            box-sizing: content-box; /* 确保padding不影响宽高 */
          }
        }

        .card-info {
          .card-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
          }

          .card-position {
            font-size: 14px;
            color: #000000;
            margin-bottom: 5px;
          }

          .card-description {
            font-size: 14px;
            color: #000000;
          }
        }
      }

      .loading,
      .no-more {
        text-align: center;
        padding: 10px;
        color: #999;
      }
    }
  }

  .middle-column {
    flex: 4;
    background-color: white;
    border-radius: 8px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    height: 100%;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0; // 防止header被压缩

      .title {
        font-size: 18px;
        font-weight: bold;
      }

      .add-button {
        font-size: 14px;
      }
    }

    .card-details {
      .top-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        gap: 16px;
        flex-shrink: 0; // 防止顶部内容被压缩

        .left-content {
          flex: 1;
        }

        .bottom-image {
          img {
            width: 200px;
            height: auto;
            max-height: 120px;
          }
        }
      }
      // 可滚动内容区域
      .scrollable-content {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }
        &::-webkit-scrollbar-thumb {
          background-color: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }
      }

      .company-name {
        font-size: 20px;
        font-weight: bold;
      }

      .tags-container {
        display: flex;
        gap: 10px;
        padding: 10px 0;
        flex-wrap: wrap;
        .tag {
          padding: 6px 12px;
          border: 1px solid #0000001a;
          border-radius: 4px;
          background-color: white;
          color: #1f1f1f;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 14px;
        }

        .tag:hover,
        .tag.hover {
          border-color: #0000001a;
          background-color: #ed6c001a;

          color: #ed6c00;
        }

        .tag.active {
          border-color: #0000001a;
          background-color: #ed6c001a;
          color: #ed6c00;
          font-weight: bold;
        }
      }

      .name {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 12px;

        .label {
          color: #666;
        }
      }

      .info {
        margin-bottom: 16px;

        .info-item {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
      }

      .creation-info {
        padding-top: 10px;
        color: #999999;
        display: flex;
        align-items: center;
        border-radius: 8px;
        margin-bottom: 12px;

        .creation-icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }

      .check-edit {
        display: flex;
        margin-top: 10px;
        align-items: center;
        background-color: #1a66fd0d;
        padding: 10px 15px;
        margin-bottom: 16px;

        .check-icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }
      }

      .details {
        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .detail-icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }
      }
      // 调整card-field-details组件样式
      .card-field-details {
        margin-top: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        padding: 12px;
      }

      .tags-section {
        flex-shrink: 0; // 防止底部内容被压缩
        margin-top: auto; // 将底部区域推到最下方
        padding-top: 16px;
        .tags-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            font-size: 18px;
            font-weight: bold;
          }

          .edit-tags {
            color: #1ea235;
            font-size: 14px;
          }
        }

        .tags-list {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .right-column {
    flex: 1.5;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .card {
      background-color: white;
      border-radius: 8px;
      padding: 16px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 18px;
          font-weight: bold;
        }

        .add-button {
          color: #1f1f1f;
          font-size: 14px;
        }

        .add-meeting-button {
          color: #ffffff;
          font-size: 14px;
        }
      }

      .card-content {
        max-height: 200px;
        overflow-y: auto;

        .no-records {
          color: #999;
          text-align: center;
        }

        .negotiation-item {
          margin-bottom: 12px;
          padding-bottom: 8px;
          cursor: pointer;
          border-bottom: 1px solid #f0f0f0;
          .content-text {
            display: -webkit-box;
            -webkit-line-clamp: 2; /* 限制2行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 14px;
            text-overflow: ellipsis;
            line-height: 1.5;
            margin-bottom: 6px;
          }
          >>> .meeting-type {
            border-radius: 8px;
            height: 20px;
            line-height: 20px !important;
            font-size: 12px !important;
          }

          .item-footer {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            padding: 0 5px;
            color: #999;
            .creator {
              flex: 1;
            }

            .create-time {
              text-align: right;
            }
          }
        }

        .hint {
          font-size: 12px;
          color: #999;
          margin-bottom: 8px;
        }

        .colleague-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;

          .colleague-permission {
            display: flex;
            align-items: center;
            .avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              margin-right: 8px;
            }

            .name {
              font-size: 14px;
            }
          }

          .creator {
            font-size: 14px;
            color: #999999;
          }
        }

        .department-item {
          margin-bottom: 8px;
          font-size: 14px;
        }
      }
    }
  }
}
/* 添加骨架屏样式 */
.skeleton-container {
  padding: 8px;
}

.skeleton-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
}

.skeleton-image {
  width: 120px;
  height: 80px;
  margin-right: 16px;
  background: #f0f0f0;
  border-radius: 4px;
}

.skeleton-info {
  flex: 1;
}

.skeleton-line {
  height: 16px;
  background: #f0f0f0;
  margin-bottom: 8px;
  border-radius: 4px;

  &.short {
    width: 70%;
  }

  &.shorter {
    width: 50%;
  }
}

.card-image {
  max-width: 200px;
  max-height: 120px;
  border-radius: 4px;
  object-fit: cover; /* 保持图片比例不变，同时填充满容器 */
  cursor: pointer; /* 鼠标悬停时显示手型 */
  transition: transform 0.3s; /* 添加过渡效果 */
}
.card-image:hover {
  transform: scale(1.02); /* 鼠标悬停时轻微放大 */
}
</style>