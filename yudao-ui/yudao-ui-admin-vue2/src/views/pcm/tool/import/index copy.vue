<template>
  <div class="import-container">
    <div class="import-steps">
      <!-- Step 1: Download template -->
      <div class="step">
        <div class="step-header">
          <span class="step-number">{{ $t("step1") }}</span>
          <span class="step-title">{{ $t("downloadTemplate") }}</span>
        </div>
        <div class="step-content">
          <div class="download-box" @click="downloadTemplate">
            <div class="excel-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="download-text">{{ $t("clickDownloadTemplate") }}</div>
          </div>
        </div>
      </div>

      <!-- Step 2: Confirm format -->
      <div class="step">
        <div class="step-header">
          <span class="step-number">{{ $t("step2") }}</span>
          <div class="title-wrapper">
            <span class="step-title">{{ $t("confirmTemplateFormat") }}</span>
            <a href="#" class="learn-more" target="_blank">{{
              $t("learnMore")
            }}</a>
          </div>
        </div>
      </div>

      <!-- Step 3: Upload file -->
      <div class="step">
        <div class="step-header">
          <span class="step-number">{{ $t("step3") }}</span>
          <span class="step-title">{{ $t("importExcelNotice") }}</span>
        </div>
        <div class="step-content">
          <p class="upload-tip">{{ $t("fileSizeLimit") }}</p>
          <el-upload
            class="upload-btn"
            :action="upload.url + '?updateSupport=' + upload.updateSupport"
            :before-upload="beforeUpload"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :headers="upload.headers"
            :auto-upload="true"
            :show-file-list="false"
            accept=".xls,.xlsx"
          >
            <el-button type="primary" size="medium">
              <i class="el-icon-upload"></i>
              {{ $t("selectExcelFile") }}
            </el-button>
          </el-upload>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { importTemplate } from "@/api/pcm/card";
import { getBaseHeader } from "@/utils/request";
export default {
  data() {
    return {
      upload: {
        // 是否禁用上传
        isUploading: false,
           // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/admin-api/pcm/card/import",
      },
    };
  },
  methods: {
    downloadTemplate() {
      // Template download logic
      window.location.href = "#";
      this.$message.success(this.$t("templateDownloadStarted"));
      importTemplate().then((response) => {
        this.$download.excel(response, "名片上傳Excel模版.xls");
      });
    },
    beforeUpload(file) {
      const isExcel =
        file.type === "application/vnd.ms-excel" ||
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isExcel) {
        this.$message.error(this.$t("uploadExcelOnly"));
        return false;
      }
      if (!isLt10M) {
        this.$message.error(this.$t("fileSizeLimitExceeded"));
        return false;
      }
      return true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$modal.msgError(response.msg);
        return;
      }
      this.upload.isUploading = false;
      // 拼接提示语
      let data = response.data;
      let text = "創建成功名片數量：" + data.createCardNames.length;
      for (const cardFullName of data.createCardNames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + cardFullName;
      }
      text += "<br />更新成功名片數量：" + data.updateCardNames.length;
      for (const cardFullName of data.updateCardNames) {
        text += "<br />&nbsp;&nbsp;&nbsp;&nbsp;" + cardFullName;
      }
      text +=
        "<br />更新失敗名片數量：" + Object.keys(data.failureCardNames).length;
      for (const cardFullName in data.failureCardNames) {
        text +=
          "<br />&nbsp;&nbsp;&nbsp;&nbsp;" +
          cardFullName +
          "：" +
          data.failureCardNames[cardFullName];
      }
      this.$alert(text, "導入結果", { dangerouslyUseHTMLString: true });
    },
  },
};
</script>

<style scoped>
.import-container {
  max-width: 800px;
  padding: 24px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.step {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px dashed #e4e7ed;
}

.step:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.step-header {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.step-number {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;
  margin-right: 12px;
  white-space: nowrap;
}

.title-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.step-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-right: 12px;
}

.download-box {
  width: 200px;
  height: 200px;
  background-color: #67c23a;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: white;
}

.download-box:hover {
  background-color: #5daf34;
  transform: translateY(-2px);
}

.excel-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.download-text {
  font-size: 16px;
  text-align: center;
}

.learn-more {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  white-space: nowrap;
}

.learn-more:hover {
  text-decoration: underline;
}

.upload-tip {
  color: #909399;
  font-size: 14px;
  margin-bottom: 16px;
}

.upload-btn {
  margin-top: 8px;
}
</style>