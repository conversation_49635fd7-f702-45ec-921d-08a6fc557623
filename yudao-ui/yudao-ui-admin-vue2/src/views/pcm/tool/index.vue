<template>
  <div class="tools-container">
    <!-- 左侧二级菜单 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 1)"
      class="left-menu"
    >
      <div class="menu-title">{{ $t("toolbox") }}</div>
      <!-- 动态渲染有权限的菜单 -->
      <div
        v-for="menu in accessibleMenus"
        :key="menu.key"
        class="menu-item"
        :class="{ active: activeMenu === menu.key }"
        @click="changeMenu(menu.key)"
      >
        <img :src="getIcon(menu.key)" class="menu-icon" />
        {{ $t(menu.label) }}
      </div>
    </div>

    <!-- 右侧内容 -->
    <div
      v-if="!$isMobile() || ($isMobile() && currentSetup === 2)"
      class="right-content"
    >
      <div class="content-title">
        <img
          src="@/assets/pcm/card-detail/back.png"
          alt="返回箭头"
          class="back-arrow hidden-sm-and-up"
          @click="goBack()"
        />
        <span>{{ activeMenuLabel }}</span>
      </div>
      <div class="content-divider"></div>
      <div class="content-body">
        <!-- 内容区域暂时留空 -->
        <p v-if="activeMenu === 'exportExcel'">
          <CardExport />
        </p>
        <p v-if="activeMenu === 'importExcel'">
          <CardImport />
        </p>
          <p v-if="activeMenu === 'exportApproval'">
          <ExportApproval />
        </p>
        <p v-if="activeMenu === 'changePassword'">
          <profile />
        </p>
        <p v-if="activeMenu === 'companyTag'">
          <tag />
        </p>
        <p v-if="activeMenu === 'recycleBin'">
          <recycle />
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import profile from "@/views/pcm/tool/profile";
import tag from "@/views/pcm/tool/tag";
import recycle from "@/views/pcm/tool/recycle";
import CardExport from "@/views/pcm/tool/export";
import CardImport from "@/views/pcm/tool/import";
import ExportApproval from "@/views/pcm/tool/approval"
export default {
  components: {
    profile,
    tag,
    recycle,
    CardExport,
    CardImport,
    ExportApproval
  },
  watch: {
    $route(to, from) {
      this.setActiveMenu(to.query.menuName);
    },
    accessibleMenus: {
      immediate: true,
      handler(newVal) {
        if (
          newVal.length > 0 &&
          !newVal.some((m) => m.key === this.activeMenu)
        ) {
          this.setActiveMenu();
        }
      },
    },
  },
  data() {
    return {
      activeMenu: "exportExcel", // 默认激活的菜单
      currentSetup: 1,
      menuItems: [
        {
          key: "exportExcel",
          permission: ["pcm:card:export"],
          label: "exportToExcel",
        },
        {
          key: "importExcel",
          permission: ["pcm:card:import"],
          label: "batchImportCards",
        },
         {
          key: "exportApproval",
          permission: ["pcm:card:export"],
          label: "exportApproval",
        },
        {
          key: "changePassword",
          permission: ["pcm:system:userInfo"],
          label: "changePassword",
        },
        {
          key: "companyTag",
          permission: ["pcm:tag:manager"],
          label: "companyTagManagement",
        },
        {
          key: "recycleBin",
          permission: ["pcm:card:recycle"],
          label: "cardRecycleBin",
        },
      ],
    };
  },
  computed: {
    accessibleMenus() {
      // 如果是admin账号，直接返回所有菜单
      if (this.$store.getters.roles?.includes("super_admin")) {
        return this.menuItems;
      }

      // 普通账号按权限过滤
      return this.menuItems.filter((item) => {
        // 如果菜单不需要权限，则直接显示
        if (!item.permission || item.permission.length === 0) return true;

        // 检查是否有任一权限
        return item.permission.some((perm) => this.$auth.hasPermi(perm));
      });
    },
    activeMenuLabel() {
      const menu = this.menuItems.find((item) => item.key === this.activeMenu);
      return menu ? this.$t(menu.label) : "";
    },
  },
  mounted() {
    this.setActiveMenu(this.$route.query.menuName);
  },
  methods: {
    goBack() {
      this.currentSetup = 1;
    },
    handleMenuChangeByMobileFn() {
      this.currentSetup = 2;
    },

     setActiveMenu(menuName) {
      // 检查传入的menuName是否有权限
      const isValidMenu = menuName && this.accessibleMenus.some(m => m.key === menuName);
      
      if (isValidMenu) {
        this.activeMenu = menuName;
      } else {
        // 使用默认的第一个有权限的菜单
        this.activeMenu = this.getDefaultActiveMenu();
        
        // 如果当前路由的menuName无效，更新路由参数
        if (menuName && menuName !== this.activeMenu) {
          this.$router.replace({
            query: {
              ...this.$route.query,
              menuName: this.activeMenu
            }
          });
        }
      }
    },
    getDefaultActiveMenu() {
      // 从有权限的菜单中获取第一个
      return this.accessibleMenus[0]?.key || "exportExcel";
    },
    changeMenu(menu) {
      if (this.accessibleMenus.some((m) => m.key === menu)) {
        this.activeMenu = menu;
        this.$router.push({
          query: {
            ...this.$route.query,
            menuName: menu,
          },
        });
        if (this.$isMobile()) {
          this.handleMenuChangeByMobileFn();
        }
      } else {
        this.$message.error(this.$t("noPermission"));
      }
    },
    getIcon(menu) {
      // 根据菜单项返回对应的图标路径
      switch (menu) {
        case "exportExcel":
          return require("@/assets/pcm/tool/icon-export.png");
        case "exportCRM":
          return require("@/assets/pcm/tool/icon-crm.png");
        case "exportGoogle":
          return require("@/assets/pcm/tool/icon-google.png");
        case "exportOutlook":
          return require("@/assets/pcm/tool/icon-outlook.png");
        case "saveToPrivate":
          return require("@/assets/pcm/tool/icon-file.png");
        case "importExcel":
          return require("@/assets/pcm/tool/icon-export.png");
        case "exportApproval":
          return require("@/assets/pcm/tool/icon-export-approval.png");
        case "changePassword":
          return require("@/assets/pcm/tool/icon-password.png");
        case "companyTag":
          return require("@/assets/pcm/tool/icon-tag.png");
        case "recycleBin":
          return require("@/assets/pcm/tool/icon-delete.png");
        default:
          return "";
      }
    },
  },
};
</script>

<style scoped>
.back-arrow {
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  position: absolute;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  cursor: pointer;
}
.tools-container {
  display: flex;
  height: 100%;
  background-color: #f0f4fa;
}

.left-menu {
  flex: 2;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.menu-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #1f1f1f;
  font-weight: 400;
}

.menu-item:hover {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-item.active {
  background-color: #ed6c001a;
  color: #ed6c00;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-item:hover .menu-icon,
.menu-item.active .menu-icon {
  filter: brightness(0) saturate(100%) invert(50%) sepia(100%) saturate(1000%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.menu-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 20px 0;
}

.right-content {
  overflow-x: scroll;
  flex: 9;
  margin-left: 10px;
  background-color: white;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

@media screen and (max-width: 768px) {
  .right-content {
    margin-left: 0px;
  }
  .content-title {
    position: relative;
    text-align: center;
  }
}

.content-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
}

.content-divider {
  height: 1px;
  background-color: #e0e0e0;
  margin-bottom: 20px;
}

.content-body {
  color: #666;
}
</style>