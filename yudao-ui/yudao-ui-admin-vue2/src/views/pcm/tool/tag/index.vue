<template>
  <div class="tag-management">
    <!-- 搜索框和操作按钮 -->
    <div class="search-and-actions">
      <el-input
        v-model="queryParams.name"
        placeholder="輸入標籤名稱"
        clearable
        size="small"
        @keyup.enter="handleSearch"
      >
        <el-button slot="append" icon="el-icon-search" @click="handleSearch" />
      </el-input>
      <el-button
        size="small"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >
        添加
      </el-button>
      <el-button
        size="small"
        type="danger"
        icon="el-icon-delete"
        :disabled="selectedTags.length === 0"
        @click="handleDelete"
      >
        删除
      </el-button>
    </div>

    <!-- 标签列表 -->
    <div class="tag-list">
      <div v-for="tag in tags" :key="tag.id" class="tag-item">
        <el-checkbox v-model="selectedTags" :label="tag.id" />
        <div class="tag-info">
          <span class="tag-name">{{ tag.name }}</span>
          <span class="tag-description">{{ tag.description }}</span>
        </div>
        <el-button type="text" icon="el-icon-edit" @click="handleEdit(tag)" />
      </div>
    </div>

    <!-- 分页组件 -->
    <pagination
      class="params"
      v-show="queryParams.total > 0"
      :total="queryParams.total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加/编辑标签对话框 -->
    <base-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      confirmText="保存"
      @confirm="submitForm"
    >
      <template v-slot:content>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="標籤名稱" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="標籤描述">
            <el-input v-model="form.description" type="textarea" />
          </el-form-item>
        </el-form>
      </template>
    </base-dialog>
  </div>
</template>

<script>
import * as TagApi from "@/api/pcm/tag";
import BaseDialog from "@/components/PcmDialog";
export default {
  components: { BaseDialog },
  data() {
    return {
      tags: [], // 标签列表
      selectedTags: [], // 选中的标签
      queryParams: {
        pageNo: 1, // 当前页码
        name: "",
        pageSize: 10, // 每页条数
        total: 0, // 总条数
      },
      dialogVisible: false, // 对话框是否显示
      dialogTitle: "添加標籤", // 对话框标题
      form: {
        id: null, // 标签 ID
        name: "", // 标签名称
        description: "", // 标签描述
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "標籤名稱不能為空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取标签列表
    async getList() {
      // 执行查询
      TagApi.page({
        ...this.queryParams,
        sortField: this.queryParams.sortField, // 传递排序字段
      }).then((response) => {
        this.tags = response.data.list;
        this.queryParams.total = response.data.total;
      });
    },
    // 搜索标签
    handleSearch() {
      this.queryParams.pageNo = 1;
      this.getList();
    },

    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        name: undefined,
        description: undefined,
      };
      this.resetForm("form");
    },

    // 添加标签
    handleAdd() {
      this.dialogTitle = "添加標籤";
      this.reset();
      this.dialogVisible = true;
    },
    // 编辑标签
    handleEdit(tag) {
      this.dialogTitle = "编辑標籤";
      this.reset();
      this.form = { ...tag };
      this.dialogVisible = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            TagApi.updatTag(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.dialogVisible = false;
              this.getList();
            });
          } else {
            TagApi.creatTag(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.dialogVisible = false;
              this.getList();
            });
          }
        }
      });
    },
    // 删除标签
    async handleDelete() {
      const ids = this.selectedTags;
      this.$modal
        .confirm("是否確認删除選中的標籤數據項目?")
        .then(function () {
          return TagApi.deletTag(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.tag-management {
  padding: 20px;
}

.search-and-actions {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.search-and-actions .el-input {
  width: 300px;
  margin-right: 10px;
}

.tag-list {
  border-bottom: 1px solid #ebeef5;
  border-radius: 4px;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.tag-item:last-child {
  border-bottom: none;
}

.tag-info {
  flex: 1;
  margin-left: 10px;
}

.tag-name {
  font-weight: bold;
}

.tag-description {
  color: #909399;
  margin-left: 10px;
}

.params {
  margin-top: 20px;
  text-align: right;
}
</style>