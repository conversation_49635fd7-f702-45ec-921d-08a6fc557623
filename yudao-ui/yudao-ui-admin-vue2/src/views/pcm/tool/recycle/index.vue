<template>
  <div class="container">
    <!-- 搜索行 -->
    <div class="search-row">
      <el-input
        v-model="queryParams.searchKeyword"
       :placeholder="$t('searchHint')"
        class="search-input"
      >
        <template #append>
          <el-button
            @click="handleSearch"
            slot="append"
            type="success"
            icon="el-icon-search"
          ></el-button>
        </template>
      </el-input>
      <el-button :disabled="selectedIds.length === 0" @click="handleDelete(selectedIds)">
        {{$t('permanentlyDelete')}}
      </el-button>
      <el-button
        type="success"
        :disabled="selectedIds.length === 0"
        @click="handleRestore(selectedIds)"
      >
         {{$t('restore')}}
      </el-button>
    </div>

    <!-- 表格 -->
    <div class="table-wrapper">
      <el-table
        :data="tableData"
        size="small"
        style="width: 100%"
        :header-cell-style="{ background: '#1EA2350D' }"
        :border="true"
        :fit="true"
        @selection-change="handleSelectionChange"
      >
        <!-- 勾选框列 -->
        <el-table-column type="selection" width="55"></el-table-column>
        <!-- 名片图像列 -->
        <el-table-column :label="$t('cardImage')" width="220">
          <template slot-scope="scope">
             <SafeImage
               :src="scope.row.imageUrl"
                alt="card-image"
                class="avatar"
              />
          </template>
        </el-table-column>
        <!-- 姓名/公司列 -->
        <el-table-column prop="fullName" :label="$t('name')"></el-table-column>
        <!-- 公司列 -->
        <el-table-column prop="companyName" :label="$t('Company')"></el-table-column>
        <!-- 职位列 -->
        <el-table-column prop="jobTitle" :label="$t('position')"></el-table-column>
        <!-- 删除者列 -->
        <el-table-column :label="$t('deletedBy')">
          <template slot-scope="scope">
            <div>
              <div>{{ scope.row.updater }}</div>
              <div>{{ parseTime(scope.row.updateTime) }}</div>
            </div>
          </template>
        </el-table-column>
        <!-- 操作列 -->
        <el-table-column :label="$t('action')" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              @click="handleDelete([scope.row.id])"
            >
              <span style="color: #ff3535">   {{$t('permanentlyDelete')}} </span>
            </el-button>
            <el-button
              size="mini"
              type="text"
              @click="handleRestore([scope.row.id])"
            >
              <span style="color: #1ea235">   {{$t('restore')}} </span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getCardTrashPage, restore, permanentlyDelete } from "@/api/pcm/card";
export default {
  data() {
    return {
      tableData: [],
      loading: false,
      selectedIds: [], // 选中的行ID
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        searchKeyword: "", // 搜索关键字
      },
      total: 20, // 总条数
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 处理查询
    handleSearch() {
      console.log("搜索关键字:", this.searchKeyword);
      this.getList();
    },

    getList() {
      this.loading = true;
      // 执行查询
      getCardTrashPage(this.queryParams).then((response) => {
        this.tableData = response.data.list;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 处理勾选
    handleSelectionChange(selection) {
      this.selectedIds = selection.map((item) => item.id);
    },
    // 处理彻底删除
    handleDelete(ids) {
      const deleteIds = ids || this.selectedIds;

      this.$modal
        .confirm("是否确认删除选中的名片数据项?")
        .then(function () {
          return permanentlyDelete(deleteIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("彻底删除成功");
        })
        .catch(() => {});
    },
    // 处理恢复
    handleRestore(ids) {
      const restoreIds = ids || this.selectedIds;
      console.log(JSON.stringify(restoreIds));
      this.$modal
        .confirm("是否确认恢复选中的名片数据项?")
        .then(() => {
          return restore(restoreIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("恢复成功");
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.container {
  padding: 20px;
}
.search-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.search-input {
  max-width: 400px;
  margin-right: 10px;
}
.table-wrapper {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}
.avatar {
  width: 90px;
  height: 56px;
}
.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>