<template>
  <div class="approval-container">
    <!-- 頁頭部分改進 -->
    <div class="approval-header">
      <img class="logo" :src="logoUrl" alt="系統標誌" v-if="logoUrl" />
      <div class="header-content">
        <h1 class="title">名片導出申請審批</h1>
        <div class="header-actions" v-if="applicationData">
          <span class="status-badge" :class="statusClass">{{ statusText }}</span>
        </div>
      </div>
    </div>

    <!-- 主要內容區域改進 -->
    <div class="approval-content">
      <!-- 加載狀態改進 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>正在加載申請信息...</p>
      </div>

      <!-- 未找到申請記錄改進 -->
      <div v-if="!loading && !applicationData" class="no-data">
        <img src="@/assets/pcm/meeting/empty-state.png" alt="無數據" class="no-data-img">
        <p class="no-data-text">沒有查到該申請記錄</p>
        <p class="no-data-hint">請檢查鏈接是否正確或聯繫管理員</p>
      </div>

      <!-- 申請信息展示改進 -->
      <div v-if="applicationData" class="application-info">
        <div class="info-card">
          <h3 class="info-card-title">申請基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>申請編號：</label>
              <span>{{ applicationData.id || "無" }}</span>
            </div>
            
            <div class="info-item">
              <label>申請人：</label>
              <span>{{ applicationData.requesterName || "無" }}</span>
            </div>
            
            <div class="info-item">
              <label>導出數量：</label>
              <span>{{ applicationData.exportCount || "0" }}</span>
            </div>
            
            <div class="info-item">
              <label>申請時間：</label>
              <span>{{ formatDate(applicationData.createTime) || "無" }}</span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <h3 class="info-card-title">申請備註</h3>
          <div class="remarks-content">
            {{ applicationData.requesterComment || "無備註內容" }}
          </div>
        </div>

        <div class="info-card" v-if="isPending">
          <h3 class="info-card-title">審批意見</h3>
          <textarea
            v-model="approvalRemarks"
            placeholder="請輸入審批意見（選填）"
            rows="3"
            class="approval-textarea"
          ></textarea>
        </div>

        <!-- 審批歷史記錄 -->
        <div class="info-card" v-if="!isPending && applicationData.approvalHistory">
          <h3 class="info-card-title">審批記錄</h3>
          <div class="timeline">
            <div class="timeline-item" v-for="(item, index) in applicationData.approvalHistory" :key="index">
              <div class="timeline-dot" :class="getTimelineDotClass(item.action)"></div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="timeline-action">{{ getActionText(item.action) }}</span>
                  <span class="timeline-time">{{ formatDate(item.time) }}</span>
                </div>
                <div class="timeline-comment" v-if="item.comment">{{ item.comment }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按鈕區域改進 -->
    <div v-if="applicationData" class="action-area">
      <div v-if="isPending" class="action-buttons">
        <button 
          class="reject-btn" 
          @click="handleReject"
          :disabled="processing"
        >
          <span v-if="!processing">拒絕</span>
          <span v-else class="loading-text">處理中...</span>
        </button>
        <button 
          class="approve-btn" 
          @click="handleApprove"
          :disabled="processing"
        >
          <span v-if="!processing">同意</span>
          <span v-else class="loading-text">處理中...</span>
        </button>
      </div>
      
      <div v-else class="reviewed-notice">
        <i class="el-icon-warning-outline"></i>
        <span>此申請已{{ statusText }}，無法再次審批</span>
      </div>
    </div>

    <!-- 操作結果提示改進 -->
    <transition name="fade">
      <div 
        v-if="showResultMessage"
        class="result-message"
        :class="resultMessageType"
      >
        <i :class="resultMessageIcon"></i>
        <span>{{ resultMessage }}</span>
      </div>
    </transition>
  </div>
</template>



<script>
import { getApplyByGuid, approveByEmail } from "@/api/pcm/approval";

export default {
  name: "ExportApproval",
  data() {
    return {
      logoUrl: require("@/assets/logo/login-logo2.png"),
      loading: true,
      processing: false,
      applicationData: null,
      approvalRemarks: "",
      showResultMessage: false,
      resultMessage: "",
      resultMessageType: "success",
    };
  },
  computed: {
    isPending() {
      return this.applicationData?.status === "PENDING";
    },
    statusText() {
      const statusMap = {
        PENDING: "待審批",
        APPROVED: "已批准",
        REJECTED: "已拒絕",
        CANCELLED: "已取消",
      };
      return statusMap[this.applicationData?.status] || "未知狀態";
    },
    statusClass() {
      return {
        "status-pending": this.applicationData?.status === "PENDING",
        "status-approved": this.applicationData?.status === "APPROVED",
        "status-rejected": this.applicationData?.status === "REJECTED",
        "status-cancelled": this.applicationData?.status === "CANCELLED",
      };
    },
    resultMessageIcon() {
      return {
        success: "el-icon-success",
        error: "el-icon-error",
      }[this.resultMessageType];
    },
  },
  created() {
    this.fetchApplicationData();
  },
  methods: {
    formatDate(dateString) {
      if (!dateString) return "";
      const date = new Date(dateString);
      return date.toLocaleString("zh-TW", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },
    
    getActionText(action) {
      const actionMap = {
        APPROVED: "批准",
        REJECT: "拒絕",
        PENDING: "提交申請",
      };
      return actionMap[action] || action;
    },
    
    getTimelineDotClass(action) {
      return {
        APPROVED: "timeline-approve",
        REJECT: "timeline-reject",
      }[action] || "timeline-default";
    },

    async fetchApplicationData() {
      try {
        this.loading = true;
        const guid = this.$route.query.guid || "";

        if (!guid) {
          this.showError("缺少申請編號參數");
          return;
        }

        // 添加延遲演示加載效果
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const response = await getApplyByGuid({ guid });
        
        if (response && response.data) {
          this.applicationData = response.data;
          
          // 模擬審批歷史數據（實際應從API獲取）
          if (!this.applicationData.approvalHistory) {
            this.applicationData.approvalHistory = [
              {
                action: "PENDING",
                time: this.applicationData.createTime,
                comment: this.applicationData.requesterComment,
              },
              {
                action: this.applicationData.status,
                time: this.applicationData.approveTime,
                comment: this.applicationData.approverComment,
              }
            ];
          }
        } else {
          this.showError(response?.message || "未找到申請記錄");
        }
      } catch (error) {
        console.error("獲取申請數據失敗:", error);
        this.showError("獲取申請數據失敗，請稍後重試");
      } finally {
        this.loading = false;
      }
    },

    async handleApprove() {
      if (!this.validateBeforeSubmit()) return;
      await this.submitApproval(true);
    },

    async handleReject() {
      if (!this.validateBeforeSubmit()) return;
      
      if (!this.approvalRemarks) {
        this.$confirm('拒絕申請建議填寫審批意見，是否繼續?', '提示', {
          confirmButtonText: '繼續拒絕',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submitApproval(false);
        });
      } else {
        await this.submitApproval(false);
      }
    },
    
    validateBeforeSubmit() {
      if (this.processing) return false;
      if (!this.isPending) {
        this.showError("當前申請狀態不可審批");
        return false;
      }
      return true;
    },

    async submitApproval(isApproved) {
      try {
        this.processing = true;
        
        const params = {
          guid: this.applicationData.guid,
          isApproved,
          approverComment: this.approvalRemarks,
        };

        const response = await approveByEmail(params);
        
        if (response && response.data===true) {
          this.resultMessage = isApproved ? "申請已批准" : "申請已拒絕";
          this.resultMessageType = "success";
          
          // 更新狀態和歷史記錄
          this.applicationData.status = isApproved ? "APPROVED" : "REJECTED";
          this.applicationData.approvalHistory.push({
            action: isApproved ? "APPROVED" : "REJECT",
            time: new Date().toISOString(),
            comment: this.approvalRemarks,
          });
        } else {
          throw new Error(response?.message || "審批提交失敗");
        }
      } catch (error) {
        console.error("審批提交失敗:", error);
        this.resultMessage = error.message || "審批提交失敗，請稍後重試";
        this.resultMessageType = "error";
      } finally {
        this.processing = false;
        this.showResultMessage = true;
        setTimeout(() => {
          this.showResultMessage = false;
        }, 3000);
      }
    },

    showError(message) {
      this.resultMessage = message;
      this.resultMessageType = "error";
      this.showResultMessage = true;
      setTimeout(() => {
        this.showResultMessage = false;
      }, 3000);
    },
  },
};
</script>

<style scoped>
/* 基礎樣式 */
.approval-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  color: #333;
  background-color: #f5f7fa;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 頁頭樣式改進 */
.approval-header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e6e8eb;
}

.header-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  height: 48px;
  margin-right: 20px;
}

.title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: #1a1a1a;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

/* 內容區域樣式改進 */
.approval-content {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 24px;
}

/* 信息卡片樣式 */
.info-card {
  margin-bottom: 24px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  overflow: hidden;
}

.info-card-title {
  margin: 0;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 600;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  word-break: break-word;
}

/* 備註內容樣式 */
.remarks-content {
  padding: 16px 20px;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

/* 審批文本區域樣式 */
.approval-textarea {
  width: 100%;
  padding: 12px;
  border: 0px solid #dcdfe6;
  border-radius: 4px;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  transition: border-color 0.2s;
  margin-top:10px;
}

.approval-textarea:focus {
  border-color: #409eff;
  outline: none;
}

/* 時間線樣式 */
.timeline {
  padding: 16px 20px;
}

.timeline-item {
  display: flex;
  padding-bottom: 16px;
  position: relative;
}

.timeline-item:last-child {
  padding-bottom: 0;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #e4e7ed;
  margin-right: 16px;
  margin-top: 4px;
  flex-shrink: 0;
}

.timeline-content {
  flex: 1;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.timeline-action {
  font-weight: 500;
}

.timeline-time {
  color: #909399;
  font-size: 12px;
}

.timeline-comment {
  color: #606266;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 4px;
}

/* 狀態顏色 */
.status-pending {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-approved {
  background-color: #f0f9eb;
  color: #67c23a;
}

.status-rejected {
  background-color: #fef0f0;
  color: #f56c6c;
}

.status-cancelled {
  background-color: #f2f6fc;
  color: #909399;
}

.timeline-approve {
  background-color: #67c23a;
}

.timeline-reject {
  background-color: #f56c6c;
}

.timeline-default {
  background-color: #e4e7ed;
}

/* 操作區域樣式 */
.action-area {
  position: sticky;
  bottom: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 12px 12px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.approve-btn, .reject-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.approve-btn {
  background-color: #67c23a;
  color: white;
}

.approve-btn:hover:not(:disabled) {
  background-color: #5daf34;
  box-shadow: 0 2px 12px rgba(103, 194, 58, 0.3);
}

.reject-btn {
  background-color: #f56c6c;
  color: white;
}

.reject-btn:hover:not(:disabled) {
  background-color: #e05e5e;
  box-shadow: 0 2px 12px rgba(245, 108, 108, 0.3);
}

.approve-btn:disabled, .reject-btn:disabled {
  background-color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.6;
}

/* 已審批提示 */
.reviewed-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #909399;
}

.reviewed-notice i {
  margin-right: 8px;
  font-size: 18px;
}

/* 結果提示消息 */
.result-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 24px;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  z-index: 2000;
}

.result-message i {
  margin-right: 8px;
  font-size: 18px;
}

.result-message.success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

.result-message.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
}

/* 動畫效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* 加載狀態 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* 無數據狀態 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
}

.no-data-img {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.no-data-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.no-data-hint {
  font-size: 14px;
  color: #909399;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .title{
        font-size: 15px;
    }
  .approval-container {
    padding: 16px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column-reverse;
  }
  
  .approve-btn, .reject-btn {
    width: 100%;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>