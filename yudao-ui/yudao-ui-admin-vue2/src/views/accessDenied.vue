<template>
  <div class="access-denied-container">
    <div class="access-denied-content">
      <img 
        src="@/assets/logo/login-logo2.png" 
        alt="Organization Logo" 
        class="logo"
      >

      <h1>Access Denied</h1>
      <p>
        You do not have permission to view this platform.
        Please contact Partnership Unit for assistance.
      </p>
      <a 
        href="http://intranet.hkfyg.org.hk/" 
        class="back-button"
      >
        Back to HKFYG Intranet
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AccessDenied'
}
</script>

<style scoped>
.access-denied-container {
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 1rem;
  overflow: hidden;
}

.access-denied-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  background-color: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  max-width: 32rem;
  width: 100%;
  text-align: center;
}

.logo {
  width: 24rem;
  height: auto;
  object-fit: contain;
  border-radius: 0.5rem;
}

h1 {
  font-size: 2.25rem;
  line-height: 2.5rem;
  font-weight: 700;
  color: #dc2626;
  margin-top: 1.5rem;
}

p {
  color: #374151;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.back-button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background-color: #ed6c00;
  color: white;
  font-weight: 600;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transition: all 0.3s ease-in-out;
}

.back-button:hover {
  background-color: #f97316;
}

.back-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  ring: 2px;
  ring-color: #ef4444;
  ring-opacity: 0.75;
}
</style>