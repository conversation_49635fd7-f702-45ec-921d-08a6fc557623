<template>
  <el-dialog
    :visible.sync="visible"
    :width="width"
    :before-close="handleClose"
    :show-close="false"
    class="base-dialog"
    custom-class="custom-dialog"
  >
    <!-- 自定义内容 -->
    <slot name="content"></slot>

    <!-- 按钮区域 -->
    <span slot="footer" class="dialog-footer">
      <slot name="footer">
        <!-- 自定义取消按钮 -->
        <el-button size="small" class="cancel-button" @click="handleCancel">
          {{ $t('cancel') }}
        </el-button>
        <!-- 自定义确定按钮 -->
        <el-button
          size="small"
          class="confirm-button"
          :style="{ backgroundColor: confirmColor }"
          @click="handleConfirm"
        >
          {{ $t(confirmText) }}
        </el-button>
      </slot>
    </span>

    <!-- 右上角自定义插槽 -->
    <template v-slot:title>
      <div class="dialog-title">
        <slot name="header">
          <span>{{  $t(title) }}</span>
        </slot>
        <div class="header-right">
          <slot name="header-right"></slot>
          <el-button
            v-if="showClose"
            class="close-button"
            icon="el-icon-close"
            @click="handleClose"
          ></el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: "BaseDialog",
  props: {
    // 是否显示弹窗
    value: {
      type: Boolean,
      default: false,
    },
    // 弹窗标题
    title: {
      type: String,
      default: "titleDefault",
    },
    // 弹窗宽度
    width: {
      type: String,
      default: "500px",
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: true,
    },
    // 确定按钮的文本
    confirmText: {
      type: String,
      default: "confirm",
    },
    // 确定按钮的颜色
    confirmColor: {
      type: String,
      default: "#1EA235",
    },
  },
  data() {
    return {
      visible: this.value,
    };
  },
  watch: {
    value(newValue) {
      this.visible = newValue;
    },
    visible(newValue) {
      this.$emit("input", newValue); // 同步 v-model 的值
    },
  },
  methods: {
    handleClose() {
      this.$emit("close"); // 触发关闭事件
      this.visible = false;
    },
    handleCancel() {
      this.$emit("cancel"); // 触发取消事件
      this.visible = false;
    },
    handleConfirm() {
      this.$emit("confirm"); // 触发确认事件
    },
  },
};
</script>

<style scoped>
.custom-dialog {
  border-radius: 12px; /* 圆弧样式 */
  background-color: #ffffff; /* 纯白色背景 */
}

.base-dialog /deep/ .el-dialog__header{
    padding: 10px 5px;
}

.base-dialog /deep/ .el-dialog__footer{
    padding: 5px 10px;
}
.base-dialog /deep/ .el-dialog__body {
    padding: 10px 15px;
}
.dialog-title{
    font-size: 18px;
    font-weight: 500;
    padding: 0;
}

.dialog-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px;
  border-bottom: 1px solid #e8e8e8; /* 添加细线 */
  padding-bottom: 10px; /* 调整标题与细线的间距 */
}

.header-right {
  display: flex;
  align-items: center;
}

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  margin-left: 10px; /* 调整关闭按钮与右边框的距离 */
}
.close-button /deep/ .el-button--small {
  border-radius: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  width: 100%; /* 按钮区域宽度占弹窗的100% */
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #ccc;
  color: #333;
  width: 48%; /* 取消按钮宽度占父容器的48% */
  margin-right: 4%; /* 按钮之间的间距 */
}

.confirm-button {
  color: #ffffff;
  border: none;
  width: 48%; /* 确定按钮宽度占父容器的48% */
}

.dialog-title /deep/ .el-button--text {
  color: #1ea235;
  font-size: 16px;
  font-weight: 400;
}
</style>