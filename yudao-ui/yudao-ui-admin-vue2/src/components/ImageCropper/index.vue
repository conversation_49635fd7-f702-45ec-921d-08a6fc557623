<template>
  <el-dialog
    :visible.sync="visible"
    title="圖片裁剪"
    width="680px"
    @close="handleClose"
    class="image-cropper-dialog"
  >
    <div class="cropper-container">
      <div class="cropper-wrapper" @wheel.prevent="handleWheel">
        <vue-cropper
          ref="cropper"
          :img="imageSrc"
          :autoCrop="true"
          :fixed="false"
          :centerBox="true"
          :high="true"
          :infoTrue="true"
          :canMove="true"
          :canMoveBox="true"
          :canScale="true"
          @imgLoad="imgLoad"
        ></vue-cropper>
      </div>
      
      <div class="operation-area">
        <el-button-group>
          <el-button size="small" @click="changeScale(-1)">
            <i class="el-icon-zoom-out"></i>
            縮小
          </el-button>
          <el-button size="small" @click="changeScale(1)">
            <i class="el-icon-zoom-in"></i>
            放大
          </el-button>
          <el-button size="small" @click="rotateLeft">
            <i class="el-icon-refresh-left"></i>
            向左旋轉
          </el-button>
          <el-button size="small" @click="rotateRight">
            <i class="el-icon-refresh-right"></i>
            向右旋轉
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirmCrop">確認</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { VueCropper } from 'vue-cropper'

export default {
  components: { VueCropper },
  data() {
    return {
      visible: false,
      imageSrc: '',
      file: null,
      currentScale: 1
    }
  },
  methods: {
    open(file) {
      this.file = file
      const reader = new FileReader()
      reader.onload = e => {
        this.imageSrc = e.target.result
        this.visible = true
        this.currentScale = 1
      }
      reader.readAsDataURL(file)
    },
    
    imgLoad() {
      this.$nextTick(() => {
        if (this.$refs.cropper) {
          this.currentScale = this.$refs.cropper.scale
        }
      })
    },
    
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    
    changeScale(num) {
      this.$refs.cropper.changeScale(num)
    },
    
    handleWheel(e) {
      e.preventDefault()
      if (e.deltaY < 0) {
        this.changeScale(0.1)
      } else {
        this.changeScale(-0.1)
      }
    },
    
    confirmCrop() {
      this.$refs.cropper.getCropBlob(blob => {
        const croppedFile = new File([blob], this.file.name, {
          type: this.file.type,
          lastModified: Date.now()
        })
        this.$emit('cropped', croppedFile)
        this.visible = false
      })
    },
    
    handleClose() {
      if (this.imageSrc) {
        URL.revokeObjectURL(this.imageSrc)
      }
      this.resetState()
    },
    
    resetState() {
      this.imageSrc = ''
      this.file = null
      this.currentScale = 1
    }
  }
}
</script>

<style lang="scss" scoped>
.image-cropper-dialog {
  .el-dialog__body {
    padding: 15px 20px;
  }
}

.cropper-container {
  display: flex;
  flex-direction: column;
  height: 500px;
}

.cropper-wrapper {
  flex: 1;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.operation-area {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  
  .el-button-group {
    display: flex;
    gap: 1px;
  }
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    
    i {
      font-size: 14px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>