{"name": "yudao-ui-admin", "version": "2.4.1-snapshot", "description": "PCM", "author": "PCM", "license": "MIT", "scripts": {"local": " vue-cli-service serve --mode local", "dev": "vue-cli-service serve --mode dev", "front": "vue-cli-service serve --mode front", "build:prod": "vue-cli-service build --mode prod", "build:stage": "vue-cli-service build --mode stage", "build:dev": "vue-cli-service build --mode dev", "build:static": "vue-cli-service build --mode static", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "clean": "rimraf node_modules"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://github.com/YunaiV/ruoyi-vue-pro"}, "dependencies": {"@babel/parser": "7.18.4", "@riophae/vue-treeselect": "0.4.0", "axios": "0.27.2", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "0.10.0", "clipboard": "2.0.8", "core-js": "^3.26.0", "crypto-js": "^4.0.0", "echarts": "5.4.0", "element-ui": "2.15.14", "file-saver": "2.0.5", "fuse.js": "6.6.2", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "jsencrypt": "3.3.1", "libphonenumber-js": "^1.12.9", "lodash-es": "^4.17.21", "min-dash": "3.5.2", "nprogress": "0.2.0", "qrcode.vue": "^1.7.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "throttle-debounce": "2.1.0", "vue": "2.7.14", "vue-count-to": "1.0.13", "vue-cropper": "0.5.8", "vue-i18n": "^8.28.2", "vue-meta": "^2.4.0", "vue-quill-editor": "^3.0.6", "vue-router": "3.4.9", "vue-video-player": "^5.0.2", "vuedraggable": "2.24.3", "vuex": "3.6.2", "xml-js": "1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.5.18", "@vue/compiler-sfc": "^3.0.1", "@vue/eslint-config-prettier": "^5.0.0", "babel-eslint": "10.1.0", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "7.2.0", "fs-extra": "^8.1.0", "lint-staged": "12.5.0", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.2.0", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "terser-webpack-plugin": "^4.2.3", "webpack-bundle-analyzer": "^3.9.0"}, "engines": {"node": ">= 8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}