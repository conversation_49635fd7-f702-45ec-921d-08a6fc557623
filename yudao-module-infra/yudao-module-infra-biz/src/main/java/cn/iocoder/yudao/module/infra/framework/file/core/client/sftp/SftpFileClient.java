package cn.iocoder.yudao.module.infra.framework.file.core.client.sftp;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.ssh.Sftp;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.module.infra.framework.file.core.client.AbstractFileClient;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import org.apache.commons.io.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * Sftp 文件客户端
 *
 * <AUTHOR>
 */
public class SftpFileClient extends AbstractFileClient<SftpFileClientConfig> {

    public SftpFileClient(Long id, SftpFileClientConfig config) {
        super(id, config);
    }

    @Override
    protected void doInit() {
        // 补全风格。例如说 Linux 是 /，Windows 是 \
        if (!config.getBasePath().endsWith(File.separator)) {
            config.setBasePath(config.getBasePath() + File.separator);
        }
        // 初始化 Ftp 对象
        //this.sftp = new Sftp(config.getHost(), config.getPort(), config.getUsername(), config.getPassword());
    }

    @Override
    public String upload(byte[] content, String path, String type) {
        // 执行写入
        int maxRetries = 3;
        int retryCount = 0;
        File tempFile = null;

        while (retryCount < maxRetries) {
            try (Sftp sftp = createSftpClient()) {
                // 创建临时文件
                tempFile = FileUtils.createTempFile(content);
                String remoteFilePath = getFilePath(path);

                // 确保远程目录存在
                createParentDirectories(sftp, remoteFilePath);

                // 执行上传
                sftp.upload(remoteFilePath, tempFile);
                // log.info("[upload][文件上传成功 path: {}]", path);
                return super.formatFileUrl(config.getDomain(), path);
            } catch (Exception e) {
                // log.error("[upload][文件上传失败 path: {}, retry: {}/{}]", path, retryCount, maxRetries, e);
                if (retryCount++ >= maxRetries - 1) {
                    throw ExceptionUtil.wrapRuntime(e);
                }
                try {
                    Thread.sleep(1000 * retryCount);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            } finally {
                if (tempFile != null) {
                    FileUtil.del(tempFile);
                }
            }
        }
        throw new RuntimeException("文件上传失败，重试次数耗尽");
    }

    @Override
    public void delete(String path) {
        int maxRetries = 3;
        int retryCount = 0;
        String remotePath = getFilePath(path);

        while (retryCount < maxRetries) {
            try (Sftp sftp = createSftpClient()) {
                if (sftp.exist(remotePath)) {
                    sftp.delFile(remotePath);
                    // log.info("[delete][文件删除成功 path: {}]", path);
                    return;
                }
            } catch (Exception e) {
                // log.error("[delete][文件删除失败 path: {}, retry: {}/{}]", path, retryCount, maxRetries, e);
                if (retryCount++ >= maxRetries - 1) {
                    throw ExceptionUtil.wrapRuntime(e);
                }
                try {
                    Thread.sleep(1000 * retryCount);
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    @Override
    public byte[] getContent(String path) {
        String remotePath = getFilePath(path);
//            try (Sftp sftp = createSftpClient(); ByteArrayOutputStream output = new ByteArrayOutputStream()) {
//                sftp.getClient().get(remotePath,output);
//
//               // sftp.download(remotePath, output);
//                // log.debug("[getContent][文件下载成功 path: {}]", path);
//                return output.toByteArray();
//            } catch (Exception e) {
//                System.out.println("[getContent][报错: {}]"+ e.getMessage());
//                // log.error("[getContent][文件下载失败 path: {}, retry: {}/{}]", path, retryCount, maxRetries, e);
//                throw ExceptionUtil.wrapRuntime(e);
//            }

        ChannelSftp sftp = null;
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        try {
            JSch jsch = new JSch();
            Session session = jsch.getSession(config.getUsername(), config.getHost(), config.getPort());
            session.setPassword(config.getPassword());
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();

            sftp = (ChannelSftp) session.openChannel("sftp");
            sftp.connect();

            sftp.get(remotePath, output);
            return output.toByteArray();
        } catch (Exception e) {
            System.err.println("下载失败: " + remotePath);
            e.printStackTrace();
            throw ExceptionUtil.wrapRuntime(e);
        } finally {
            if (sftp != null) sftp.disconnect();
            try {
                if(output!=null) {
                    output.close();
                }
            } catch (Exception ex) {}

        }
    }

    private String getFilePath(String path) {
        return config.getBasePath() + path;
    }

    private Sftp createSftpClient() {
        return new Sftp(config.getHost(), config.getPort(), config.getUsername(), config.getPassword());
    }

    private void createParentDirectories(Sftp sftp, String remoteFilePath) {
        String parentDir = FileUtil.getParent(remoteFilePath, 1);
        if (sftp.exist(parentDir)) {
            return;
        }
        System.out.println("[createParentDirectories][创建远程目录: {}]"+ parentDir);
        sftp.mkDirs(parentDir);
    }

}
