package cn.iocoder.yudao.module.infra.api.file;

import cn.iocoder.yudao.module.infra.service.file.FileService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;

    @Override
    public String createFile(String name, String path, byte[] content) {
        return fileService.createFile(name, path, content);
    }

    @Override
    public JSONObject pcmCreateFile(String name, String path, byte[] content) {
        return fileService.pcmCreateFile(name, path, content);
    }

    @Override
    public byte[] getFileById(Long id) {
        return fileService.getFileById(id);
    }

    @Override
    public JSONObject getFileDOById(Long id) {
        return fileService.getFileDOById(id);
    }

}
