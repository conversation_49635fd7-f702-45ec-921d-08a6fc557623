package cn.iocoder.yudao.module.infra.api.file;

import com.alibaba.fastjson.JSONObject;

/**
 * 文件 API 接口
 *
 * <AUTHOR>
 */
public interface FileApi {

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    default String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name 文件名称
     * @param path 文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    String createFile(String name, String path, byte[] content);

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param name    文件名称
     * @param path    文件路径
     * @param content 文件内容
     * @return FileDO的JSON对象
     */
    JSONObject pcmCreateFile(String name, String path, byte[] content);

    /**
     * 获得文件内容
     *
     * @param id 文件编号
     * @return 文件内容
     */
    byte[] getFileById(Long id);

    /**
     * 获得文件信息
     *
     * @param id 文件编号
     * @return 文件信息
     */
    JSONObject getFileDOById(Long id);
}
