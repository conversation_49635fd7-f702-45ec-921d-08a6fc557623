<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.pcm.dal.mysql.meetingpermission.MeetingPermissionMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 删除指定会议记录的所有权限 -->
    <delete id="deleteByMeetingId">
        DELETE FROM pcm_meeting_permission WHERE meeting_id = #{meetingId}
    </delete>

    <!-- 校验用户是否有权限查看会议记录 -->
    <select id="hasPermission" resultType="int">
        SELECT COUNT(*)
        FROM pcm_meeting_permission
        WHERE meeting_id = #{meetingId}
        AND (
        user_id = #{userId}
        OR dept_id = #{deptId}
        OR role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        )
    </select>

</mapper>