<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.pcm.dal.mysql.card.CardMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectDistinctGroupIdPage" resultType="map">
        SELECT
        id,
        group_id AS groupId,
        first_name AS firstName,
        last_name AS lastName,
        full_name AS fullName,
        first_name_en AS firstNameEn,
        last_name_en AS lastNameEn,
        company_id AS companyId,
        company_name AS companyName,
        company_id_en AS companyIdEn,
        company_name_en AS companyNameEn,
        job_title AS jobTitle,
        job_title_en AS jobTitleEn,
        image_url AS imageUrl,
        create_time as createTime
        FROM (
        SELECT
        t.id,
        t.group_id,
        t.first_name,
        t.last_name,
        t.full_name,
        t.first_name_en,
        t.last_name_en,
        t.company_id,
        t.company_name,
        t.company_id_en,
        t.company_name_en,
        t.job_title,
        t.job_title_en,
        t.image_url,
        t.create_time,
        ROW_NUMBER() OVER (PARTITION BY t.group_id ORDER BY t.id) AS rn
        FROM pcm_card t
        WHERE t.deleted = 0
        <if test="reqVO.userId != null and reqVO.userId != '' or reqVO.deptId != null and reqVO.deptId != ''">
            AND (
            -- 默认无权限（user_id 和 dept_id 都为 null）时，所有人可见
            (t.user_id IS NULL AND t.dept_id IS NULL)
            <if test="reqVO.userId != null and reqVO.userId != ''">
                OR t.user_id LIKE CONCAT('%', #{reqVO.userId}, '%')
            </if>
            <if test="reqVO.deptId != null and reqVO.deptId != ''">
                OR t.dept_id LIKE CONCAT('%', #{reqVO.deptId}, '%')
            </if>
            <if test="reqVO.createUserId != null">
                OR t.create_user_id = #{reqVO.createUserId}
            </if>
            )
        </if>
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            AND (
            t.first_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.last_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.full_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.company_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.company_name_en LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.job_title LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.email LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.phone_mobile LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR CONCAT(t.first_name, '', t.last_name) LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR LOWER(CONCAT(TRIM(t.last_name_en), ' ', TRIM(t.first_name_en))) LIKE CONCAT('%',
            LOWER(#{reqVO.keyword}),'%')
            OR LOWER(CONCAT(TRIM(t.last_name_en), '', TRIM(t.first_name_en))) LIKE CONCAT('%',
            LOWER(#{reqVO.keyword}),'%')
            )
        </if>
        <if test="reqVO.currentCardId != null">
            AND t.id != #{reqVO.currentCardId}
        </if>
        <if test="reqVO.groupId != null and reqVO.groupId != ''">
            AND t.group_Id != #{reqVO.groupId}
        </if>

        <if test="reqVO.orderBy != null and reqVO.orderBy != ''">
            ORDER BY
            <choose>
                <when test="reqVO.orderBy.split('\\|')[0] == 'createTime'">
                    t.create_time
                </when>
                <when test="reqVO.orderBy.split('\\|')[0] == 'id'">
                    t.id
                </when>
                <!-- 可以继续加其他允许排序的字段 -->
                <otherwise>
                    t.id
                </otherwise>
            </choose>
            <choose>
                <when test="reqVO.orderBy.split('\\|')[1] == 'asc'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        </if>
        ) t
        WHERE t.rn = 1
    </select>

    <!--名片詳情左邊的列表Ids-->
    <select id="selectDistinctGroupIds" resultType="map">
        SELECT
        id
        FROM (
        SELECT
        t.id,
        t.group_id,
        ROW_NUMBER() OVER (PARTITION BY t.group_id ORDER BY t.id) AS rn
        FROM pcm_card t
        WHERE t.deleted = 0
        <if test="reqVO.userId != null and reqVO.userId != '' or reqVO.deptId != null and reqVO.deptId != ''">
            AND (
            -- 默认无权限（user_id 和 dept_id 都为 null）时，所有人可见
            (t.user_id IS NULL AND t.dept_id IS NULL)
            <if test="reqVO.userId != null and reqVO.userId != ''">
                OR t.user_id LIKE CONCAT('%', #{reqVO.userId}, '%')
            </if>
            <if test="reqVO.deptId != null and reqVO.deptId != ''">
                OR t.dept_id LIKE CONCAT('%', #{reqVO.deptId}, '%')
            </if>
            <if test="reqVO.createUserId != null">
                OR t.create_user_id = #{reqVO.createUserId}
            </if>
            )
        </if>
        <if test="reqVO.keyword != null and reqVO.keyword != ''">
            AND (
            t.first_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.last_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.full_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.company_name LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.company_name_en LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.job_title LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.email LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR t.phone_mobile LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR CONCAT(t.first_name, '', t.last_name) LIKE CONCAT('%', #{reqVO.keyword}, '%')
            OR LOWER(CONCAT(TRIM(t.last_name_en), ' ', TRIM(t.first_name_en))) LIKE CONCAT('%', LOWER(#{reqVO.keyword}), '%')
            OR LOWER(CONCAT(TRIM(t.last_name_en), '', TRIM(t.first_name_en))) LIKE CONCAT('%', LOWER(#{reqVO.keyword}), '%')
            )
        </if>
        <if test="reqVO.currentCardId != null">
            AND t.id != #{reqVO.currentCardId}
        </if>
        <if test="reqVO.groupId != null and reqVO.groupId != ''">
            AND t.group_id != #{reqVO.groupId}
        </if>
        <if test="reqVO.orderBy != null and reqVO.orderBy != ''">
            ORDER BY
            <choose>
                <when test="reqVO.orderBy.split('\\|')[0] == 'createTime'">
                    t.create_time
                </when>
                <when test="reqVO.orderBy.split('\\|')[0] == 'id'">
                    t.id
                </when>
                <otherwise>
                    t.id
                </otherwise>
            </choose>
            <choose>
                <when test="reqVO.orderBy.split('\\|')[1] == 'asc'">
                    ASC
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
        </if>
        ) t
        WHERE t.rn = 1
    </select>

    <!--
        業務邏輯：優先選中文，沒有中文選英文
    -->
    <select id="getCompanyList" resultType="map">
        SELECT DISTINCT
            CASE
                WHEN NULLIF(TRIM(company_name), '') IS NOT NULL THEN company_id
                WHEN NULLIF(TRIM(company_name_en), '') IS NOT NULL THEN company_id_en
                ELSE NULL
                END AS id,
            COALESCE(NULLIF(TRIM(company_name), ''), company_name_en) AS name
        FROM pcm_card
        WHERE COALESCE(NULLIF(TRIM(company_name), ''), company_name_en) IS NOT NULL
          AND (
            (NULLIF(TRIM(company_name), '') IS NOT NULL AND company_id IS NOT NULL)
                OR (NULLIF(TRIM(company_name), '') IS NULL AND NULLIF(TRIM(company_name_en), '') IS NOT NULL AND company_id_en IS NOT NULL)
            )
    </select>

    <!--
        查询有效的公司信息（中英文），并去重。
        支持字段动态切换：
        - lang == 'zh'：查询中文字段 company_id、company_name
        - lang == 'en'：查询英文字段 company_id_en、company_name_en

        条件：
        - 字段值不为 NULL
        - 去除空格后不为空字符串
    -->
    <select id="getSimpleCompanyList" resultType="map">
        SELECT DISTINCT
        <choose>
            <when test="lang == 'en'">
                company_id_en AS id,
                company_name_en AS name
            </when>
            <otherwise>
                company_id AS id,
                company_name AS name
            </otherwise>
        </choose>
        FROM pcm_card
        WHERE
        <choose>
            <when test="lang == 'en'">
                company_id_en IS NOT NULL AND TRIM(company_id_en) &lt;&gt; ''
                AND company_name_en IS NOT NULL AND TRIM(company_name_en) &lt;&gt; ''
            </when>
            <otherwise>
                company_id IS NOT NULL AND TRIM(company_id) &lt;&gt; ''
                AND company_name IS NOT NULL AND TRIM(company_name) &lt;&gt; ''
            </otherwise>
        </choose>
    </select>


    <select id="findByCompanyIdOrEn" resultType="map">
        select id, company_id, company_name, company_id_en, company_name_en from pcm_card
        WHERE
        <choose>
            <when test="searchByEn">
                company_id_en = #{companyId}
            </when>
            <otherwise>
                company_id = #{companyId}
            </otherwise>
        </choose>
        LIMIT 1
    </select>


</mapper>
