package cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商谈记录表，存储名片相关的商谈记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeetingRespVO {

    @Schema(description = "商谈记录ID", example = "10000001")
    @ExcelProperty("商谈记录ID")
    private Long id;

    @Schema(description = "名片ID", example = "10000001")
    @ExcelProperty("名片ID")
    private Long cardId;

    @Schema(description = "商谈类型", example = "1")
    @ExcelProperty("商谈类型")
    private String type;

    @Schema(description = "商谈主题", example = "技术合作洽谈")
    @ExcelProperty("商谈主题")
    private String title;

    @Schema(description = "商谈内容", example = "讨论了新产品的技术合作方案，达成初步意向。")
    @ExcelProperty("商谈内容")
    private String content;

    @Schema(description = "会议场合", example = "技术峰会")
    @ExcelProperty("会议场合")
    private String occasion;

    @Schema(description = "商谈时间")
    @ExcelProperty("商谈时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime meetingTime;

    @Schema(description = "图片", example = "https://www.iocoder.cn")
    @ExcelProperty("图片")
    private String imageUrl;

    @Schema(description = "创建人id")
    private Long createUserId;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "商谈人姓名")
    private String userName;

    @Schema(description = "商谈人职位")
    private String jobTitle;

    @Schema(description = "有权向查看的同事")
    private String userId;

    @Schema(description = "有权限查看的部门")
    private String deptId;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updater;

}