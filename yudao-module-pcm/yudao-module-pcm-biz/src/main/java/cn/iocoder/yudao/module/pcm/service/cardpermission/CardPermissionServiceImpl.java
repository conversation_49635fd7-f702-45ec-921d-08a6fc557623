package cn.iocoder.yudao.module.pcm.service.cardpermission;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo.CardPermissionPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo.CardPermissionSaveReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo.CardPermissionVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission.CardPermissionDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.card.CardMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.cardpermission.CardPermissionMapper;
import cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.CARD_ID_IS_EMPTY;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.CARD_PERMISSION_NOT_EXISTS;
import static com.baomidou.mybatisplus.extension.toolkit.Db.saveBatch;

@Service
public class CardPermissionServiceImpl implements CardPermissionService {

    private static final Logger log = LoggerFactory.getLogger(CardPermissionServiceImpl.class);
    @Resource
    private CardPermissionMapper cardPermissionMapper;
    @Resource
    private CardMapper cardMapper;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;

    @Override
    public Long createCardPermission(CardPermissionSaveReqVO createReqVO) {
        // 插入
        CardPermissionDO cardPermission = BeanUtils.toBean(createReqVO, CardPermissionDO.class);
        cardPermissionMapper.insert(cardPermission);
        // 返回
        return cardPermission.getId();
    }

    @Override
    @Transactional
    public CommonResult<Long> savePermission(Long cardId, JSONArray permissionArray) {
        try {
            // 校验输入
            if (cardId == null) {
                throw exception(CARD_ID_IS_EMPTY);
            }

            //更新名片权限
            CardDO cardDO = cardMapper.selectById(cardId);

            // 将 JSONArray 转换为 List<CardPermissionVO>
            List<CardPermissionVO> permissionVOs;
            if (permissionArray == null || permissionArray.isEmpty()) {
                permissionVOs = Collections.emptyList();
            } else {
                permissionVOs = permissionArray.toJavaList(CardPermissionVO.class);
            }

            // 收集所有 userId 用于批量删除
            Set<Long> userIds = permissionVOs.stream().map(CardPermissionVO::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
            String deptId = cardDO.getDeptId();
            if (StringUtils.isNotBlank(deptId) && !userIds.isEmpty()) {
                List<Long> userIdList = userIds.stream().collect(Collectors.toList());
                JSONArray userList = adminUserApi.getUserListByIds(userIdList);
                String[] deptIds = deptId.split(";");
                // 将 deptIds 转换为 Set 以便快速查找
                Set<String> deptIdSet = new HashSet<>(Arrays.asList(deptIds));

                Collection<Long> ids = deptIdSet.stream()
                        .map(id -> {
                            try {
                                return Optional.of(Long.parseLong(id));
                            } catch (NumberFormatException e) {
                                return Optional.<Long>empty();
                            }
                        })
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
                Map<Long, DeptRespDTO> deptMap = deptApi.getDeptMap(ids);

                // 收集匹配的部门和用户名
                StringBuilder matchedUsers = new StringBuilder();
                for (int i = 0; i < userList.size(); i++) {
                    JSONObject user = userList.getJSONObject(i);
                    String userDept = user.getString("deptId");
                    String deptName = getDeptName(userDept, deptMap);
                    String userName = user.getString("nickname");
                    if (StringUtils.isNotBlank(userDept) && deptIdSet.contains(userDept)) {
                        if (matchedUsers.length() > 0) {
                            matchedUsers.append("、");
                        }
//                        matchedUsers.append("用戶: ").append(userName).append(", 部門: ").append(deptName).append("\n");
                        matchedUsers.append(userName).append("\n");
                    }
                }

                // 如果有匹配项，抛出异常
                if (matchedUsers.length() > 0) {
                    String errorMessage = String.format("「%s 同事已在『有權限查看的同事』名單中，如欲新增至『有權限查看的部門』名單，請先將相關同事從同事名單中移除。」", matchedUsers);
                    throw new IllegalArgumentException(errorMessage);
                }
            }

            // 步骤 1：根据 cardId 和 userIds 批量物理删除已有权限
            LambdaQueryWrapper<CardPermissionDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CardPermissionDO::getCardId, cardId);
            cardPermissionMapper.delete(queryWrapper);

            // 步骤 2：批量插入新权限
            List<CardPermissionDO> permissionDOs = new ArrayList<>();
            for (CardPermissionVO vo : permissionVOs) {
                Long userId = vo.getUserId();
                List<String> fieldPermissions = vo.getFieldPermissions();

                if (userId == null) {
                    continue; // 跳过无效数据
                }

                // 为每个 fieldPermission 创建 CardPermissionDO
                CardPermissionDO permissionDO = new CardPermissionDO();
                permissionDO.setCardId(cardId);
                permissionDO.setUserId(userId);
                permissionDO.setIsEdit(vo.getIsEdit());

                String fieldName = "";
                if (fieldPermissions == null || fieldPermissions.isEmpty()) {
                    fieldName = "[]";
                } else {
                    fieldName = JSON.toJSONString(fieldPermissions);
                }

                permissionDO.setFieldName(fieldName);
                permissionDO.setCreator(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
                permissionDO.setCreateTime(LocalDateTime.now());
                // 如果需要 deptId 或 roleId，可以在这里设置
                permissionDOs.add(permissionDO);
            }

            // 批量插入
            if (!permissionDOs.isEmpty()) {
                saveBatch(permissionDOs);
            }

            Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
            // 将用户ID集合转换为字符串列表，并用分号分隔
            String userId = String.join(";", userIds.stream()
                    .map(String::valueOf).collect(Collectors.toList()));

            cardDO.setUserId(userId);
            cardMapper.updateById(cardDO);

            return CommonResult.success(cardId);
        } catch (Exception e) {
            return CommonResult.error(400, e.getMessage());
        }
    }

    private String getDeptName(String deptId, Map<Long, DeptRespDTO> deptMap) {
        DeptRespDTO deptRespDTO = deptMap.get(Long.parseLong(deptId));
        String deptName = deptRespDTO != null ? deptRespDTO.getName() : null;
        return deptName;
    }

    @Override
    public void updateCardPermission(CardPermissionSaveReqVO updateReqVO) {
        // 校验存在
        validateCardPermissionExists(updateReqVO.getId());
        // 更新
        CardPermissionDO updateObj = BeanUtils.toBean(updateReqVO, CardPermissionDO.class);
        cardPermissionMapper.updateById(updateObj);
    }

    @Override
    public void deleteCardPermission(Long id) {
        // 校验存在
        validateCardPermissionExists(id);
        // 删除
        cardPermissionMapper.update(Wrappers.<CardPermissionDO>lambdaUpdate()
                .set(CardPermissionDO::getDeleted, 1)
                .eq(CardPermissionDO::getId, id));
    }

    private void validateCardPermissionExists(Long id) {
        if (cardPermissionMapper.selectById(id) == null) {
            throw exception(CARD_PERMISSION_NOT_EXISTS);
        }
    }

    @Override
    public CardPermissionDO getCardPermission(Long id) {
        return cardPermissionMapper.selectById(id);
    }

    @Override
    public PageResult<CardPermissionDO> getCardPermissionPage(CardPermissionPageReqVO pageReqVO) {
        return cardPermissionMapper.selectPage(pageReqVO);
    }

    @Override
    public JSONArray getCardPermissionByCardId(Long cardId) {
        return transformToJsonArray(cardPermissionMapper.getByCardId(cardId));
    }

    private JSONArray transformToJsonArray(List<CardPermissionDO> permissions) {
        // 按 userId 和 cardId 分组权限
        Map<Long, Map<Long, List<String>>> userCardFieldMap = new HashMap<>();
        for (CardPermissionDO permission : permissions) {
            // 解析 fieldName JSON 字符串为列表
            JSONArray fields = JSONObject.parseArray(permission.getFieldName());
            List<String> fieldList = new ArrayList<>();
            for (int i = 0; i < fields.size(); i++) {
                fieldList.add(fields.getString(i));
            }

            // 按 userId 和 cardId 组织数据
            userCardFieldMap
                    .computeIfAbsent(permission.getUserId(), k -> new HashMap<>())
                    .computeIfAbsent(permission.getCardId(), k -> new ArrayList<>())
                    .addAll(fieldList);

        }

        // 转换为目标 JSON 结构
        JSONArray resultArray = new JSONArray();
        userCardFieldMap.forEach((userId, cardMap) -> {
            cardMap.forEach((cardId, fieldList) -> {
                JSONObject userPermission = new JSONObject();
                userPermission.put("userId", userId);
                userPermission.put("cardId", cardId);
                // 添加 isEdit 字段，从 permissions 中获取
                userPermission.put("isEdit", permissions.stream()
                        .filter(p -> p.getUserId().equals(userId) && p.getCardId().equals(cardId))
                        .findFirst()
                        .map(CardPermissionDO::getIsEdit)
                        .orElse(false)); // 提供默认值以防万一
                userPermission.put("fieldPermissions", fieldList);
                resultArray.add(userPermission);
            });
        });
        return resultArray;
    }

}