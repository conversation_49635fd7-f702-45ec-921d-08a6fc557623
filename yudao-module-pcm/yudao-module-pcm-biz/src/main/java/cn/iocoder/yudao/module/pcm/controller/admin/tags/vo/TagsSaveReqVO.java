package cn.iocoder.yudao.module.pcm.controller.admin.tags.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 标签新增/修改 Request VO")
@Data
public class TagsSaveReqVO {

    @Schema(description = "标签ID", example = "10000001")
    private Long id;

    @Schema(description = "公司ID", example = "10000001")
    private Long companyId;

    @Schema(description = "标签名称", example = "河南中原高速公路")
    private String name;

    @Schema(description = "描述/备注")
    private String description;

}