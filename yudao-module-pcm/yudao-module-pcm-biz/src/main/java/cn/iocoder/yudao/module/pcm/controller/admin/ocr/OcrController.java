package cn.iocoder.yudao.module.pcm.controller.admin.ocr;

import cn.hutool.core.io.IoUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardSaveReqVO;
import cn.iocoder.yudao.module.pcm.ocr.service.OcrService;
import cn.iocoder.yudao.module.pcm.ocr.vo.CardRecognitionResult;
import cn.iocoder.yudao.module.pcm.ocr.vo.ProcessedCard;
import cn.iocoder.yudao.module.pcm.service.redis.CardRedisService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

@Tag(name = "管理後臺 - Ocr")
@RestController
@RequestMapping("/pcm/ocr")
@Validated
public class OcrController {

    private static final Logger log = LoggerFactory.getLogger(OcrController.class);
    @Resource
    private OcrService ocrService;

    @Resource
    private FileApi fileApi;

    @PostMapping("/upload")
    @Operation(summary = "上傳名片", description = "pcm上傳接口，記錄上傳了上傳的文件")
    public CommonResult<JSONArray> pcmUpload(@RequestParam("files") MultipartFile[] files) throws Exception {
        log.info("ocr upload");
        // 判斷 files 是否為空或長度為 0
        if (files == null || files.length == 0 || Arrays.stream(files)
                .allMatch(file -> file == null || file.isEmpty() || file.getOriginalFilename().trim().isEmpty())) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请至少上传一个文件进行识别");
        }
        if (files.length > 10) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "最多上传10张图片");
        }
        JSONArray jsonArray = new JSONArray();
        for (MultipartFile file : files) {

            if (file.isEmpty()) {
                return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "上传文件不能为空");
            }

            if (file.getSize() > 15 * 1024 * 1024) {
                return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "文件大小不能超过15M");
            }
            String oldName = file.getOriginalFilename();
            String extension = StringUtils.substringAfterLast(oldName, "."); // 取文件扩展名
            String fileName = UUID.randomUUID().toString().replace("-", "") +
                    (StringUtils.isNotBlank(extension) ? "." + extension : "");

            byte[] fileBytes = IoUtil.readBytes(file.getInputStream());
            JSONObject jsonObject = fileApi.pcmCreateFile(fileName, fileName, fileBytes);
            jsonArray.add(jsonObject);
        }

        return CommonResult.success(jsonArray);
    }

    @PostMapping("/recognize")
    @Operation(summary = "識別名片")
    @Deprecated
    public CommonResult<List<Long>> recognition(@RequestBody JSONObject params) {
        JSONArray arrayIds = params.getJSONArray("fileIds");
        if (arrayIds == null || arrayIds.isEmpty()) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "fileIds 列表不能为空");
        }
        List<Long> fileIds = arrayIds.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        List<Long> cardIds = new ArrayList<>();
        for (Long fileId : fileIds) {
            JSONObject fileDO = fileApi.getFileDOById(fileId);
            if (fileDO == null) {
                return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "檔案ID不存在: " + fileId);
            }
            byte[] bytes = fileApi.getFileById(fileId);
            String url = fileDO.getString("url");
            BusinessCardOCRResponse resp = ocrService.recognizeBusinessCard(bytes);
            cardIds.add(ocrService.singleSaveCard(resp, url));
        }
        // 倒序排列
        Collections.reverse(cardIds);
        // 返回所有识别结果
        return CommonResult.success(cardIds);
    }

    @PostMapping("/doubleSidedCard")
    @Operation(summary = "識別雙面名片", description = "識別雙面名片")
    public CommonResult<CardRecognitionResult> doubleSidedCard(@RequestBody JSONObject params) {
        // 获取 fileIds 数组
        JSONArray fileIdsArray = params.getJSONArray("fileIds");

        if (fileIdsArray == null || fileIdsArray.isEmpty()) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "檔案 ID 列表不能為空");
        }

        //最多能识别20张名片
        if (fileIdsArray.size() > 20) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "最多能識別20張名片");
        }

        CardRecognitionResult result = new CardRecognitionResult();
        List<ProcessedCard> processedCards = result.getProcessedCards();

        for (int i = 0; i < fileIdsArray.size(); i++) {
            JSONObject fileIdObj = fileIdsArray.getJSONObject(i);
            Long frontFileId = fileIdObj.getLong("frontFileId");
            Long backFileId = fileIdObj.getLong("backFileId");

            // 至少需要正面或反面檔案 ID
            if (frontFileId == null && backFileId == null) {
                processedCards.add(new ProcessedCard(null, null, null, "ERROR", "正面和反面檔案 ID 不能同時為空"));
                continue;
            }

            // 调用服务层处理名片识别
            ProcessedCard processedCard = ocrService.recognitionDoubleSideCardQwen(frontFileId, backFileId);
            processedCards.add(processedCard);
        }

        return CommonResult.success(result);
    }

    /**
     * 上傳名片並識別內容
     * @param files
     * @return
     * @throws IOException
     */
    @Deprecated
    public CommonResult<List<String>> recognizeBusinessCard(@RequestParam("files") MultipartFile[] files) throws IOException {

        // 判斷 files 是否為空或長度為 0
        if (files == null || files.length == 0 || Arrays.stream(files)
                .allMatch(file -> file == null || file.isEmpty() || file.getOriginalFilename().trim().isEmpty())) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "请至少上传一个文件进行识别");
        }

        List<String> results = new ArrayList<>();

        for (MultipartFile file : files) {
            // 將 MultipartFile 轉換為臨時檔案，並獲取檔案路徑
            File tempFile = convertMultipartFileToFile(file);
            try {
                // 調用 OcrService 的方法識別名片內容
                BusinessCardOCRResponse resp = ocrService.recognizeBusinessCard(tempFile.getAbsolutePath());
                // 將識別結果添加到結果列表
                results.add(BusinessCardOCRResponse.toJsonString(resp));
            } finally {
                // 確保臨時檔案在使用完成後被刪除
                if (tempFile != null && tempFile.exists()) {
                    tempFile.delete();
                }
            }
        }
        // 返回所有識別結果
        return CommonResult.success(results);
    }

    /**
     * 將 MultipartFile 轉換為臨時檔案
     *
     * @param file 上傳的檔案
     * @return 臨時檔案物件
     */
    private File convertMultipartFileToFile(MultipartFile file) throws IOException {
        // 創建臨時檔案，檔案名包含原始檔案名（避免衝突）
        File tempFile = Files.createTempFile("uploaded-", "-" + file.getOriginalFilename()).toFile();
        // 將上傳的檔案內容寫入臨時檔案
        file.transferTo(tempFile);
        // 返回臨時檔案物件
        return tempFile;
    }

}
