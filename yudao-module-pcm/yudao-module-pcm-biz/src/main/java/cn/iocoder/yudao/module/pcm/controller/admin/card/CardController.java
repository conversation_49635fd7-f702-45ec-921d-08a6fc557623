package cn.iocoder.yudao.module.pcm.controller.admin.card;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportRecordDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportTotalDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportRecordMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportTotalMapper;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import cn.iocoder.yudao.module.pcm.service.export.ExportService;
import cn.iocoder.yudao.module.pcm.service.meeting.MeetingService;
import cn.iocoder.yudao.module.pcm.util.CardImportExcelSampleDataUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.*;

@Tag(name = "管理後臺 - 名片")
@RestController
@RequestMapping("/pcm/card")
@Validated
public class CardController {
    private static final Logger log = LoggerFactory.getLogger(CardController.class);
    @Resource
    private CardService cardService;
    @Resource
    private MeetingService meetingService;
    @Resource
    private ExportService exportService;
    @Resource
    private ExportTotalMapper exportTotalMapper;
    @Resource
    private ExportRecordMapper exportRecordMapper;


    @PostMapping("/page")
    @Operation(summary = "获得名片分页")
    public CommonResult<PageResult<CardRespVO>> getCardPage(@Valid @RequestBody CardPageReqVO pageReqVO) {
        return success(cardService.getCardPage(pageReqVO));
    }

    @PostMapping("/getCardPageByCompanyId")
    @Operation(summary = "根據公司ID获得名片分页")
    public CommonResult<PageResult<CardRespVO>> getCardPageByCompanyId(@Valid @RequestBody CardPageReqVO pageReqVO) {
        return success(cardService.getCardPageByCompanyId(pageReqVO));
    }

    @GetMapping("/getCardFieldNames")
    @Operation(summary = "获得名片所有字段名")
    public CommonResult<List<Map<String, Object>>> getCardFieldNames() {
        return success(cardService.getTableFieldNamesWithDesc());
    }

    @PostMapping("/getDistinctByGroupIdPage")
    @Operation(summary = "根据GroupId去重获得名片分页")
    public CommonResult<PageResult<Map<String, Object>>> getDistinctByGroupIdPage(@RequestBody PcmCardPageReqVO pageReqVO) {
        return success(cardService.getDistrictPage(pageReqVO));
    }

    @PostMapping("/getDistrictIds")
    @Operation(summary = "根据GroupId去重获得名片Ids")
    public CommonResult<List<Long>> getDistrictIds(@RequestBody PcmCardPageReqVO pageReqVO) {
        return success(cardService.getDistrictIds(pageReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得名片")
    @Parameter(name = "id", description = "编号", required = true, example = "10000001")
    public CommonResult<CardRespVO> getCard(@RequestParam("id") Long id) {
        return cardService.getCard(id);
    }

    @PostMapping("/create")
    @Operation(summary = "创建名片")
    public CommonResult<Long> createCard(@Valid @RequestBody CardSaveReqVO cardSaveReqVO) {
        if (cardSaveReqVO == null) {
            return CommonResult.error(400,"请求体不能为空");
        }
        return success(cardService.createCard(cardSaveReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新名片")
    public CommonResult<Boolean> updateCard(@Valid @RequestBody CardSaveReqVO cardSaveReqVO) {
        return cardService.updateCard(cardSaveReqVO);
    }

    @PutMapping("/saveAndReturnPrevious")
    @Operation(summary = "保存并编辑上一张")
    public CommonResult<Map<String, Object>> saveAndReturnPrevious(@Valid @RequestBody CardSaveReqVO cardSaveReqVO) {
        return cardService.saveAndReturnPrevious(cardSaveReqVO);
    }

    @PostMapping("/merge")
    @Operation(summary = "合并名片")
    @Parameter(name = "ids", description = "多个主键编号", required = true, example = "{'ids':[10000001,10000002]}")
    public CommonResult<Boolean> mergeCard(@RequestBody JSONObject params) {
        JSONArray arrayIds = params.getJSONArray("ids");
        if (arrayIds == null || arrayIds.isEmpty()) {
            return CommonResult.error(403,  "请选择要合并的名片");
        }
        if (arrayIds.size() < 2) {
            return CommonResult.error(403,  "至少需要选择两张名片进行合并");
        }
        if (arrayIds.size() > 10) {
            return CommonResult.error(403,  "最多只能选择10张名片进行合并");
        }

        List<Long> ids = arrayIds.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());

        cardService.mergeCards(ids);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除名片")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteCard(@RequestBody JSONObject params) {
        JSONArray jsonArray = params.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return CommonResult.error(403,  "至少需要勾选一张名片");
        }
        List<Long> ids = jsonArray.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        for (Long id : ids) {
            cardService.deleteCard(id);
            meetingService.deleteMeetingByCardId(id);
        }
        return success(true);
    }

    @PutMapping("/batchUpdateField")
    @Operation(summary = "批量设置标签、名片创建者")
    public CommonResult<Boolean> batchUpdateField(@RequestBody JSONObject params) {
        JSONArray jsonArray = params.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return CommonResult.error(403,  "至少需要勾选一张名片");
        }
        String fieldName = params.getString("fieldName");
        String fieldValue = params.getString("fieldValue");

        if (fieldName == null || fieldName.isEmpty()) {
            return CommonResult.error(403,  "请选择要更新的字段");
        }

        if (fieldValue == null || fieldValue.isEmpty()) {
            return CommonResult.error(403,  "请输入要更新的字段值");
        }

        List<Long> ids = jsonArray.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());

        cardService.batchUpdateField(ids, fieldName, fieldValue);

        return success(true);
    }

    @GetMapping("/countCardsByCreatorInDept")
    @Operation(summary = "统计创建者名片数")
    @Parameter(name = "companyId", description = "公司ID", required = true, example = "10000001")
    public CommonResult<List<Map<String, Object>>> countCardsByCreatorInDept(@RequestParam("companyId") Long companyId) {
        return success(cardService.countCardsByCreatorInDept(companyId));
    }

    @GetMapping("/getCardsByGroudId")
    @Operation(summary = "根据groupId获取名片")
    @Parameter(name = "groupId", description = "groupId", required = true)
    public CommonResult<List<Map<String, Object>>> getCardsByGroudId(@RequestParam("groupId") String groupId) {
        return success(cardService.selectByGroudId(groupId));
    }

    @GetMapping("/countCardByUserId")
    @Operation(summary = "根据用户ID统计名片数")
    public CommonResult<Long> countCardByUserId(@RequestParam("userId") Long userId) {
        return success(cardService.countCardByUserId(userId));
    }

    @PostMapping("/export-excel")
    @Operation(summary = "導出名片 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCardExcel(@Valid @RequestBody CardPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        boolean admin = SecurityFrameworkUtils.isAdmin();
        Long userId = getCurrentUserId();

        // 查詢名片數據
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<CardRespVO> pageResult = cardService.exportCardExcel(pageReqVO);
        List<CardRespVO> list = pageResult.getList();
        int exportCount = list.size();
        log.info("導出名片 Excel 總數：{}", exportCount);

        if (exportCount == 0) {
            throw ServiceExceptionUtil.exception(EXPORT_AT_LEAST_ONE_CARD_REQUIRED);
        }

        // 檢查30天內累計導出數量
        ExportTotalDO total = exportTotalMapper.findByUserId(userId);
        int currentTotal = total != null ? total.getTotalCount() : 0;

        if (!admin && currentTotal + exportCount >= 50) {
            throw ServiceExceptionUtil.exception(EXPORT_LIMIT_EXCEEDED_NEED_APPROVAL);
        }

        // 獲取名片主鍵 ID 列表
        String cardIds = list.stream()
                .map(card -> String.valueOf(card.getId()))
                .collect(Collectors.joining(","));

        // 系統自動備註
        String requesterComment = "30天內累計未達50張，系統自動通過";

        // 提交導出請求
        CommonResult<ExportRecordDO> exportResult = exportService.requestExport(userId, exportCount, requesterComment, cardIds);

        if (!exportResult.isSuccess()) {
            throw ServiceExceptionUtil.exception(new ErrorCode(exportResult.getCode(), exportResult.getMsg()));
        }

        ExportRecordDO exportRecord = exportResult.getData();

        // 檢查審批狀態
        if ("APPROVED".equals(exportRecord.getStatus())) {
            // 已通過，直接導出
            ExcelUtils.write(response, "導出名片.xls", "名片", CardRespVO.class, list);
            // 更新導出狀態為 EXPORTED
            exportRecord.setExportStatus("EXPORTED");
            exportRecordMapper.updateById(exportRecord);
        } else {
            // 待審批或拒絕，拋出異常
            throw ServiceExceptionUtil.exception(EXPORT_REQUEST_NEEDS_APPROVAL_LIMIT_REACHED);
        }

    }

    @GetMapping("/get-import-template")
    @Operation(summary = "獲得導入名片模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手動創建導出 demo
        List<CardImportExcelVO> list = CardImportExcelSampleDataUtil.generateSampleCards();
        // 輸出
        ExcelUtils.write(response, "名片導入模板.xls", "名片列表", CardImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "導入名片")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默認爲 false", example = "true")
    })
    public CommonResult<CardImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false")
                                                      Boolean updateSupport) throws Exception {

        List<CardImportExcelVO> list = ExcelUtils.readSheetFirst(file, CardImportExcelVO.class);
        return success(cardService.importCardList(list, updateSupport));
    }

    /**
     * 獲取當前使用者編號
     * @return 使用者編號
     */
    private Long getCurrentUserId() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (userId == null) {
            throw new SecurityException("未登錄");
        }
        return userId;
    }

    /**
     * 獲取當前使用者
     * @return 當前使用者名稱
     */
    private String getCurrentUser() {
        String userName = SecurityFrameworkUtils.getUserName();
        return StringUtils.isNotBlank(userName) ? userName : "SYSTEM";
    }

}
