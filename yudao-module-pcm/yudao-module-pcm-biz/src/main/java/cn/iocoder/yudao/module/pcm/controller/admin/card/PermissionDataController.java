package cn.iocoder.yudao.module.pcm.controller.admin.card;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardSaveReqVO;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

//@Tag(name = "管理後臺 - 有权查看的同事h和部门")
//@RestController
//@RequestMapping("/pcm/accessible")
//@Validated
public class PermissionDataController {

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeptApi deptApi;

    @Resource
    private CardService cardService;


//    @PostMapping("/add-user")
//    @Operation(summary = "新增同事")
//    @PreAuthorize("@ss.hasPermission('pcm:card:create')")
    public CommonResult<Boolean> createAccessibleUser(@Valid @RequestBody CardSaveReqVO cardSaveReqVO) {
        if (cardSaveReqVO == null) {
            return CommonResult.error(400,"请求体不能为空");
        }
        cardService.updateCard(cardSaveReqVO);
        return success(true);
    }


//    @PostMapping("/add-dept")
//    @Operation(summary = "新增同事")
//    @PreAuthorize("@ss.hasPermission('pcm:card:create')")
    public CommonResult<Boolean> createAccessibleDept(@Valid @RequestBody CardSaveReqVO cardSaveReqVO) {
        if (cardSaveReqVO == null) {
            return CommonResult.error(400,"请求体不能为空");
        }
        cardService.updateCard(cardSaveReqVO);
        return success(true);
    }

    /*@PostMapping("/users")
    @Operation(summary = "有权查看的同事")
    @PreAuthorize("@ss.hasPermission('pcm:card:query')")*/
    public CommonResult<JSONArray> getAccessibleUsers(@RequestBody JSONObject params) {
        JSONArray arrayIds = params.getJSONArray("userIds");
        if (arrayIds == null || arrayIds.isEmpty()) {
            return CommonResult.error(403,  "至少提供一名同事");
        }
        List<Long> userIds = arrayIds.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        // 查询当前用户有权限查看的同事
        return CommonResult.success(adminUserApi.getUserListByIds(userIds));
    }

    /*@PostMapping("/depts")
    @Operation(summary = "有权查看的部门")
    @PreAuthorize("@ss.hasPermission('pcm:card:query')")*/
    public CommonResult<JSONArray> getAccessibleDepts(@RequestBody JSONObject params) {
        JSONArray arrayIds = params.getJSONArray("deptIds");
        if (arrayIds == null || arrayIds.isEmpty()) {
            return CommonResult.error(403,  "至少提供一个部门");
        }
        List<Long> deptIds = arrayIds.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        // 查询当前用户有权限查看的部门
        return CommonResult.success(deptApi.getDeptListByIds(deptIds));
    }

}
