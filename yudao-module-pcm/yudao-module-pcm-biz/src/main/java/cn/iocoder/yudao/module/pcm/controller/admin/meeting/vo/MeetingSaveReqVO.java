package cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 商谈记录新增/修改 Request VO")
@Data
public class MeetingSaveReqVO {

    @Schema(description = "商谈记录ID", example = "10000001")
    private Long id;

    @Schema(description = "名片ID", example = "10000001")
    private Long cardId;

    @Schema(description = "商谈类型")
    private String type;

    @Schema(description = "商谈主题")
    private String title;

    @Schema(description = "商谈内容")
    private String content;

    @Schema(description = "会议场合")
    private String occasion;

    @Schema(description = "商谈时间")
    private String meetingTime;

    @Schema(description = "图片", example = "https://www.iocoder.cn")
    private String imageUrl;

    @Schema(description = "商谈人姓名")
    private String userName;

    @Schema(description = "商谈人职位")
    private String jobTitle;

    @Schema(description = "有权向查看的同事")
    private String userId;

    @Schema(description = "有权限查看的部门")
    private String deptId;

    @Schema(description = "创建人id")
    private Long createUserId;

}