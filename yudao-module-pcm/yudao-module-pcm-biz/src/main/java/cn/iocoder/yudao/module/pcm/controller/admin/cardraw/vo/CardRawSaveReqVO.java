package cn.iocoder.yudao.module.pcm.controller.admin.cardraw.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 識別名片的原始記錄新增/修改 Request VO")
@Data
public class CardRawSaveReqVO {

    @Schema(description = "名片的原始記錄ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000001")
    private Long id;

    @Schema(description = "圖片ID，對應infra_file中的ID")
    private Long imageId;

    @Schema(description = "原始識別文本")
    private String rawOcrText;

    @Schema(description = "引擎返回的結構化 JSON 結果")
    private String ocrResultJson;

    @Schema(description = "正反面標識，1:正面,0反面")
    private Boolean directionFlag;

    @Schema(description = "數據來源，tencent_ocr:騰訊ocr,ali_qwen:阿里千問")
    private String apiSource;

    @Schema(description = "創建時間")
    private LocalDateTime createTime;

    @Schema(description = "更新時間")
    private LocalDateTime updateTime;

    @Schema(description = "創建者ID")
    private Long createUserId;

    @Schema(description = "創建者")
    private String creator;

    @Schema(description = "最後更新者")
    private String updater;

    @Schema(description = "是否删除")
    private Boolean deleted;

}