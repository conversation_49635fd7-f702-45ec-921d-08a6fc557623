package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


@Schema(description = "管理后台 - 名片分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CardPageReqVO extends PageParam {

    @Schema(description = "可根据姓名、公司、职位、邮箱、手机号搜索")
    private String keyword;

    @Schema(description = "分组ID,用于合并名片")
    private String groupId;

    @Schema(description = "Title (Mr, Ms, Mrs, Others)")
    private String titleEn;

    @Schema(description = "First Name (e.g. Tai-man)")
    private String firstNameEn;

    @Schema(description = "Last Name (e.g. CHAN)")
    private String lastNameEn;

    @Schema(description = "Honour (e.g. MH, JP)")
    private String honourEn;

    @Schema(description = "稱謂（先生/ 小姐/ 女士/其他）")
    private String title;

    @Schema(description = "姓氏")
    private String firstName;

    @Schema(description = "名字")
    private String lastName;

    @Schema(description = "勳銜")
    private String honour;

    @Schema(description = "全名")
    private String fullName;

    @Schema(description = "全名数组，用于高级搜索")
    private String fullNames;

    @Schema(description = "暱稱")
    private String nickName;

    @Schema(description = "ID of Organisation")
    private Long companyIdEn;

    @Schema(description = "companyNameEn(Name of Organisation)")
    private String companyNameEn;

    @Schema(description = "Department")
    private String departmentEn;

    @Schema(description = "Job Title")
    private String jobTitleEn;

    @Schema(description = "機構ID")
    private Long companyId;

    @Schema(description = "機構名稱")
    private String companyName;

    @Schema(description = "機構名稱，用于高级搜索")
    private String companyNames;

    @Schema(description = "部門")
    private String department;

    @Schema(description = "職位")
    private String jobTitle;

    @Schema(description = "職位，用于高级搜索")
    private String jobTitles;

    @Schema(description = "流動電話")
    @ExcelProperty("流動電話")
    private String phoneMobile;

    @Schema(description = "流動電話區號")
    private String phoneMobileAreaCode;

    @Schema(description = "內地號碼")
    @ExcelProperty("內地號碼")
    private String phoneMainland;

    @Schema(description = "內地號碼區號")
    private String phoneMainlandAreaCode;

    @Schema(description = "電郵")
    private String email;

    @Schema(description = "電郵1")
    private String email1;

    @Schema(description = "電郵，用于高级搜索")
    private String emails;

    @Schema(description = "電郵2")
    private String email2;

    @Schema(description = "電話（辦公室）")
    private String phoneOffice;

    @Schema(description = "辦公電話區號")
    private String phoneOfficeAreaCode;

    @Schema(description = "電話（直線）")
    private String phoneDirectLine;

    @Schema(description = "直線電話區碼")
    private String phoneDirectLineAreaCode;

    @Schema(description = "傳真")
    @ExcelProperty("傳真")
    private String faxNumber;

    @Schema(description = "英文地址1")
    @ExcelProperty("英文地址1")
    private String addressLine1En;

    @Schema(description = "英文地址2")
    private String addressLine2En;

    @Schema(description = "英文地區(e.g. Tsim Sha Tsui)")
    private String districtEn;

    @Schema(description = "英文區域")
    private String areaEn;

    @Schema(description = "英文國家")
    private String countryEn;

    @Schema(description = "中文地址1")
    private String addressLine1;

    @Schema(description = "中文地址2")
    @ExcelProperty("中文地址2")
    private String addressLine2;

    @Schema(description = "地區")
    private String district;

    @Schema(description = "區域")
    private String area;

    @Schema(description = "國家")
    private String country;

    @Schema(description = "省")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "行業類型")
    private String industryType;

    @Schema(description = "業務類型（政府或相關組織、企業、基金會、非政府組織、其他）")
    private String businessType;

    @Schema(description = "其他業務類型")
    private String otherBusiness;

    @Schema(description = "linkedin profile url")
    private String linkedinProfileUrl;

    @Schema(description = "facebook page url")
    private String facebookPageUrl;

    @Schema(description = "instagram url")
    private String instagramUrl;

    @Schema(description = "wechat id")
    private String wechatId;

    @Schema(description = "會面詳情")
    private String communicationHistory;

    @Schema(description = "標籤")
    private String customTags;

    @Schema(description = "狀態（Active:活躍，Inactive:不活躍）")
    private String status;

    @Schema(description = "備註")
    private String notes;

    @Schema(description = "簡介")
    private String description;

    @Schema(description = "图片头像地址")
    private String imageUrl;

    @Schema(description = "反面名片的URL")
    private String backUrl;

    /**
     * 正面图片ID
     */
    private Long imageId;

    /**
     * 反面图片ID
     */
    private Long backId;

    private String address;
    private String socialMedia;

    @Schema(description = "网址")
    private String website;

    @Schema(description = "即时消息")
    private String instantMessaging;

    @Schema(description = "纪念日")
    private String anniversary;

    @Schema(description = "交换日期")
    private String exchangeDate;

    @Schema(description = "是否双面：true 为双面，false 为单面")
    private Boolean doubleSided;

    @Schema(description = "創建時間")
    private String[] createTime; // 时间范围数组

    @Schema(description = "更新時間")
    private LocalDateTime updateTime;

    @Schema(description = "創建者id")
    private Long[] createUserId;

    @Schema(description = "創建者")
    private String creator;

    @Schema(description = "最後更新者")
    private String updater;

    @Schema(description = "是否删除")
    private Boolean deleted;

    @Schema(description = "排序字段", example = "{'orderBy':'createTime|desc'}")
    private String orderBy;

    private String userId;
    private String deptId;
    private String cardType;
    private Set<Long> deptIds;

    // 用於導出
    private List<Long> ids;

}