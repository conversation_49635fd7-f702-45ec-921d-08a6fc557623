package cn.iocoder.yudao.module.pcm.controller.admin.company.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.format.DateTimeFormatter;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 公司 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CompanyRespVO {

    @Schema(description = "公司ID")
    @ExcelProperty("公司ID")
    private Long id;

    @Schema(description = "公司名称")
    @ExcelProperty("公司名称")
    private String name;

    @Schema(description = "父公司ID，0表示顶级公司")
    @ExcelProperty("父公司ID，0表示顶级公司")
    private Long parentId;

    @Schema(description = "公司地址")
    @ExcelProperty("公司地址")
    private String address;

    @Schema(description = "公司电话")
    @ExcelProperty("公司电话")
    private String phone;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private List<CompanyRespVO> children;

    private List<Map<String, Object>> cards;

}