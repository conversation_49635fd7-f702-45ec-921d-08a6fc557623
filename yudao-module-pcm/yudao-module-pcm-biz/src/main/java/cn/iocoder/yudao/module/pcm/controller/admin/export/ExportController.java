package cn.iocoder.yudao.module.pcm.controller.admin.export;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardRespVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.ExportRequestVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ApprovalPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ApproveRequestVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ExportRecordVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportRecordDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportRecordMapper;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import cn.iocoder.yudao.module.pcm.service.export.ExportRecordService;
import cn.iocoder.yudao.module.pcm.service.export.ExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.*;

/**
 * 匯出控制器，處理匯出請求及審批相關API
 */
@Tag(name = "管理後台 - 匯出審批")
@RestController
@RequestMapping("/pcm/export")
@Validated
public class ExportController {

    private static final Logger log = LoggerFactory.getLogger(ExportController.class);
    @Resource
    private ExportService exportService;
    @Resource
    private ExportRecordMapper exportRecordMapper;
    @Resource
    private CardService cardService;
    @Resource
    private ExportRecordService exportRecordService;

    /**
     * 提交導出申請
     * @param requestVO 導出申請參數
     * @return 匯出記錄編號
     */
    @PostMapping("/request")
    @Operation(summary = "提交名片導出申請")
    public CommonResult<Long> submitExportRequest(@Valid @RequestBody ExportRequestVO requestVO) {
        return exportRecordService.submitExportRequest(requestVO);
    }

    /**
     * 獲取審批列表
     * @param pageVO 分頁查詢條件
     * @return 審批記錄分頁結果
     */
    @PostMapping("/approval-list")
    @Operation(summary = "獲取審批列表")
    public CommonResult<PageResult<ExportRecordVO>> getApprovalList(@Valid @RequestBody ApprovalPageReqVO pageVO) {
        return exportRecordService.getApprovalList(pageVO);
    }

    /**
     * 獲取審批列表
     * @param pageVO 分頁查詢條件
     * @return 審批記錄分頁結果
     */
    @PostMapping("/export-list")
    @Operation(summary = "獲取導出列表")
    public CommonResult<PageResult<ExportRecordVO>> getExportList(@Valid @RequestBody ApprovalPageReqVO pageVO) {
        return exportRecordService.getExportList(pageVO);
    }

    /**
     * 審批匯出請求
     * @param approveVO 審批參數
     * @return 審批後的匯出記錄
     */
    @PostMapping("/approve")
    @Operation(summary = "審批匯出請求")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<ExportRecordVO> approveExportRequest(@Valid @RequestBody ApproveRequestVO approveVO) {
        return exportRecordService.approveExportRequest(approveVO);
    }

    /**
     * 導出已審批的名片 Excel
     * @param recordId 匯出記錄編號
     * @param guid 權限驗證唯一標識
     * @param response HTTP 響應
     * @return CommonResult，成功時導出 Excel
     * @throws IOException IO 異常
     */
    @GetMapping("/approved")
    @Operation(summary = "導出已審批的名片 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportApprovedCardExcel(@RequestParam Long recordId,
                                                      @RequestParam(required = false) String guid,
                                                      HttpServletResponse response) throws IOException {
        // 獲取當前使用者編號
        Long userId = getCurrentUserId();
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();

        // 查詢匯出記錄
        ExportRecordDO exportRecord = exportService.getExportRecord(recordId);
        if (exportRecord == null) {
            throw ServiceExceptionUtil.exception(EXPORT_RECORD_NOT_FOUND);
        }
        if (!"APPROVED".equals(exportRecord.getStatus())) {
            throw ServiceExceptionUtil.exception(EXPORT_REQUEST_NOT_APPROVED);
        }
        if (!isAdmin && !userId.equals(exportRecord.getUserId())) {
            throw ServiceExceptionUtil.exception(EXPORT_RECORD_NO_PERMISSION);
        }
        if ("EXPORTED".equals(exportRecord.getExportStatus())) {
            throw ServiceExceptionUtil.exception(EXPORT_RECORD_ALREADY_EXPORTED);
        }
        if (guid != null && !guid.equals(exportRecord.getGuid())) {
            throw ServiceExceptionUtil.exception(EXPORT_PERMISSION_VALIDATION_FAILED);
        }

        // 根據 card_ids 查詢名片數據
        List<Long> cardIds = Arrays.stream(exportRecord.getCardIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
        List<CardRespVO> list = cardService.getCardsByIds(cardIds);

        // 導出 Excel
        ExcelUtils.write(response, "導出名片.xls", "名片", CardRespVO.class, list);
        log.info("導出已審批名片 Excel 總數：{}", list.size());

        // 更新導出狀態為 EXPORTED
        exportRecord.setExportStatus("EXPORTED");
        exportRecord.setUpdater(getCurrentUser());
        exportRecordMapper.updateById(exportRecord);
    }


    /**
     * 獲取當前使用者的待審批和待導出記錄數
     * @return 待審批和待導出記錄數
     */
    @GetMapping("/todo-notification")
    @Operation(summary = "獲取待審批和待導出記錄數")
    public CommonResult<Map<String,Object>> getTodoNotification() {
        return exportRecordService.getTodoNotification();
    }

    /**
     * 檢查當前用戶是否達到導出限制（累計次數 >=50 次）
     * @return 待審批和待導出記錄數
     */
    @PostMapping("/check-restriction")
    @Operation(summary = "檢查是否達到導出限制")
    public CommonResult<Map<String,Object>> checkRestriction(@Valid CardPageReqVO pageReqVO) {
        return exportRecordService.checkRestriction(pageReqVO);
    }

    /**
     * 審批匯出請求
     * @param guid 導出記錄表的唯一值
     * @param approverComment 審批評論
     * @return
     */
    @GetMapping("/approveByEmail")
    @PermitAll
    @Operation(summary = "審批匯出請求")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> approveByEmail(
            @RequestParam @Parameter(description = "導出記錄的唯一標識符", required = true) String guid,
            @RequestParam @Parameter(description = "審批結果(true-通過, false-拒絕)", required = true) boolean isApproved,
            @RequestParam(required = false) @Parameter(description = "審批意見") String approverComment) {

        return exportRecordService.approveByEmail(guid, isApproved, approverComment);
    }


    @GetMapping("/getApproveByGuid")
    @PermitAll
    @Operation(summary = "根據GUID獲取審批請求")
    @ApiAccessLog(operateType = GET)
    public CommonResult<ExportRecordVO> getApproveByGuid(
            @RequestParam @Parameter(description = "導出記錄的唯一標識符", required = true) String guid) {
        return exportRecordService.getApproveByGuid(guid);
    }

    /**
     * 獲取當前使用者編號
     * @return 使用者編號
     */
    private Long getCurrentUserId() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (userId == null) {
            throw new SecurityException("未登錄");
        }
        return userId;
    }

    /**
     * 獲取當前使用者
     * @return 當前使用者名稱
     */
    private String getCurrentUser() {
        String userName = SecurityFrameworkUtils.getUserName();
        return StringUtils.isNotBlank(userName) ? userName : "SYSTEM";
    }

}
