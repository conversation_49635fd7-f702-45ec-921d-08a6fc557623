package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理後台 - 名片高級查詢 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PcmCardPageReqVO extends PageParam {

    @Schema(description = "可根據姓名、公司、職位、郵箱、手機號搜索")
    private String keyword;

    @Schema(description = "當前選中的名片ID")
    private Long currentCardId;

    @Schema(description = "當前選中的名片分组ID")
    private String groupId;

    private String orderBy;
    private String userId;
    private String deptId;

    private Long createUserId;

}
