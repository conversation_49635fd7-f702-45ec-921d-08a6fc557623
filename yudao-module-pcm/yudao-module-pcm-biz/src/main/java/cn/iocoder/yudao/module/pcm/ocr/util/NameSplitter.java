package cn.iocoder.yudao.module.pcm.ocr.util;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;

public class NameSplitter {

    // 百家姓：单姓和复姓（部分示例，实际可扩展）
    /*private static final String[] SURNAMES = {
            // 单姓
            "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
            "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
            // 复姓
            "欧阳", "司马", "上官", "诸葛", "南宫", "东方", "西门", "独孤", "公孙", "慕容"
    };*/
    private static final String[] SURNAMES = {
            // 单姓简体
            "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
            "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
            "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧",
            "程", "曹", "袁", "邓", "许", "傅", "沈", "曾", "彭", "吕",
            "苏", "卢", "蒋", "蔡", "贾", "丁", "魏", "薛", "叶", "阎",
            "余", "潘", "杜", "戴", "夏", "钟", "汪", "田", "任", "姜",
            "范", "方", "石", "姚", "谭", "廖", "邹", "熊", "金", "陆",
            "郝", "孔", "白", "崔", "康", "毛", "邱", "秦", "江", "史",
            "顾", "侯", "邵", "孟", "龙", "万", "段", "雷", "钱", "汤",
            "尹", "黎", "易", "常", "武", "乔", "贺", "赖", "龚", "文",

            // 单姓繁体
            "李", "王", "張", "劉", "陳", "楊", "趙", "黃", "周", "吳",
            "徐", "孫", "胡", "朱", "高", "林", "何", "郭", "馬", "羅",
            "梁", "宋", "鄭", "謝", "韓", "唐", "馮", "於", "董", "蕭",
            "程", "曹", "袁", "鄧", "許", "傅", "沈", "曾", "彭", "呂",
            "蘇", "盧", "蔣", "蔡", "賈", "丁", "魏", "薛", "葉", "閻",
            "余", "潘", "杜", "戴", "夏", "鍾", "汪", "田", "任", "姜",
            "範", "方", "石", "姚", "譚", "廖", "鄒", "熊", "金", "陸",
            "郝", "孔", "白", "崔", "康", "毛", "邱", "秦", "江", "史",
            "顧", "侯", "邵", "孟", "龍", "萬", "段", "雷", "錢", "湯",
            "尹", "黎", "易", "常", "武", "喬", "賀", "賴", "龔", "文",

            // 复姓简体
            "欧阳", "司马", "上官", "诸葛", "南宫", "东方", "西门", "独孤", "公孙", "慕容",
            "皇甫", "尉迟", "司徒", "司空", "夏侯", "宇文", "长孙", "赫连", "澹台", "公冶",
            "宗政", "濮阳", "淳于", "单于", "太叔", "申屠", "轩辕", "令狐", "钟离", "闾丘",
            "端木", "颛孙", "子车", "巫马", "公西", "漆雕", "乐正", "壤驷", "公良", "拓跋",

            // 复姓繁体
            "歐陽", "司馬", "上官", "諸葛", "南宮", "東方", "西門", "獨孤", "公孫", "慕容",
            "皇甫", "尉遲", "司徒", "司空", "夏侯", "宇文", "長孫", "赫連", "澹臺", "公冶",
            "宗政", "濮陽", "淳於", "單於", "太叔", "申屠", "軒轅", "令狐", "鍾離", "閭丘",
            "端木", "顓孫", "子車", "巫馬", "公西", "漆雕", "樂正", "壤駟", "公良", "拓跋"
    };

    /**
     * 判断是否为中文姓名
     * @param name 姓名字符串
     * @return true 表示中文姓名，false 表示非中文（假设为英文）
     */
    public static boolean isChineseName(String name) {
        if (StrUtil.isBlank(name)) {
            return false;
        }
        // 检查是否包含中文字符（Unicode 范围）
        return ReUtil.contains("[\\u4E00-\\u9FFF]", name);
    }

    /**
     * 拆分中文姓名
     * @param name 姓名
     * @return String[] {姓氏, 名字}
     */
    public static String[] splitChineseName(String name) {
        if (StrUtil.isBlank(name) || name.length() < 2) {
            return (name == null || name.trim().isEmpty()) ? new String[] {"", ""} : new String[] {name, name};
            //throw new IllegalArgumentException("中文姓名长度至少为2个字符");
        }

        String firstName; // 姓氏
        String lastName;  // 名字

        // 优先检查复姓
        for (String surname : SURNAMES) {
            if (name.startsWith(surname)) {
                firstName = surname;
                lastName = name.substring(surname.length());
                if (StrUtil.isBlank(lastName)) {
                    return new String[] {firstName, firstName};
                    //throw new IllegalArgumentException("中文姓名缺少名字部分");
                }
                return new String[]{firstName, lastName};
            }
        }

        // 未匹配单姓，假设第1字符为姓氏
        firstName = name.substring(0, 1);
        lastName = name.substring(1);
        if (StrUtil.isBlank(lastName)) {
            return new String[] {firstName, firstName};
            //throw new IllegalArgumentException("中文姓名缺少名字部分");
        }

        return new String[]{firstName, lastName};
    }

    /**
     * 拆分英文姓名（假设格式为 "名字 姓氏"）
     * @param name 姓名
     * @return String[] {姓氏, 名字}
     */
    public static String[] splitEnglishName(String name) {
        if (StrUtil.isBlank(name)) {
            return new String[] {"", ""};
            //throw new IllegalArgumentException("英文姓名不能为空");
        }

        // 按空格拆分，去除多余空格
        String[] parts = StrUtil.trim(name).split("\\s+");
        if (parts.length < 2) {
            return new String[]{parts[0], parts[0]};
            //throw new IllegalArgumentException("英文姓名需包含名字和姓氏（如 'John Smith'）");
        }

        String lastName = parts[0];  // 名字
        String firstName = parts[1]; // 姓氏

        return new String[]{firstName, lastName};
    }

    /**
     * 拆分姓名（主方法）
     * @param fullName 完整姓名
     * @return String[] {姓氏, 名字}
     */
    public static String[] splitName(String fullName) {
        if (StrUtil.isBlank(fullName)) {
            return null;
//            throw new IllegalArgumentException("姓名不能为空");
        }

        if (isChineseName(fullName)) {
            return splitChineseName(fullName);
        } else {
            return splitEnglishName(fullName);
        }
    }

    public static void main(String[] args) {
        // 测试用例
        String[] names = {
                "欧阳修",         // 中文复姓
                "John Smith",     // 英文姓名
                "Mary Jane Watson" // 英文多词姓名
        };

        for (String name : names) {
            try {
                String[] result = splitName(name);
                System.out.println("姓名: " + name);
                System.out.println("姓氏: " + result[0]);
                System.out.println("名字: " + result[1]);
                System.out.println();
            } catch (IllegalArgumentException e) {
                System.out.println("错误: " + e.getMessage());
            }
        }

        System.out.println("---> " + ObjUtil.defaultIfNull(null, CommonStatusEnum.ENABLE.getStatus()));
    }

}
