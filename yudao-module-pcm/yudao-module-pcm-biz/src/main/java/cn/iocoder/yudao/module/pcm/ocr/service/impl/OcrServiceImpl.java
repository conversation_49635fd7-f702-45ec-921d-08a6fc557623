package cn.iocoder.yudao.module.pcm.ocr.service.impl;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardSaveReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.cardraw.vo.CardRawSaveReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.company.vo.CompanySaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.company.CompanyDO;
import cn.iocoder.yudao.module.pcm.ocr.client.QwenOcrClient;
import cn.iocoder.yudao.module.pcm.ocr.client.TencentOcrClient;
import cn.iocoder.yudao.module.pcm.ocr.exception.CardRecognitionException;
import cn.iocoder.yudao.module.pcm.ocr.service.OcrService;
import cn.iocoder.yudao.module.pcm.ocr.util.NameSplitter;
import cn.iocoder.yudao.module.pcm.ocr.util.OcrUtils;
import cn.iocoder.yudao.module.pcm.ocr.util.PhoneNumberUtils;
import cn.iocoder.yudao.module.pcm.ocr.vo.ProcessedCard;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import cn.iocoder.yudao.module.pcm.service.cardraw.CardRawService;
import cn.iocoder.yudao.module.pcm.service.company.CompanyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardInfo;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class OcrServiceImpl implements OcrService {

    private static final Logger log = LoggerFactory.getLogger(OcrServiceImpl.class);
    @Resource
    private TencentOcrClient tencentOcrClient;
    @Resource
    private CardService cardService;
    @Resource
    private CompanyService companyService;
    @Resource
    private FileApi fileApi;
    @Resource
    private CardRawService cardRawService;
    @Resource
    private ConfigApi configApi;

    @Override
    public BusinessCardOCRResponse recognizeBusinessCard(String imagePath) {
        try {
            String imageBase64 = OcrUtils.imageToBase64(imagePath);
            BusinessCardOCRRequest req = new BusinessCardOCRRequest();
            req.setImageBase64(imageBase64);
            return tencentOcrClient.businessCardOcr(req);
        } catch (TencentCloudSDKException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public BusinessCardOCRResponse recognizeBusinessCard(byte[] imageData) {
        try {
            String imageBase64 = OcrUtils.imageToBase64(imageData);
            BusinessCardOCRRequest req = new BusinessCardOCRRequest();
            req.setImageBase64(imageBase64);
            /*String config = " {\"RetImageType\":\"PROPROCESS\"}";
            req.setConfig(config);*/
            return tencentOcrClient.businessCardOcr(req);
        } catch (TencentCloudSDKException e) {
            log.error("腾讯OCR接口识别失败 : {}", e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public ProcessedCard recognitionDoubleSideCard(Long frontFileId, Long backFileId) {
        try {
            // 初始化名片請求對象
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            CardSaveReqVO cardSaveReqVO = new CardSaveReqVO();
            cardSaveReqVO.setUserId(String.valueOf(loginUserId));
            cardSaveReqVO.setCreateUserId(loginUserId);
            cardSaveReqVO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            cardSaveReqVO.setDoubleSided(false);

            boolean frontSuccess = false;
            boolean backSuccess = false;
            Map<String, List<String>> frontMap = null;
            Map<String, List<String>> backMap = null;

            // 處理正面
            if (frontFileId != null) {
                try {
                    // 設置正面信息
                    cardSaveReqVO.setImageId(frontFileId);
                    JSONObject frontFileDO = fileApi.getFileDOById(frontFileId);
                    if (frontFileDO == null) {
                        log.info("正面檔案 ID 不存在，繼續處理反面，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                    } else {
                        byte[] frontBytes = fileApi.getFileById(frontFileId);
                        String frontUrl = frontFileDO.getString("url");
                        cardSaveReqVO.setImageUrl(frontUrl);
                        // 識別正面
                        BusinessCardOCRResponse frontResp = recognizeBusinessCard(frontBytes);
                        log.info("正面識別原始結果: {}", JSONObject.toJSON(frontResp));
                        if (frontResp == null) {
                            log.error("正面識別失敗，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                            throw new CardRecognitionException(frontFileId, backFileId, "FRONT", "正面識別失敗");
                        }

                        frontMap = getCardInfo(frontResp);
                        log.info("正面識別格式化后結果: {}", JSONObject.toJSON(frontMap));
                        if (frontMap == null || frontMap.isEmpty()) {
                            log.error("正面識別失敗，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                            throw new CardRecognitionException(frontFileId, backFileId, "FRONT", "正面識別失敗");
                        }

                        // 保存原始信息
                        this.saveCardRaw(frontFileId, frontResp, frontMap, true);
                        populateCardSaveReqVO(cardSaveReqVO, frontMap, true);
                        frontSuccess = true;
                    }
                } catch (CardRecognitionException e) {
                    log.info("正面識別失敗，繼續處理反面，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                }
            } else {
                log.info("正面檔案 ID 為空，繼續處理反面，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
            }

            // 處理反面
            if (backFileId != null) {
                try {
                    // 設置反面專屬字段
                    cardSaveReqVO.setBackId(backFileId);

                    JSONObject backFileDO = fileApi.getFileDOById(backFileId);
                    if (backFileDO == null) {
                        log.info("反面檔案 ID {} 不存在", backFileId);
                    } else {
                        String backUrl = backFileDO.getString("url");
                        cardSaveReqVO.setBackUrl(backUrl);
                        if (backFileDO.isEmpty() || !backFileDO.containsKey("url") || StringUtils.isBlank(backUrl)) {
                            log.info("反面檔案 backFileDO 元數據無效: {}", backFileDO);
                        } else {
                            byte[] backBytes = fileApi.getFileById(backFileId);
                            if (backBytes == null || backBytes.length == 0) {
                                log.info("反面文件內容為空或無法獲取: {}", backFileId);
                            } else {
                                BusinessCardOCRResponse backResp = recognizeBusinessCard(backBytes);
                                if (backResp == null) {
                                    log.info("反面識別失敗，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                                } else {
                                    backMap = getCardInfo(backResp);
                                    log.info("反面識別結果: {}", backMap);
                                    if (backMap != null && !backMap.isEmpty()) {
                                        cardSaveReqVO.setDoubleSided(true);
                                        populateCardSaveReqVO(cardSaveReqVO, backMap, false);
                                        backSuccess = true;
                                    } else {
                                        log.info("反面識別結果轉換為空，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                                    }
                                }

                                // 保存原始信息
                                this.saveCardRaw(backFileId, backResp, backMap, false);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.info("反面識別失敗，frontFileId: {}, backFileId: {}, 錯誤: {}", frontFileId, backFileId, e.getMessage());
                }
            }

            // 根據正反面結果決定返回狀態
            if (frontSuccess || backSuccess) {
                // 檢查名片唯一性
                CardPageReqVO cardPageReqVO = BeanUtils.toBean(cardSaveReqVO, CardPageReqVO.class);
                Long duplicateCardId = cardService.checkCardUnique(cardPageReqVO);
                if (duplicateCardId != null) {
                    return new ProcessedCard(frontFileId, backFileId, duplicateCardId, "DUPLICATE", "您已上傳了該名片，請勿重複上傳");
                }

                // 保存名片
                Long cardId = cardService.createCard(cardSaveReqVO);
                if (frontSuccess && (backFileId == null || backSuccess)) {
                    return new ProcessedCard(frontFileId, backFileId, cardId, "SUCCESS", "名片識別成功");
                } else if (frontSuccess) {
                    return new ProcessedCard(frontFileId, backFileId, cardId, "BACK_FAILED", "正面識別成功，反面識別失敗");
                } else {
                    return new ProcessedCard(frontFileId, backFileId, cardId, "FRONT_FAILED", "反面識別成功，正面識別失敗");
                }
            }

            return new ProcessedCard(frontFileId, backFileId, null, "BOTH_FAILED", "雙面識別失敗");

        } catch (Exception e) {
            log.error("名片識別失敗，frontFileId: {}, backFileId: {}, 錯誤: {}", frontFileId, backFileId, e.getMessage());
            return new ProcessedCard(frontFileId, backFileId, null, "ERROR", e.getMessage());
        }
    }

    /**
     * 填充 CardSaveReqVO 实体
     * @param cardSaveReqVO 名片请求对象
     * @param cardMap 名片信息映射
     * @param isFront 是否为正面（正面优先级高于反面）
     */
    private void populateCardSaveReqVO(CardSaveReqVO cardSaveReqVO, Map<String, List<String>> cardMap, boolean isFront) {
        // 定义分隔符（仅用于非电话字段）
        final String DELIMITER = " ";

        String orgName = cardMap.getOrDefault("公司", Collections.emptyList()).stream().findFirst().orElse("");
        String jobTitle = cardMap.getOrDefault("职位", Collections.emptyList()).stream().findFirst().orElse("");
        String[] fixed = fixOrgAndJob(orgName, jobTitle);
        orgName = fixed[0];
        jobTitle = fixed[1];

        // 处理公司信息
        if (StringUtils.isNotBlank(orgName)) {
            String companyName = cardMap.getOrDefault("公司名称", Collections.emptyList()).stream().findFirst().orElse("");
            Long companyId = saveCompany(orgName, "zh", companyName);
            if (isFront || cardSaveReqVO.getCompanyId() == null) {
                cardSaveReqVO.setCompanyId(companyId);
                cardSaveReqVO.setCompanyName(orgName);
            }
        }

        // 处理英文公司名称（合并多个英文公司名称）
        List<String> orgNamesEn = cardMap.getOrDefault("英文公司", Collections.emptyList());
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getCompanyNameEn())) {
            cardSaveReqVO.setCompanyNameEn(String.join(DELIMITER, orgNamesEn));
            if (!orgNamesEn.isEmpty()) {
                String companyName = cardMap.getOrDefault("公司名称", Collections.emptyList()).stream().findFirst().orElse("");
                Long companyIdEn = saveCompany(orgNamesEn.get(0), "en", companyName);
                cardSaveReqVO.setCompanyIdEn(String.valueOf(companyIdEn));
            }
        } else {
            List<String> currentOrgNamesEn = StringUtils.isBlank(cardSaveReqVO.getCompanyNameEn())
                    ? new ArrayList<>()
                    : new ArrayList<>(Arrays.asList(cardSaveReqVO.getCompanyNameEn().split("\\" + DELIMITER)));
            currentOrgNamesEn.addAll(orgNamesEn);
            cardSaveReqVO.setCompanyNameEn(String.join(DELIMITER, currentOrgNamesEn));
        }

        // 处理姓名
        String zhName = cardMap.getOrDefault("姓名", Collections.emptyList()).stream().findFirst().orElse("");
        if (StringUtils.isNotBlank(zhName)) {
            String[] zhNames = NameSplitter.splitName(zhName);
            if (zhNames != null && zhNames.length > 1) {
                if (NameSplitter.isChineseName(zhName)) {
                    if (isFront || StringUtils.isBlank(cardSaveReqVO.getFirstName())) {
                        cardSaveReqVO.setFirstName(zhNames[0]);
                        cardSaveReqVO.setLastName(zhNames[1]);
                        cardSaveReqVO.setFullName(zhName);
                    }
                } else {
                    if (isFront || StringUtils.isBlank(cardSaveReqVO.getFirstNameEn())) {
                        cardSaveReqVO.setFirstNameEn(zhNames[0]);
                        cardSaveReqVO.setLastNameEn(zhNames[1]);
                    }
                }
            } else if (isFront || StringUtils.isBlank(cardSaveReqVO.getFullName())) {
                cardSaveReqVO.setFullName(zhName);
            }
        }

        // 处理英文姓名
        String enName = cardMap.getOrDefault("英文姓名", Collections.emptyList()).stream().findFirst().orElse("");
        if (StringUtils.isNotBlank(enName)) {
            String[] enNames = NameSplitter.splitName(enName);
            if (enNames != null && enNames.length > 1) {
                if (NameSplitter.isChineseName(enName)) {
                    if (isFront || StringUtils.isBlank(cardSaveReqVO.getFirstName())) {
                        cardSaveReqVO.setFirstName(enNames[0]);
                        cardSaveReqVO.setLastName(enNames[1]);
                        cardSaveReqVO.setFullName(enName);
                    }
                } else {
                    if (isFront || StringUtils.isBlank(cardSaveReqVO.getFirstNameEn())) {
                        cardSaveReqVO.setFirstNameEn(enNames[0]);
                        cardSaveReqVO.setLastNameEn(enNames[1]);
                    }
                }
            } else if (isFront || StringUtils.isBlank(cardSaveReqVO.getFullName())) {
                cardSaveReqVO.setFullName(enName);
            }
        }

        // 处理职位
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getJobTitle())) {
            cardSaveReqVO.setJobTitle(jobTitle);
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getJobTitleEn())) {
            cardSaveReqVO.setJobTitleEn(cardMap.getOrDefault("英文职位", Collections.emptyList()).stream().findFirst().orElse(""));
        }

        // 处理多值字段：地址
        List<String> addresses = cardMap.getOrDefault("地址", Collections.emptyList());
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getAddress())) {
            cardSaveReqVO.setAddress(String.join(DELIMITER, addresses));
            // 设置 address_line1 和 address_line2
            if (!addresses.isEmpty()) {
                cardSaveReqVO.setAddressLine1(addresses.get(0));
                if (addresses.size() > 1) {
                    cardSaveReqVO.setAddressLine2(addresses.get(1));
                }
            }
        } else {
            List<String> currentAddresses = StringUtils.isBlank(cardSaveReqVO.getAddress())
                    ? new ArrayList<>()
                    : new ArrayList<>(Arrays.asList(cardSaveReqVO.getAddress().split("\\" + DELIMITER)));
            currentAddresses.addAll(addresses);
            cardSaveReqVO.setAddress(String.join(DELIMITER, currentAddresses));
            // 更新 address_line1 和 address_line2
            if (!currentAddresses.isEmpty()) {
                cardSaveReqVO.setAddressLine1(currentAddresses.get(0));
                if (currentAddresses.size() > 1) {
                    cardSaveReqVO.setAddressLine2(currentAddresses.get(1));
                }
            }
        }
        // 处理多值字段：英文地址
        List<String> addressesEn = cardMap.getOrDefault("英文地址", Collections.emptyList());
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getAddressLine1En())) {
            cardSaveReqVO.setAddressLine1En(String.join(DELIMITER, addressesEn));
            // 设置 address_line1_en 和 address_line2_en
            if (!addressesEn.isEmpty()) {
                cardSaveReqVO.setAddressLine1En(addressesEn.get(0));
                if (addressesEn.size() > 1) {
                    cardSaveReqVO.setAddressLine2En(addressesEn.get(1));
                }
            }
        } else {
            List<String> currentAddressesEn = StringUtils.isBlank(cardSaveReqVO.getAddressLine1En())
                    ? new ArrayList<>()
                    : new ArrayList<>(Arrays.asList(cardSaveReqVO.getAddressLine1En().split("\\" + DELIMITER)));
            currentAddressesEn.addAll(addressesEn);
            cardSaveReqVO.setAddressLine1En(String.join(DELIMITER, currentAddressesEn));
            // 更新 address_line1_en 和 address_line2_en
            if (!currentAddressesEn.isEmpty()) {
                cardSaveReqVO.setAddressLine1En(currentAddressesEn.get(0));
                if (currentAddressesEn.size() > 1) {
                    cardSaveReqVO.setAddressLine2En(currentAddressesEn.get(1));
                }
            }
        }

        //处理各种号码
        List<String> telephones = cardMap.getOrDefault("电话", Collections.emptyList());
        String mobilePhone = cardMap.getOrDefault("手机", Collections.emptyList()).stream().findFirst().orElse("");
        String[] mobilePhones = PhoneNumberUtils.extractAreaCodeAndNumber(mobilePhone);

        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneMobile())) {
            if (telephones != null && !telephones.isEmpty()) {
                String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(telephones.get(0));
                cardSaveReqVO.setPhoneMobileAreaCode(phones[0]);
                cardSaveReqVO.setPhoneMobile(phones[1]);
            }
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneMainland())) {
            if (!mobilePhones[0].contains("852")) {
                cardSaveReqVO.setPhoneMainlandAreaCode(mobilePhones[0]);
                cardSaveReqVO.setPhoneMainland(mobilePhones[1]);
            }
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneOffice())) {
            String phone = cardMap.getOrDefault("办公电话", Collections.emptyList()).stream().findFirst().orElse("");
            phone = phone.isEmpty() && telephones.size() >= 2 ? telephones.get(1) : phone;
            String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(phone);
            cardSaveReqVO.setPhoneOfficeAreaCode(phones[0]);
            cardSaveReqVO.setPhoneOffice(phones[1]);
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneDirectLine())) {
            String phone = cardMap.getOrDefault("直线电话", Collections.emptyList()).stream().findFirst().orElse("");
            phone = phone.isEmpty() && telephones.size() >= 3 ? telephones.get(2) : phone;
            String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(phone);
            if (StringUtils.isBlank(phone)) {
                phones = mobilePhones;
            }
            cardSaveReqVO.setPhoneDirectLineAreaCode(phones[0]);
            cardSaveReqVO.setPhoneDirectLine(phones[1]);
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getFaxNumber())) {
            String faxNumber = cardMap.getOrDefault("传真", Collections.emptyList()).stream().findFirst().orElse("");
            faxNumber = faxNumber.isEmpty() && telephones.size() >= 4 ? telephones.get(3) : faxNumber;
            cardSaveReqVO.setFaxNumber(faxNumber);
        }

        // 处理邮箱
        List<String> emails = cardMap.getOrDefault("邮箱", Collections.emptyList());
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getEmail())) {
            cardSaveReqVO.setEmail(emails.stream().findFirst().orElse(""));
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getEmail2())) {
            if (emails.size() > 1) {
                cardSaveReqVO.setEmail2(emails.get(1));
            }
        }

        // 处理网址
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getWebsite())) {
            String website = cardMap.getOrDefault("网址", Collections.emptyList()).stream().findFirst().orElse("");
            if (StringUtils.isBlank(website)) {
                website = cardMap.getOrDefault("网站", Collections.emptyList()).stream().findFirst().orElse("");
            }
            cardSaveReqVO.setWebsite(website);
        }

        // 处理社交媒体
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getSocialMedia())) {
            cardSaveReqVO.setSocialMedia(cardMap.getOrDefault("社交媒体", Collections.emptyList()).stream().findFirst().orElse(""));
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getSocialMedia())) {
            List<String> socials = cardMap.getOrDefault("社交媒体", Collections.emptyList());
            cardSaveReqVO.setSocialMedia(String.join(DELIMITER, socials));
            // 提取 LinkedIn、Facebook 等
            for (String social : socials) {
                if (social.contains("linkedin.com")) {
                    cardSaveReqVO.setLinkedinProfileUrl(social);
                } else if (social.contains("facebook.com")) {
                    cardSaveReqVO.setFacebookPageUrl(social);
                } else if (social.contains("instagram.com")) {
                    cardSaveReqVO.setInstagramUrl(social);
                }
            }
        }

    }

    /**
     * 千問识别双面名片
     * @param frontFileId 前面文件ID
     * @param backFileId  后面文件ID
     * @return 识别结果
     */
    public ProcessedCard recognitionDoubleSideCardQwen(Long frontFileId, Long backFileId) {
        try {
            // 初始化名片請求對象
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            CardSaveReqVO cardSaveReqVO = new CardSaveReqVO();
            cardSaveReqVO.setCreateUserId(loginUserId);
            cardSaveReqVO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            cardSaveReqVO.setDoubleSided(false);

            boolean frontSuccess = false;
            boolean backSuccess = false;
            Map<String, String> frontMap = null;
            Map<String, String> backMap = null;

            String apiKey = configApi.getConfigValueByKey("qwen.api.key");
            String modelName = configApi.getConfigValueByKey("qwen.model.name");

            // 處理正面
            if (frontFileId != null) {
                try {
                    // 設置正面信息
                    cardSaveReqVO.setImageId(frontFileId);
                    JSONObject frontFileDO = fileApi.getFileDOById(frontFileId);
                     String fileName = frontFileDO.getString("name");
                    if (frontFileDO == null) {
                        log.info("正面檔案 ID 不存在，繼續處理反面，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                    } else {
                        byte[] frontBytes = fileApi.getFileById(frontFileId);
                        String frontUrl = frontFileDO.getString("url");
                        cardSaveReqVO.setImageUrl(frontUrl);
                        // 識別正面
                        frontMap = QwenOcrClient.multiModalConversationCall(apiKey, modelName, fileName, frontBytes);
                        log.info("正面識別格式化后結果: {}", JSONObject.toJSON(frontMap));
                        if (frontMap == null || frontMap.isEmpty()) {
                            log.error("正面識別失敗，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                            throw new CardRecognitionException(frontFileId, backFileId, "FRONT", "正面識別失敗");
                        }

                        populateCardQwen(cardSaveReqVO, frontMap, true);
                        // 保存原始信息
                        this.saveCardRawQwen(frontFileId, frontMap, true);
                        frontSuccess = true;
                    }
                } catch (CardRecognitionException e) {
                    log.info("正面識別失敗，繼續處理反面，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                }
            } else {
                log.info("正面檔案 ID 為空，繼續處理反面，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
            }

            // 處理反面
            if (backFileId != null) {
                try {
                    // 設置反面專屬字段
                    cardSaveReqVO.setBackId(backFileId);

                    JSONObject backFileDO = fileApi.getFileDOById(backFileId);
                    if (backFileDO == null) {
                        log.info("反面檔案 ID {} 不存在", backFileId);
                    } else {
                        String backUrl = backFileDO.getString("url");
                        cardSaveReqVO.setBackUrl(backUrl);
                        String fileName = backFileDO.getString("name");
                        if (backFileDO.isEmpty() || !backFileDO.containsKey("url") || StringUtils.isBlank(backUrl)) {
                            log.info("反面檔案 backFileDO 元數據無效: {}", backFileDO);
                        } else {
                            byte[] backBytes = fileApi.getFileById(backFileId);
                            if (backBytes == null || backBytes.length == 0) {
                                log.info("反面文件內容為空或無法獲取: {}", backFileId);
                            } else {
                                backMap = QwenOcrClient.multiModalConversationCall(apiKey, modelName, fileName, backBytes);
                                if (backMap != null && !backMap.isEmpty()) {
                                    cardSaveReqVO.setDoubleSided(true);
                                    populateCardQwen(cardSaveReqVO, backMap, false);
                                    // 保存原始信息
                                    this.saveCardRawQwen(backFileId, backMap, false);
                                    backSuccess = true;
                                } else {
                                    log.info("反面識別結果轉換為空，frontFileId: {}, backFileId: {}", frontFileId, backFileId);
                                }

                            }
                        }
                    }
                } catch (Exception e) {
                    log.info("反面識別失敗，frontFileId: {}, backFileId: {}, 錯誤: {}", frontFileId, backFileId, e.getMessage());
                }
            }

            // 根據正反面結果決定返回狀態
            if (frontSuccess || backSuccess) {
                CardPageReqVO cardPageReqVO = BeanUtils.toBean(cardSaveReqVO, CardPageReqVO.class);
                Long duplicateId = cardService.checkCardUnique(cardPageReqVO); // 檢查名片唯一性
                if (duplicateId != null) {
                    cardSaveReqVO.setDeleted(true);
                    Long cardId = cardService.createCard(cardSaveReqVO);
                    return new ProcessedCard(frontFileId, backFileId, cardId, "DUPLICATE", "相同姓名的卡片已存在 如有需要請點擊以下查看", duplicateId);
                }

                // 保存名片
                Long cardId = cardService.createCard(cardSaveReqVO);
                if (frontSuccess && (backFileId == null || backSuccess)) {
                    return new ProcessedCard(frontFileId, backFileId, cardId, "SUCCESS", "名片識別成功");
                } else if (frontSuccess) {
                    return new ProcessedCard(frontFileId, backFileId, cardId, "BACK_FAILED", "正面識別成功，反面識別失敗");
                } else {
                    return new ProcessedCard(frontFileId, backFileId, cardId, "FRONT_FAILED", "反面識別成功，正面識別失敗");
                }
            }

            return new ProcessedCard(frontFileId, backFileId, null, "BOTH_FAILED", "雙面識別失敗");

        } catch (Exception e) {
            log.error("名片識別失敗，frontFileId: {}, backFileId: {}, 錯誤: {}", frontFileId, backFileId, e.getMessage());
            return new ProcessedCard(frontFileId, backFileId, null, "ERROR", e.getMessage());
        }
    }

    /**
     * 填充 CardSaveReqVO 实体
     * @param cardSaveReqVO 名片请求对象
     * @param cardMap 名片信息映射
     * @param isFront 是否为正面（正面优先级高于反面）
     */
    private void populateCardQwen(CardSaveReqVO cardSaveReqVO, Map<String, String> cardMap, boolean isFront) {

        BeanUtils.copyProperties(cardMap, cardSaveReqVO);
        String firstName = cardMap.getOrDefault("firstName", "");
        String lastName = cardMap.getOrDefault("lastName", "");
        String phoneOffice = cardMap.getOrDefault("phoneOffice", "");
        String phoneMobile = cardMap.getOrDefault("phoneMobile", "");
        String phoneMainland = cardMap.getOrDefault("phoneMainland", "");
        String phoneDirect = cardMap.getOrDefault("phoneDirect", "");
        String email = cardMap.getOrDefault("email", "");
        String jobTitle = cardMap.getOrDefault("jobTitle", "");
        String orgName = cardMap.getOrDefault("companyName", "");
        String address = cardMap.getOrDefault("address", "");
        String faxNumber = cardMap.getOrDefault("faxNumber", "");
        String website = cardMap.getOrDefault("website", "");
        String firstNameEn = cardMap.getOrDefault("firstNameEn", "");
        String lastNameEn = cardMap.getOrDefault("lastNameEn", "");
        String jobTitleEn = cardMap.getOrDefault("jobTitleEn", "");
        String orgNamesEn = cardMap.getOrDefault("companyNameEn", "");
        String addressEn = cardMap.getOrDefault("addressEn", "");
        String socialMedia = cardMap.getOrDefault("socialMedia", "");
        String honour = cardMap.getOrDefault("honour", "");
        String honourEn = cardMap.getOrDefault("honourEn", "");

        // 处理公司信息
        if (StringUtils.isNotBlank(orgName)) {
            Long companyId = saveCompany(orgName, "zh", cardMap.getOrDefault("companyName", ""));
            if (isFront || cardSaveReqVO.getCompanyId() == null) {
                cardSaveReqVO.setCompanyId(companyId);
                cardSaveReqVO.setCompanyName(orgName);
            }
        }

        if (isFront || StringUtils.isBlank(cardSaveReqVO.getCompanyNameEn())) {
            if (!orgNamesEn.isEmpty()) {
                Long companyIdEn = saveCompany(orgNamesEn, "en", cardMap.getOrDefault("companyNameEn", ""));
                cardSaveReqVO.setCompanyIdEn(String.valueOf(companyIdEn));
                cardSaveReqVO.setCompanyNameEn(orgNamesEn);
            }
        }

        // 处理中文姓名
        if (isFront || (StringUtils.isBlank(cardSaveReqVO.getFirstName())) || StringUtils.isBlank(cardSaveReqVO.getLastName())) {
            cardSaveReqVO.setFirstName(firstName);
            cardSaveReqVO.setLastName(lastName);
            cardSaveReqVO.setFullName(firstName + lastName);
        }

        // 处理英文姓名
        if (isFront || (StringUtils.isBlank(cardSaveReqVO.getFirstNameEn()) || StringUtils.isBlank(cardSaveReqVO.getLastNameEn()))) {
            cardSaveReqVO.setFirstNameEn(firstNameEn);
            cardSaveReqVO.setLastNameEn(lastNameEn);
            if (StringUtils.isBlank(cardSaveReqVO.getFullName())) {
                cardSaveReqVO.setFullName(firstNameEn + lastNameEn);
            }
        }

        // 处理职位
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getJobTitle())) {
            cardSaveReqVO.setJobTitle(jobTitle);
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getJobTitleEn())) {
            cardSaveReqVO.setJobTitleEn(jobTitleEn);
        }

        if (isFront || StringUtils.isBlank(cardSaveReqVO.getAddress())) {
            cardSaveReqVO.setAddress(address);
            cardSaveReqVO.setAddressLine1(address);
        }

        if (isFront || StringUtils.isBlank(cardSaveReqVO.getAddressLine1En())) {
            cardSaveReqVO.setAddressLine1(addressEn);
        }
        //处理流動電話
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneMobile())) {
            String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(phoneMobile);
            cardSaveReqVO.setPhoneMobileAreaCode(phones[0]);
            cardSaveReqVO.setPhoneMobile(phones[1]);
        }
        //处理内地號碼
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneMainland())) {
            String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(phoneMainland);
            cardSaveReqVO.setPhoneMainlandAreaCode(phones[0]);
            cardSaveReqVO.setPhoneMainland(phones[1]);
        }
        //处理辦公電話
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneOffice())) {
            String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(phoneOffice);
            cardSaveReqVO.setPhoneOfficeAreaCode(phones[0]);
            cardSaveReqVO.setPhoneOffice(phones[1]);
        }
        //处理直线電話
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getPhoneDirectLine())) {
            String[] phones = PhoneNumberUtils.extractAreaCodeAndNumber(phoneDirect);
            cardSaveReqVO.setPhoneDirectLineAreaCode(phones[0]);
            cardSaveReqVO.setPhoneDirectLine(phones[1]);
        }
        //处理傳真
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getFaxNumber())) {
            cardSaveReqVO.setFaxNumber(faxNumber);
        }

        // 处理邮箱
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getEmail())) {
            cardSaveReqVO.setEmail(email);
        }

        // 处理网址
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getWebsite())) {
            cardSaveReqVO.setWebsite(website);
        }

        // 处理社交媒体
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getSocialMedia())) {
            cardSaveReqVO.setSocialMedia(socialMedia);
        }

        // 处理勳銜
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getHonour())) {
            cardSaveReqVO.setHonour(honour);
        }
        if (isFront || StringUtils.isBlank(cardSaveReqVO.getHonourEn())) {
            cardSaveReqVO.setHonourEn(honourEn);
        }

        /*if (isFront || StringUtils.isBlank(cardSaveReqVO.getSocialMedia())) {
            List<String> socials = cardMap.getOrDefault("社交媒体", "");
            cardSaveReqVO.setSocialMedia(String.join(DELIMITER, socials));
            // 提取 LinkedIn、Facebook 等
            for (String social : socials) {
                if (social.contains("linkedin.com")) {
                    cardSaveReqVO.setLinkedinProfileUrl(social);
                } else if (social.contains("facebook.com")) {
                    cardSaveReqVO.setFacebookPageUrl(social);
                } else if (social.contains("instagram.com")) {
                    cardSaveReqVO.setInstagramUrl(social);
                }
            }
        }*/

    }

    private JSONObject reUpload(byte[] bytes, String fileName) {
        String frontName = OcrUtils.renameFile(fileName);
        return fileApi.pcmCreateFile(frontName, frontName, bytes);
    }

    @Override
    public Long singleSaveCard(BusinessCardOCRResponse resp, String url) {
        Map<String, List<String>> data = getCardInfo(resp);
        CardSaveReqVO cardSaveReqVO = new CardSaveReqVO();
        populateCardSaveReqVO(cardSaveReqVO, data, true);

        cardSaveReqVO.setImageUrl(url);
        cardSaveReqVO.setUserId(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
        cardSaveReqVO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());

        CardPageReqVO cardPageReqVO = BeanUtils.toBean(cardSaveReqVO, CardPageReqVO.class);
        List<CardDO> cardList = cardService.selectUniqueCard(cardPageReqVO);
        if (cardList != null && !cardList.isEmpty()) {
            CardDO cardDO = cardList.get(0);
            return cardDO.getId();
        }

        return cardService.createCard(cardSaveReqVO);
    }

    private Map<String, List<String>> getCardInfo(BusinessCardOCRResponse resp) {
        Map<String, List<String>> collect = Optional.ofNullable(resp.getBusinessCardInfos())
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                .collect(Collectors.groupingBy(
                        BusinessCardInfo::getName,
                        Collectors.mapping(BusinessCardInfo::getValue, Collectors.toList())
                ));
        return collect;
    }

    private Long saveCompany(String orgName, String languageType, String companyName) {
        if (StringUtils.isBlank(orgName)) {
            orgName = companyName;
        }

        Long companyId = 0L;
        if (StringUtils.isNotBlank(orgName)) {
            CompanyDO companyDO = companyService.getCompanyByName(orgName);
            if (companyDO == null) {
                companyDO = new CompanyDO();
                companyDO.setName(orgName);
                companyDO.setLanguageType(languageType);
                companyDO.setParentId(0L);
                companyDO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
                companyId = companyService.createCompany(BeanUtils.toBean(companyDO, CompanySaveReqVO.class));
            } else {
                companyId = companyDO.getId();
            }
        }
        return companyId;
    }

    /**
     * 保存名片原始数据
     * @param imageId 圖片ID
     * @param frontResp 原始識別文本
     * @param cardMap 引擎返回的結構化 JSON 結果
     * @param isFront 正反面標識，true:正面, false反面
     */
    private void saveCardRaw(Long imageId, BusinessCardOCRResponse frontResp,
                             Map<String, List<String>> cardMap, boolean isFront) {
        CardRawSaveReqVO cardRawVO = new CardRawSaveReqVO();
        cardRawVO.setImageId(imageId);
        cardRawVO.setRawOcrText(JSON.toJSONString(frontResp));
        cardRawVO.setOcrResultJson(JSON.toJSONString(cardMap));
        cardRawVO.setDirectionFlag(isFront);
        cardRawVO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        cardRawVO.setCreateTime(LocalDateTime.now());
        cardRawVO.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        cardRawVO.setUpdateTime(LocalDateTime.now());
        cardRawVO.setDeleted(false);
        cardRawService.createCard(cardRawVO);
    }

    /**
     * 保存名片原始数据
     * @param imageId 圖片ID
     * @param cardMap 引擎返回的結構化 JSON 結果
     * @param isFront 正反面標識，true:正面, false反面
     */
    private void saveCardRawQwen(Long imageId, Map<String, String> cardMap, boolean isFront) {
        CardRawSaveReqVO cardRawVO = new CardRawSaveReqVO();
        cardRawVO.setImageId(imageId);
        cardRawVO.setRawOcrText(JSON.toJSONString(cardMap));
        cardRawVO.setOcrResultJson(JSON.toJSONString(cardMap));
        cardRawVO.setDirectionFlag(isFront);
        cardRawVO.setApiSource("ali_qwen");
        cardRawVO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        cardRawVO.setCreateTime(LocalDateTime.now());
        cardRawVO.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        cardRawVO.setUpdateTime(LocalDateTime.now());
        cardRawVO.setDeleted(false);
        cardRawService.createCard(cardRawVO);
    }


    private String[] fixOrgAndJob(String orgName, String jobTitle) {
        orgName = orgName == null ? "" : orgName.trim();
        jobTitle = jobTitle == null ? "" : jobTitle.trim();

        boolean orgIsJob = looksLikeJobTitle(orgName);
        boolean jobIsOrg = looksLikeOrgName(jobTitle);

        // 1. 同时不为空，且 org 是职位、job 是公司 → 交换
        if (!orgName.isEmpty() && !jobTitle.isEmpty() && orgIsJob && jobIsOrg) {
            String temp = orgName;
            orgName = jobTitle;
            jobTitle = temp;
        }

        // 2. orgName 是职位 且 jobTitle 为空 → jobTitle = orgName
        if (!orgName.isEmpty() && orgIsJob && jobTitle.isEmpty()) {
            jobTitle = orgName;
        }

        // 3. jobTitle 是公司 且 orgName 为空 → orgName = jobTitle，jobTitle 清空
        if (!jobTitle.isEmpty() && jobIsOrg && orgName.isEmpty()) {
            orgName = jobTitle;
            jobTitle = "";
        }

        return new String[]{orgName, jobTitle};
    }

    private boolean looksLikeJobTitle(String text) {
        if (text == null || text.isEmpty()) return false;
        String[] jobKeywords = {"經理", "主任", "工程師", "主管", "助理", "總監", "分析師", "專員", "顧問", "技術員"};
        return Arrays.stream(jobKeywords).anyMatch(text::contains);
    }

    private boolean looksLikeOrgName(String text) {
        if (text == null || text.isEmpty()) return false;
        String[] orgKeywords = {"公司", "集團", "有限公司", "股份", "企业", "事業", "控股"};
        return Arrays.stream(orgKeywords).anyMatch(text::contains);
    }


}
