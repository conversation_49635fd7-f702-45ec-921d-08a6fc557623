package cn.iocoder.yudao.module.pcm.controller.admin.company.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理後臺 - 公司列表 Request VO")
@Data
public class CompanyListReqVO {

    @Schema(description = "公司名稱，模糊匹配", example = "芋道")
    private String name;

    @Schema(description = "展示狀態，參見 CommonStatusEnum 枚舉類", example = "1")
    private Integer status;

}
