package cn.iocoder.yudao.module.pcm.ocr.util;

public class PhoneNumberUtils {

    /**
     * 從電話號碼中提取區號與號碼，支持香港(+852)、澳門(+853)、台灣(+886)、新加坡(+65)、中國大陸(+86及城市區號)、國際區號
     * @param phoneNumber 輸入電話號碼，可能包含區號
     * @return String[]，[0]為區號，[1]為號碼
     */
    public static String[] extractAreaCodeAndNumber(String phoneNumber) {
        String[] result = new String[]{"", ""};

        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return result;
        }

        // 清理輸入：移除空格、連字符、點號、左右括號，轉換 00852 等
        String cleaned = phoneNumber.replaceAll("[\\s-.()]", "")
                .replace("00852", "+852")
                .replace("00853", "+853")
                .replace("00886", "+886")
                .replace("0086", "+86")
                .replace("0065", "+65");

        // 處理香港區號：+852, (852), 852 開頭
        if (cleaned.startsWith("+852") || cleaned.startsWith("852")) {
            result[0] = "+852";
            result[1] = cleaned.startsWith("+") ? cleaned.substring(4) : cleaned.substring(3);
            // 驗證香港號碼（7-8 位）
            /*if (result[1].matches("^[0-9]{7,8}$")) {
                return result;
            }
            result[0] = "";
            result[1] = cleaned;*/
            return result;
        }

        // 處理澳門區號：+853, (853), 853 開頭
        if (cleaned.startsWith("+853") || cleaned.startsWith("853")) {
            result[0] = "+853";
            result[1] = cleaned.startsWith("+") ? cleaned.substring(4) : cleaned.substring(3);
            // 驗證澳門號碼（7-8 位）
            /*if (result[1].matches("^[0-9]{7,8}$")) {
                return result;
            }
            result[0] = "";
            result[1] = cleaned;*/
            return result;
        }

        // 處理台灣區號：+886, (886), 09xxxxxxxx
        if (cleaned.startsWith("+886") || cleaned.matches("^09[0-9]{8}$")) {
            result[0] = "+886";
            result[1] = cleaned.startsWith("+") ? cleaned.substring(4) :
                    cleaned.startsWith("09") ? cleaned.substring(2) : cleaned;
            // 驗證台灣號碼（移動：9 位，以 9 開頭；固定：6-8 位）
            /*if (result[1].matches("^9[0-9]{8}$") || result[1].matches("^[0-9]{6,8}$")) {
                return result;
            }
            result[0] = "";
            result[1] = cleaned;*/
            return result;
        }

        // 處理新加坡區號：+65
        if (cleaned.startsWith("+65")) {
            result[0] = "+65";
            result[1] = cleaned.substring(3);
            // 驗證新加坡號碼（8 位）
            /*if (result[1].matches("^[0-9]{8}$")) {
                return result;
            }
            result[0] = "";
            result[1] = cleaned;*/
            return result;
        }

        // 處理中國大陸：+86
        if (cleaned.startsWith("+86")) {
            result[0] = "+86";
            result[1] = cleaned.substring(3);
            // 檢查是否為城市區號（如 +86 10）
            String remaining = result[1];
            if (remaining.matches("^[0-9]{2,3}[0-9].*")) {
                // 優先匹配 3 位城市區號（如 010）
                if (remaining.matches("^[0-9]{2}[0-9]{7,}.*")) {
                    result[0] = "+86" + remaining.substring(0, 2);
                    result[1] = remaining.substring(2);
                    return result;
                }
                // 再匹配 4 位城市區號（如 0933）
                if (remaining.matches("^[0-9]{3}[0-9].*")) {
                    result[0] = "+86" + remaining.substring(0, 3);
                    result[1] = remaining.substring(3);
                    return result;
                }
            }
            // 驗證移動號碼（11 位，以 13-19 開頭）
            if (result[1].matches("^1[3-9][0-9]{9}$")) {
                return result;
            }
            result[0] = "";
            result[1] = cleaned;
            return result;
        }

        // 處理中國大陸城市區號（以 0 開頭，如 010、0933）
        if (cleaned.matches("^0[0-9]{2,3}[0-9].*")) {
            // 優先匹配 3 位區號（如 010）
            if (cleaned.matches("^0[0-9]{2}[0-9]{7,}.*")) {
                String cityCode = cleaned.substring(0, 3);
                result[0] = "+86" + cityCode.substring(1);
                result[1] = cleaned.substring(3);
                return result;
            }
            // 再匹配 4 位區號（如 0933）
            if (cleaned.matches("^0[0-9]{3}[0-9].*")) {
                String cityCode = cleaned.substring(0, 4);
                result[0] = "+86" + cityCode.substring(1);
                result[1] = cleaned.substring(4);
                return result;
            }
        }

        // 處理中國大陸移動號碼（無區號的 1xxxxxxxxxx）
        if (cleaned.matches("^1[3-9][0-9]{9}$")) {
            result[0] = "+86";
            result[1] = cleaned;
            return result;
        }

        // 處理國際區號（+ 後 2-5 位數字，排除已知區號）
        if (cleaned.matches("^\\+[0-9]{2,5}[0-9].*")) {
            String internationalCode = cleaned.replaceAll("^(\\+[0-9]{2,5}).*", "$1");
            if (!internationalCode.equals("+65") && !internationalCode.equals("+852") &&
                    !internationalCode.equals("+853") && !internationalCode.equals("+886") &&
                    !internationalCode.equals("+86")) {
                result[0] = internationalCode;
                result[1] = cleaned.substring(internationalCode.length());
                return result;
            }
        }

        // 無區號，返回原號碼
        result[0] = "";
        result[1] = cleaned;
        return result;
    }

    /**
     * 判断区号
     */
    public static Boolean isValidAreaCode(String areaCode) {
        // 檢查區號是否為 3 位數字
        if (areaCode.matches("^[0-9]{3}$")) {
            // 檢查區號是否在指定範圍內
            int areaCodeNumber = Integer.parseInt(areaCode);
            return areaCodeNumber >= 1 && areaCodeNumber <= 999;
        }
        return false;
    }

    // 測試方法
    public static void main(String[] args) {
        // 測試用例，涵蓋問題用例及港澳台、新加坡、中國大陸
        String[] testCases = {
                "+852395566001",
                "(8522620111",         // 香港，無左括號
                "852 2620111",         // 香港，無左括號
                "852)2620111",         // 香港，無左括號
                "(852)37557025",       // 香港，括號格式
                "85235988954",         // 香港，無前綴
                "(853)28765432",       // 澳門，括號格式
                "85312345678",         // 澳門，無前綴
                "+85201715047",        // 香港，測試用例
                "+85361234567",        // 澳門
                "+886912345678",       // 台灣，移動號碼
                "0912345678",          // 台灣，無前綴移動號碼
                "0933.178.520",        // 中國大陸，蘭州
                "010-12345678",        // 中國大陸，北京
                "+86 10 8888 8888",    // 中國大陸，北京
                "+86 13900001111",     // 中國大陸，移動號碼
                "15322784041",         // 中國大陸，移動號碼無區號
                "+65 9123 3210",       // 新加坡，移動號碼
                "+65 6234 5678",       // 新加坡，固定電話
                "69923847",            // 無區號
                "+49211424721",        // 國際區號（德國）
                "12345678",            // 無區號
                "abc",                 // 無效輸入
                "",                    // 空輸入
                null                   // null 輸入
        };

        for (String test : testCases) {
            String[] result = extractAreaCodeAndNumber(test);
            System.out.printf("Input: %-20s | Area Code: %-10s | Number: %s%n",
                    test, result[0], result[1]);
        }
    }
}
