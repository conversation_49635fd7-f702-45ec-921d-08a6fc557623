package cn.iocoder.yudao.module.pcm.controller.admin.company.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 公司分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CompanyPageReqVO extends PageParam {

    @Schema(description = "公司名称")
    private String name;

    @Schema(description = "父公司ID，0表示顶级公司")
    private Long parentId;

    @Schema(description = "公司語言類型: zh-中文公司, en-英文公司")
    private String languageType;

    @Schema(description = "公司地址")
    private String address;

    @Schema(description = "公司电话")
    private String phone;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}