package cn.iocoder.yudao.module.pcm.ocr.vo;

import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardSaveReqVO;

public class ProcessedCard {

    private Long frontFileId;
    private Long backFileId;
    private Long cardId; // 成功时返回的 cardId，失败时为 null
    private String status; // "SUCCESS", "FRONT_FAILED", "BACK_FAILED", "ERROR"
    private String message; // 失败原因或成功信息
    private Long duplicateCardId; // 重复的CardId

    public ProcessedCard(Long frontFileId, Long backFileId, Long cardId, String status, String message) {
        this.frontFileId = frontFileId;
        this.backFileId = backFileId;
        this.cardId = cardId;
        this.status = status;
        this.message = message;
    }

    public ProcessedCard(Long frontFileId, Long backFileId, Long cardId, String status, String message, Long duplicateId) {
        this.frontFileId = frontFileId;
        this.backFileId = backFileId;
        this.cardId = cardId;
        this.status = status;
        this.message = message;
        this.duplicateCardId = duplicateId;
    }

    public Long getFrontFileId() {
        return frontFileId;
    }

    public void setFrontFileId(Long frontFileId) {
        this.frontFileId = frontFileId;
    }

    public Long getBackFileId() {
        return backFileId;
    }

    public void setBackFileId(Long backFileId) {
        this.backFileId = backFileId;
    }

    public Long getCardId() {
        return cardId;
    }

    public void setCardId(Long cardId) {
        this.cardId = cardId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getDuplicateCardId() {
        return duplicateCardId;
    }

    public void setDuplicateCardId(Long duplicateCardId) {
        this.duplicateCardId = duplicateCardId;
    }
}
