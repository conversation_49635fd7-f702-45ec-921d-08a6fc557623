package cn.iocoder.yudao.module.pcm.ocr.client;

import java.io.IOException;
import java.util.*;

import cn.iocoder.yudao.module.pcm.ocr.util.PublicUrlHandler;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversation;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationParam;
import com.alibaba.dashscope.aigc.multimodalconversation.MultiModalConversationResult;
import com.alibaba.dashscope.common.MultiModalMessage;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.UploadFileException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class QwenOcrClient {

    private static final String DASHSCOPE_API_KEY = "sk-fdc2594694a04358acba2fed12ec6dbe";
    private static final Logger log = LoggerFactory.getLogger(QwenOcrClient.class);

    public static Map<String, String> simpleMultiModalConversationCall() throws ApiException, NoApiKeyException, UploadFileException {

        try {

            String imageUrl = PublicUrlHandler.testUploadFileAndGetUrl();
            MultiModalConversation conv = new MultiModalConversation();
            Map<String, Object> map = new HashMap<>();
            map.put("image", imageUrl);
            // 输入图像的最小像素阈值，小于该值图像会按原比例放大，直到总像素大于min_pixels
            map.put("min_pixels", "3136");
            // 输入图像的最大像素阈值，超过该值图像会按原比例缩小，直到总像素低于max_pixels
            map.put("max_pixels", "6422528");

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("X-DashScope-OssResourceResolve", "enable");

            // 开启图像自动转正功能
            map.put("enable_rotate", true);
            MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue())
                    .content(Arrays.asList(
                            map,
                            Collections.singletonMap("text", "作为名片识别助手，请执行： 识别名片中的信息，包括姓氏(lastName)、名字(firstName)、英文姓氏(lastNameEn)、英文名字(firstNameEn)、办公电话（phoneMobile/office/T）、直线电话号码（phoneMainland/direct/D）、手机号码（mobile/M）、国内手机号码（phoneMainland）、传真号码（faxNumber）、邮箱（email/E）、职位（jobTitle）、英文职位(jobTitleEn)、中文公司名称(companyName)、英文公司名称(companyNameEn)、中文公司地址(address)、英文公司地址(addressEn)、网址(website)、社交媒体(socialMedia),若上述信息不存在则对应字段返回\"\"，这里说的中文包含简体和繁体，而且不需要把繁体转成简体，最终识别结果按照以下格式固定返回：data:{'firstName':'xx','lastName':'xx','phoneOffice':'xx','phoneMobile':'xx','phoneMainland':'xx','phoneDirect':'xx','email':'xx','jobTitle':'xx','companyName':'xx','address':'xx','faxNumber':'xx','website':'xx','firstNameEn':'xx','lastNameEn':'xx','jobTitleEn':'xx','companyNameEn':'xx','addressEn':'xx','socialMedia':'xx'}"))).build();

            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .apiKey(DASHSCOPE_API_KEY)
                    .model("qwen-vl-max")
                    .message(userMessage)
                    .headers(headers)
                    .build();

            MultiModalConversationResult result = conv.call(param);
            String jsonString = (String) result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text");

            // 提取并解析结构化 JSON 部分
            String jsonPart = jsonString.split("```json")[1].replace("\n```", "").trim();

            ObjectMapper mapper = new ObjectMapper();
            // 反序列化为 Map
            Map<String, Object> jsonMap = mapper.readValue(jsonPart, Map.class);

            // 访问 "data" 字段
            Map<String, String> data = (Map<String, String>) jsonMap.get("data");
            System.out.println(data);
            return data;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Map<String, String> multiModalConversationCall(String apiKey, String modelName, String fileName, byte[] bytes) throws ApiException, NoApiKeyException, UploadFileException {

        try {
            String imageUrl = PublicUrlHandler.uploadFileAndGetUrlByte(apiKey, modelName, fileName, bytes);
            MultiModalConversation conv = new MultiModalConversation();
            Map<String, Object> map = new HashMap<>();
            map.put("image", imageUrl);
            // 输入图像的最小像素阈值，小于该值图像会按原比例放大，直到总像素大于min_pixels
            map.put("min_pixels", "3136");
            // 输入图像的最大像素阈值，超过该值图像会按原比例缩小，直到总像素低于max_pixels
            map.put("max_pixels", "6422528");

            // 设置请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("X-DashScope-OssResourceResolve", "enable");

            // 开启图像自动转正功能
            map.put("enable_rotate", true);
            MultiModalMessage userMessage = MultiModalMessage.builder().role(Role.USER.getValue())
                    .content(Arrays.asList(
                            map,
                            Collections.singletonMap("text", "作为名片识别助手，请执行： 识别名片中的信息，包括中文姓氏(firstName)、中文名字(lastName)、英文姓氏(firstNameEn)、英文名字(lastNameEn)、办公电话（phoneMobile/office/T）、直线电话号码（phoneMainland/direct/D）、手机号码（mobile/M）、国内手机号码（phoneMainland）、传真号码（faxNumber）、邮箱（email/E）、职位（jobTitle）、英文职位(jobTitleEn)、中文公司名称(companyName)、英文公司名称(companyNameEn)、中文公司地址(address)、英文公司地址(addressEn)、中文勋衔(honour)、英文(honourEn)、网址(website),若上述信息不存在则对应字段返回\"\"，这里说的中文包含简体和繁体，并且不需要把繁体转成简体，英文姓氏和英文名字不用翻译成中文，不要把识别到的英文姓氏和英文名字放到中文姓氏和中文名字中，不要把公司名识别成人的姓名，英文地址也不要放到中文地址，中文地址也不能放到应该地址，如果没识别到，返回\"\"，即可，最终识别结果按照以下格式固定返回：data:{'firstName':'xx','lastName':'xx','firstNameEn':'xx','lastNameEn':'xx','phoneOffice':'xx','phoneMobile':'xx','phoneMainland':'xx','phoneDirect':'xx','faxNumber':'xx','email':'xx','jobTitle':'xx','jobTitleEn':'xx','companyName':'xx','companyNameEn':'xx','address':'xx','addressEn':'xx','honour':'xx','honourEn':'xx','website':'xx'}"))).build();

            MultiModalConversationParam param = MultiModalConversationParam.builder()
                    .apiKey(apiKey)
                    .model(modelName)
                    .message(userMessage)
                    .headers(headers)
                    .build();

            MultiModalConversationResult result = conv.call(param);
            log.info("千問原始數據：{} ", result);

            String jsonString = (String) result.getOutput().getChoices().get(0).getMessage().getContent().get(0).get("text");
            log.info("千問獲取jsonString：{} ", jsonString);

            // 提取并解析结构化 JSON 部分
            String jsonPart = jsonString.split("```json")[1].replace("\n```", "").trim();
            log.info("千問獲取jsonPart：{} ", jsonPart);

            ObjectMapper mapper = new ObjectMapper();
            // 反序列化为 Map
            Map<String, Object> jsonMap = mapper.readValue(jsonPart, Map.class);
            log.info("千問獲取jsonMap：{} ", jsonMap);

            // 访问 "data" 字段
            Map<String, String> data = null;
            // 检查 jsonMap 是否非空
            if (jsonMap != null && !jsonMap.isEmpty()) {
                // 获取 data 字段
                Object dataObj = jsonMap.get("data");
                if (dataObj instanceof Map) {
                    data = (Map<String, String>) dataObj;
                    // 如果 data 非空且 key 符合要求，返回 data
                    if (data != null && isValidDataFormat(data)) {
                        return data;
                    }
                } else if (dataObj == null) {
                    // 如果 data 为 null，验证 jsonMap 是否符合格式
                    if (isValidDataFormat(jsonMap)) {
                        // 确保 jsonMap 的值可以安全转换为 String
                        Map<String, String> stringStringMap = convertToStringMap(jsonMap);
                        log.info("千問獲取data：{} ", stringStringMap);
                        return stringStringMap;
                    }
                }
            }

            // 验证失败，返回空 Map
            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("千問獲取data失败：{} ", e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将 Map<String, Object> 转换为 Map<String, String>
     * @param map
     * @return
     */
    private static Map<String, String> convertToStringMap(Map<String, Object> map) {
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object value = entry.getValue();
            result.put(entry.getKey(), value != null ? value.toString() : null);
        }
        return result;
    }

    /**
     * 检查 Map 的数据格式是否正确
     * @param data
     * @return
     */
    private static boolean isValidDataFormat(Map<String, ?> data) {
        // 定义必需字段
        String[] requiredFields = {
                "firstName", "lastName", "phoneOffice", "phoneMobile",
                "email", "companyName", "address", "firstNameEn",
                "lastNameEn", "jobTitleEn", "companyNameEn", "addressEn"
        };

        // 检查所有必需字段是否存在
        for (String field : requiredFields) {
            if (!data.containsKey(field)) {
                return false;
            }
        }

        // 统计值为 null 的字段数量
        int nullCount = 0;
        for (String field : requiredFields) {
            if (data.get(field) == null) {
                nullCount++;
            }
        }

        // 如果所有字段值都为 null，返回 false
        if (nullCount == requiredFields.length) {
            return false;
        }

        return true;
    }

    public static void main(String[] args) {
        try {
            simpleMultiModalConversationCall();
        } catch (ApiException | NoApiKeyException | UploadFileException e) {
            System.out.println(e.getMessage());
        }
        System.exit(0);
    }

}
