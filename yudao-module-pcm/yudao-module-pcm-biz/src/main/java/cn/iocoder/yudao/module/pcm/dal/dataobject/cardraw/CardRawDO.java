package cn.iocoder.yudao.module.pcm.dal.dataobject.cardraw;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 名片 DO
 *
 * <AUTHOR>
 */
@TableName("pcm_card_raw")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardRawDO implements Serializable, TransPojo {

    /**
     * 名片ID
     */
    @TableId
    private Long id;

    /**
     * 圖片ID，對應infra_file中的ID
     */
    private Long imageId;

    /**
     * 原始識別文本
     */
    private String rawOcrText;

    /**
     * 引擎返回的結構化 JSON 結果
     */
    private String ocrResultJson;

    /**
     * 正反面標識，1:正面,0反面
     */
    private Boolean directionFlag;

    /**
     * 數據來源，tencent_ocr:騰訊ocr,ali_qwen：阿里千問
     */
    private String apiSource;

    /**
     * 建立時間
     */
    private LocalDateTime createTime;

    /**
     * 更新時間
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新者
     *
     */
    private String updater;

    /**
     * 是否刪除
     */
    private Boolean deleted;

}