package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 名片 Excel 導入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 設置 chain = false，避免名片導入有問題
public class CardImportExcelVO {

    @ExcelProperty("姓氏")
    private String firstName;

    @ExcelProperty("名字")
    private String lastName;

    @ExcelProperty("暱稱")
    private String nickName;

    @ExcelProperty("標籤")
    private String customTags;

    @ExcelProperty("機構名稱")
    private String companyName;

    @ExcelProperty("部門")
    private String department;

    @ExcelProperty("職位")
    private String jobTitle;

    @ExcelProperty("電郵")
    private String email;

    @ExcelProperty("電郵1")
    private String email1;

    @ExcelProperty("稱謂")
    private String title;

    @ExcelProperty("勳銜")
    private String honour;

    @ExcelProperty("Title")
    private String titleEn;

    @ExcelProperty("FirstName")
    private String firstNameEn;

    @ExcelProperty("LastName")
    private String lastNameEn;

    @ExcelProperty("Honour")
    private String honourEn;

    @ExcelProperty("Name of Organisation")
    private String companyNameEn;

    @ExcelProperty("Department")
    private String departmentEn;

    @ExcelProperty("Job Title")
    private String jobTitleEn;

    @ExcelProperty("流動電話")
    private String phoneMobile;

    @ExcelProperty("內地號碼")
    private String phoneMainland;

    @ExcelProperty("電郵2")
    private String email2;

    @ExcelProperty("辦公電話")
    private String phoneOffice;

    @ExcelProperty("直線電話")
    private String phoneDirectLine;

    @ExcelProperty("傳真")
    private String faxNumber;

    @ExcelProperty("addressline1")
    private String addressLine1En;

    @ExcelProperty("addressline2")
    private String addressLine2En;

    @ExcelProperty("district")
    private String districtEn;

    @ExcelProperty("area")
    private String areaEn;

    @ExcelProperty("country")
    private String countryEn;

    @ExcelProperty("地址1")
    private String addressLine1;

    @ExcelProperty("地址2")
    private String addressLine2;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("地區")
    private String district;

    @ExcelProperty("區域")
    private String area;

    @ExcelProperty("国家")
    private String country;

    @ExcelProperty("城市")
    private String city;

    @ExcelProperty("行業類型")
    private String industryType;

    @ExcelProperty("業務類型")
    private String businessType;

    @ExcelProperty("其他業務類型")
    private String otherBusiness;

    @ExcelProperty("linkedin profile url")
    private String linkedinProfileUrl;

    @ExcelProperty("facebook page url")
    private String facebookPageUrl;

    @ExcelProperty("instagram url")
    private String instagramUrl;

    @ExcelProperty("wechat id")
    private String wechatId;

    @ExcelProperty("社交媒体")
    private String socialMedia;

    @ExcelProperty("會面詳情")
    private String communicationHistory;

    @ExcelProperty("创建者")
    private String creator;


}
