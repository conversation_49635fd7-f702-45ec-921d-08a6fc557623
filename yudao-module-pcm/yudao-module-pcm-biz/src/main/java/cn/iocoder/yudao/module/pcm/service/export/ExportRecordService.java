package cn.iocoder.yudao.module.pcm.service.export;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.ExportRequestVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ApprovalPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ApproveRequestVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ExportRecordVO;

import java.util.List;
import java.util.Map;

public interface ExportRecordService {

    /**
     * 提交名片导出申请
     *
     * @param requestVO 申请信息
     * @return 申请编号
     */
    CommonResult<Long> submitExportRequest(ExportRequestVO requestVO);

    /**
     * 获取审批列表
     *
     * @param pageVO 分页参数
     * @return 列表
     */
    CommonResult<PageResult<ExportRecordVO>> getApprovalList(ApprovalPageReqVO pageVO);

    /**
     * 获取导出列表
     *
     * @param pageVO 分页参数
     * @return 列表
     */
    CommonResult<PageResult<ExportRecordVO>> getExportList(ApprovalPageReqVO pageVO);

    /**
     * 审批导出请求
     *
     * @param approveVO 审批信息
     * @return 导出记录
     */
    CommonResult<ExportRecordVO> approveExportRequest(ApproveRequestVO approveVO);

    /**
     * 獲取當前使用者的待審批和待導出記錄數
     * @return 待審批和待導出記錄數
     */
    CommonResult<Map<String, Object>> getTodoNotification();

    /**
     * 檢查當前用戶是否達到導出限制（累計次數 >=50 次）
     * @return
     */
    CommonResult<Map<String, Object>> checkRestriction(CardPageReqVO pageReqVO);

    /**
     * 审批导出请求
     *
     * @param guid export_records 的guid
     * @return 导出记录
     */
    CommonResult<Boolean> approveByEmail(String guid, boolean isApproved, String approverComment);


    /**
     * 获取审批请求
     * @param guid
     * @return
     */
    CommonResult<ExportRecordVO> getApproveByGuid(String guid);
}
