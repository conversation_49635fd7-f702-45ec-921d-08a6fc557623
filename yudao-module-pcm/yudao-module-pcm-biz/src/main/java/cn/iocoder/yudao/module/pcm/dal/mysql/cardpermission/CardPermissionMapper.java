package cn.iocoder.yudao.module.pcm.dal.mysql.cardpermission;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo.CardPermissionPageReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission.CardPermissionDO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface CardPermissionMapper extends BaseMapperX<CardPermissionDO> {

    default PageResult<CardPermissionDO> selectPage(CardPermissionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CardPermissionDO>()
                .eqIfPresent(CardPermissionDO::getCardId, reqVO.getCardId())
                .eqIfPresent(CardPermissionDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(CardPermissionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CardPermissionDO::getId));
    }

    default List<CardPermissionDO> getByCardId(Long cardId) {
        return selectList(CardPermissionDO::getCardId, cardId);
    }

    default List<CardPermissionDO> getByCardIdAndUserId(Long cardId, Long userId) {
        return selectList(
                Wrappers.<CardPermissionDO>lambdaQuery()
                        .eq(CardPermissionDO::getCardId, cardId)
                        .eq(CardPermissionDO::getUserId, userId)
        );
    }



}