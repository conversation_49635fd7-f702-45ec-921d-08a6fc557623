package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 名片新增/修改 Request VO")
@Data
public class CardSaveReqVO {

    private String redisId;

    @Schema(description = "名片ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000001")
    private Long id;

    @Schema(description = "分组ID,用于合并名片", requiredMode = Schema.RequiredMode.REQUIRED, example = "251f242d4cd548ea80ed69e4015cb6c9")
    private String groupId;

    @Schema(description = "Title (Mr, Ms, Mrs, Others)")
    private String titleEn;

    @Schema(description = "First Name (e.g. Tai-man)")
    private String firstNameEn;

    @Schema(description = "Last Name (e.g. CHAN)")
    private String lastNameEn;

    @Schema(description = "Honour (e.g. MH, JP)")
    private String honourEn;

    @Schema(description = "稱謂（先生/ 小姐/ 女士/其他）")
    private String title;

    @Schema(description = "姓氏")
    private String firstName;

    @Schema(description = "名字")
    private String lastName;

    @Schema(description = "勳銜")
    private String honour;

    @Schema(description = "全名")
    private String fullName;

    @Schema(description = "暱稱")
    private String nickName;

    @Schema(description = "companyIdEn 機構ID")
    private String companyIdEn;

    @Schema(description = "companyNameEn(Name of Organisation)")
    private String companyNameEn;

    @Schema(description = "Department")
    private String departmentEn;

    @Schema(description = "Job Title")
    private String jobTitleEn;

    @Schema(description = "機構ID")
    private Long companyId;

    @Schema(description = "機構名稱")
    private String companyName;

    @Schema(description = "部門")
    private String department;

    @Schema(description = "職位")
    private String jobTitle;

    @Schema(description = "流動電話")
    private String phoneMobile;

    @Schema(description = "內地號碼區碼")
    private String phoneMobileAreaCode;

    @Schema(description = "內地號碼")
    private String phoneMainland;

    @Schema(description = "內地號碼區碼")
    private String phoneMainlandAreaCode;

    @Schema(description = "Email Address 電郵")
    private String email;

    @Schema(description = "電郵1")
    private String email1;

    @Schema(description = "電郵2")
    private String email2;

    @Schema(description = "Phone(office) 電話（辦公室）")
    private String phoneOffice;

    @Schema(description = "辦公電話區碼")
    private String phoneOfficeAreaCode;

    @Schema(description = "Phone(Direct Line)電話（直線）")
    private String phoneDirectLine;

    @Schema(description = "直線電話區碼")
    private String phoneDirectLineAreaCode;

    @Schema(description = "fax number 傳真")
    private String faxNumber;

    @Schema(description = "英文地址1")
    private String addressLine1En;

    @Schema(description = "英文地址2")
    private String addressLine2En;

    @Schema(description = "英文地區(e.g. Tsim Sha Tsui)")
    private String districtEn;

    @Schema(description = "英文區域(e.g. Kowloon)")
    private String areaEn;

    @Schema(description = "英文國家(countryEn)")
    private String countryEn;

    @Schema(description = "中文地址1")
    private String addressLine1;

    @Schema(description = "中文地址2")
    private String addressLine2;

    @Schema(description = "中文地區")
    private String district;

    @Schema(description = "中文區域")
    private String area;

    @Schema(description = "中文國家")
    private String country;

    @Schema(description = "省")
    private String province;

    @Schema(description = "中文城市")
    private String city;

    @Schema(description = "(e.g. Technology, Healthcare) 行業類型")
    private String industryType;

    @Schema(description = "業務類型（政府或相關組織、企業、基金會、非政府組織、其他）")
    private String businessType;

    @Schema(description = "其他業務類型")
    private String otherBusiness;

    @Schema(description = "linkedin profile url")
    private String linkedinProfileUrl;

    @Schema(description = "facebook page url")
    private String facebookPageUrl;

    @Schema(description = "instagram url")
    private String instagramUrl;

    @Schema(description = "wechat id")
    private String wechatId;

    @Schema(description = "會面詳情")
    private String communicationHistory;

    @Schema(description = "標籤")
    private String customTags;

    @Schema(description = "狀態（Active:活躍，Inactive:不活躍）")
    private String status;

    @Schema(description = "備註")
    private String notes;

    @Schema(description = "簡介", example = "")
    private String description;

    @Schema(description = "图片头像地址")
    private String imageUrl;

    @Schema(description = "反面名片的URL")
    private String backUrl;

    /**
     * 正面图片ID
     */
    private Long imageId;

    /**
     * 反面图片ID
     */
    private Long backId;

    private String address;
    private String socialMedia;

    private String userId;
    private String deptId;

    @Schema(description = "网址")
    private String website;

    @Schema(description = "即时消息")
    private String instantMessaging;

    @Schema(description = "纪念日")
    private String anniversary;

    @Schema(description = "交换日期")
    private String exchangeDate;

    @Schema(description = "是否双面：true 为双面，false 为单面")
    private Boolean doubleSided;

    @Schema(description = "創建時間")
    private LocalDateTime createTime;

    @Schema(description = "更新時間")
    private LocalDateTime updateTime;

    @Schema(description = "創建者ID")
    private Long createUserId;

    @Schema(description = "創建者")
    private String creator;

    @Schema(description = "最後更新者")
    private String updater;

    @Schema(description = "是否删除")
    private Boolean deleted;

}