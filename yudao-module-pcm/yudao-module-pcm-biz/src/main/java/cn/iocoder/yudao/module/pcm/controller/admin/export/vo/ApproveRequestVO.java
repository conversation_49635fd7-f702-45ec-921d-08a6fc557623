package cn.iocoder.yudao.module.pcm.controller.admin.export.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class ApproveRequestVO {

    //@NotNull(message = "匯出記錄編號不可為空")
    private Long recordId; // 匯出記錄編號

    //@NotNull(message = "審批結果不可為空")
    private Boolean approved; // 是否通過

    //@NotBlank(message = "審批人備註不可為空")
    private String approverComment; // 審批人備註


}
