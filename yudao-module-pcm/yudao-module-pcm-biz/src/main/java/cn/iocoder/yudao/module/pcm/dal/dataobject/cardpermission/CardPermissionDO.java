package cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;
import java.time.LocalDateTime;

@Data
@TableName("pcm_card_permission")
public class CardPermissionDO {

    @TableId
    private Long id;
    private Long cardId;
    private Long userId;
    private String fieldName; // 以数组格式，存储字段权限
    /**
     * 是否可编辑名片：1 可编辑, 0 不可编辑
     */
    private Boolean isEdit;
    private Long deptId;
    private Long roleId;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     */
    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     */
    @TableField(fill = FieldFill.UPDATE, jdbcType = JdbcType.VARCHAR)
    private String updater;

    /**
     * 是否删除
     */
    private Boolean deleted;
}