package cn.iocoder.yudao.module.pcm.ocr.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.UUID;

/**
 * 图片转base64
 */
public class OcrUtils {

    /**
     * 图片转base64
     * @param imagePath
     * @return
     */
    public static String imageToBase64(String imagePath) {
        File file = new File(imagePath);
        try (FileInputStream imageInFile = new FileInputStream(file)) {
            byte[] imageData = new byte[(int) file.length()];
            imageInFile.read(imageData);
            return Base64.getEncoder().encodeToString(imageData);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }
    /**
     * 将图像数据转换为Base64编码的字符串
     * 此方法用于将图像的二进制数据转换为Base64编码的字符串，以便于在网络传输或存储时使用
     * @param imageData 图像的二进制数据数组
     * @return 转换后的Base64编码字符串
     */
    public static String imageToBase64(byte[] imageData) {
        return Base64.getEncoder().encodeToString(imageData);
    }

    /**
     * 将Base64编码的图像数据转换为二进制数据
     * @param base64
     * @return
     */
    public static byte[] base64ToImage(String base64) {
        return Base64.getDecoder().decode(base64);
    }

    /**
     * 根据原文件名生成新的文件名，保留原文件后缀
     *
     * @param originalFilename 原文件名
     * @return 新文件名
     */
    public static String renameFile(String originalFilename) {
        if (originalFilename == null || !originalFilename.contains(".")) {
            throw new IllegalArgumentException("文件名无效，无法提取后缀！");
        }

        // 截取后缀名（包含.，如 .png）
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));

        // 生成新的随机文件名
        String newFileName = UUID.randomUUID().toString().replace("-", "") + suffix;

        return newFileName;
    }

}
