package cn.iocoder.yudao.module.pcm.ocr.client;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRResponse;

public class TencentOcrClient {

    private final OcrClient client;

    public TencentOcrClient(Credential credential, String region) {
        // 创建一个 HttpProfile 对象，用于配置HTTP请求的细节。
        HttpProfile httpProfile = new HttpProfile();

        // 指定了腾讯云OCR服务的API地址（域名），程序会向这个地址发送请求。
        httpProfile.setEndpoint("ocr.tencentcloudapi.com");

        // 创建一个 ClientProfile对象，用于配置client的一些属性，如超时时间等。
        ClientProfile clientProfile = new ClientProfile();

        //// 配置的 httpProfile 设置到客户端配置文件中
        clientProfile.setHttpProfile(httpProfile);

        // 创建一个 OcrClient 对象，用于发送请求到腾讯云OCR服务。
        this.client = new OcrClient(credential, region, clientProfile);
    }

    /**
     * 调用腾讯云OCR服务进行名片识别
     * @param request 请求对象
     * @return
     * @throws TencentCloudSDKException
     */
    public BusinessCardOCRResponse businessCardOcr(BusinessCardOCRRequest request) throws TencentCloudSDKException {
        return client.BusinessCardOCR(request);
    }
}
