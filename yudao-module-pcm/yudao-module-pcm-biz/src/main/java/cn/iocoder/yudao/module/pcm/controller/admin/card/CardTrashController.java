package cn.iocoder.yudao.module.pcm.controller.admin.card;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardRespVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import cn.iocoder.yudao.module.pcm.service.meeting.MeetingService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理後臺 - 名片回收站")
@RestController
@RequestMapping("/pcm/cardtrash")
@Validated
public class CardTrashController {

    @Resource
    private CardService cardService;

    @Resource
    private MeetingService meetingService;


    @GetMapping("/getCardTrash")
    @Operation(summary = "名片回收站列表")
    public CommonResult<PageResult<CardRespVO>> getCardTrash(CardPageReqVO pageReqVO) {
        PageResult<CardDO> pageResult = cardService.getCardTrash(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CardRespVO.class));
    }

    @DeleteMapping("/permanentlyDelete")
    @Operation(summary = "批量彻底删除名片")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> permanentlyDelete(@RequestBody JSONObject params) {
        JSONArray arrayIds = params.getJSONArray("ids");
        if (arrayIds == null || arrayIds.isEmpty()) {
            return CommonResult.error(403,  "请选择要彻底删除的名片");
        }
        List<Long> ids = arrayIds.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        cardService.permanentlyDelete(ids);
        meetingService.permanentlyDeleteMeeting(ids);
        return success(true);
    }

    @PutMapping("/restore")
    @Operation(summary = "批量恢复名片")
    public CommonResult<Boolean> restore(@RequestBody JSONObject params) {
        JSONArray arrayIds = params.getJSONArray("ids");
        if (arrayIds == null || arrayIds.isEmpty()) {
            return CommonResult.error(403,  "请选择要恢复的名片");
        }
        List<Long> ids = arrayIds.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        cardService.restore(ids);
        meetingService.restoreMeeting(ids);
        return success(true);
    }

}
