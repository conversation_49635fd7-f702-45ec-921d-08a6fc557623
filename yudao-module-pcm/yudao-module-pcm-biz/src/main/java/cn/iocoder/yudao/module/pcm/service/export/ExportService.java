package cn.iocoder.yudao.module.pcm.service.export;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportRecordDO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;

public interface ExportService {

    /**
     * 提交导出请求
     * @param userId
     * @param count
     * @param requesterComment
     * @param cardIds
     * @return
     */
    CommonResult<ExportRecordDO> requestExport(Long userId, int count, String requesterComment, String cardIds);

    /**
     * 审批导出请求
     * @param recordId
     * @param approverId
     * @param isApproved
     * @param approverComment
     * @return
     */
    CommonResult<ExportRecordDO> approveExport(Long recordId, Long approverId, boolean isApproved, String approverComment);

    /**
     * 获取导出记录
     * @param recordId
     * @return
     */
    ExportRecordDO getExportRecord(Long recordId);

    /**
     * 获取审批人
     * @param user
     * @return
     */
    AdminUserRespDTO getApprover(AdminUserRespDTO user);
}
