package cn.iocoder.yudao.module.pcm.service.export;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardRespVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.ExportRequestVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ApprovalPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ApproveRequestVO;
import cn.iocoder.yudao.module.pcm.controller.admin.export.vo.ExportRecordVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportRecordDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportTotalDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportRecordMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportTotalMapper;
import cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.EXPORT_AT_LEAST_ONE_CARD_REQUIRED;

@Service
public class ExportRecordServiceImpl implements ExportRecordService {

    private static final Logger log = LoggerFactory.getLogger(ExportRecordServiceImpl.class);
    @Resource
    private ExportRecordMapper exportRecordMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private CardService cardService;
    @Resource
    private ExportTotalMapper exportTotalMapper;
    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public CommonResult<Long> submitExportRequest(ExportRequestVO requestVO) {
        // 獲取當前使用者編號
        Long userId = getCurrentUserId();

        // 重新查詢名片數據（使用原查詢條件）
        CardPageReqVO pageReqVO = requestVO.getPageReqVO();
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<CardRespVO> pageResult = cardService.exportCardExcel(pageReqVO);
        List<CardRespVO> list = pageResult.getList();
        int exportCount = list.size();

        if (exportCount == 0) {
            return CommonResult.error(EXPORT_AT_LEAST_ONE_CARD_REQUIRED);
        }

        // 獲取名片主鍵 ID 列表
        String cardIds = list.stream()
                .map(card -> String.valueOf(card.getId()))
                .collect(Collectors.joining(","));

        // 提交導出請求
        CommonResult<ExportRecordDO> exportResult = exportService.requestExport(
                userId,
                exportCount,
                requestVO.getRequesterComment(),
                cardIds
        );

        if (!exportResult.isSuccess()) {
            return CommonResult.error(exportResult.getCode(), exportResult.getMsg());
        }

        ExportRecordDO exportRecord = exportResult.getData();
        return CommonResult.success(exportRecord.getId());
    }

    @Override
    public CommonResult<PageResult<ExportRecordVO>> getApprovalList(ApprovalPageReqVO pageVO) {
        boolean admin = SecurityFrameworkUtils.isAdmin();

        // 獲取當前使用者編號
        Long approverId = getCurrentUserId();

        // 查詢當前審批人相關的匯出記錄
        LambdaQueryWrapperX<ExportRecordDO> queryWrapper = new LambdaQueryWrapperX();
        queryWrapper.eq(ExportRecordDO::getDeleted, false);

        if (!admin) {
            // 非管理員：限制審批人，並根據 exportStatus 查詢
            queryWrapper.eq(ExportRecordDO::getApproverId, approverId);
        }

        // 申請人模糊查詢
        queryWrapper.eqIfPresent(ExportRecordDO::getStatus, pageVO.getStatus());
        queryWrapper.likeIfPresent(ExportRecordDO::getRequesterName, pageVO.getApplicant());

        // 處理時間範圍查詢
        String[] createTime = pageVO.getCreateTime();
        if (createTime != null && createTime.length == 2 && StrUtil.isNotBlank(createTime[0]) && StrUtil.isNotBlank(createTime[1])) {
            queryWrapper.between(ExportRecordDO::getCreateTime, createTime[0], createTime[1]);
        }

        queryWrapper.orderByDesc(ExportRecordDO::getCreateTime);

        // 分頁查詢
        PageResult<ExportRecordDO> pageResult = exportRecordMapper.selectPage(pageVO, queryWrapper);
        List<ExportRecordVO> voList = BeanUtils.toBean(pageResult.getList(), ExportRecordVO.class);
        PageResult<ExportRecordVO> voPageResult = new PageResult<>(voList, pageResult.getTotal());
        return CommonResult.success(voPageResult);
    }

    @Override
    public CommonResult<PageResult<ExportRecordVO>> getExportList(ApprovalPageReqVO pageVO) {
        boolean admin = SecurityFrameworkUtils.isAdmin();
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        // 查詢當前審批人相關的匯出記錄
        LambdaQueryWrapperX<ExportRecordDO> queryWrapper = new LambdaQueryWrapperX();
        queryWrapper.eq(ExportRecordDO::getDeleted, false);
        if (!admin) {
            queryWrapper.eq(ExportRecordDO::getUserId, loginUserId);
        }
        queryWrapper.eqIfPresent(ExportRecordDO::getId, pageVO.getId());
        queryWrapper.likeIfPresent(ExportRecordDO::getRequesterName, pageVO.getApplicant());
        queryWrapper.eqIfPresent(ExportRecordDO::getExportStatus, pageVO.getExportStatus());

        // 處理 createTime 時間範圍查詢
        if (pageVO.getCreateTime() != null && pageVO.getCreateTime().length == 2) {
            String startTime = pageVO.getCreateTime()[0];
            String endTime = pageVO.getCreateTime()[1];
            if (StrUtil.isNotBlank(startTime) && StrUtil.isNotBlank(endTime)) {
                queryWrapper.between(ExportRecordDO::getCreateTime, startTime, endTime);
            }
        }

        queryWrapper.orderByDesc(ExportRecordDO::getCreateTime);

        // 分頁查詢
        PageResult<ExportRecordDO> pageResult = exportRecordMapper.selectPage(pageVO, queryWrapper);
        return CommonResult.success(BeanUtils.toBean(pageResult, ExportRecordVO.class));
    }

    @Override
    public CommonResult<ExportRecordVO> approveExportRequest(ApproveRequestVO approveVO) {
        // 獲取當前使用者編號
        Long approverId = getCurrentUserId();

        // 提交審批
        CommonResult<ExportRecordDO> approveResult = exportService.approveExport(
                approveVO.getRecordId(),
                approverId,
                approveVO.getApproved(),
                approveVO.getApproverComment());
        if (!approveResult.isSuccess()) {
            return CommonResult.error(approveResult.getCode(), approveResult.getMsg());
        }

        return CommonResult.success(BeanUtils.toBean(approveResult.getData(), ExportRecordVO.class));
    }

    @Override
    public CommonResult<Map<String, Object>> getTodoNotification() {

        try {
            // 獲取當前使用者編號
            Long userId = getCurrentUserId();

            // 查詢需要審批的記錄數（status = PENDING 且 (approver_id = userId 或 approver_id IS NULL)）
            LambdaQueryWrapperX<ExportRecordDO> approvalQuery = new LambdaQueryWrapperX();
            approvalQuery.eq(ExportRecordDO::getDeleted, false)
                    .eq(ExportRecordDO::getStatus, "PENDING")
                    .and(wrapper -> wrapper
                            .eq(ExportRecordDO::getApproverId, userId)
                            .or()
                            .isNull(ExportRecordDO::getApproverId)
                    );
            long approvalCount = exportRecordMapper.selectCount(approvalQuery);
            log.info("待審批的匯出請求數：{}", approvalCount);

            // 查詢自己需要導出的記錄數（user_id = userId 且 status IN (PENDING, APPROVED) 且 export_status != EXPORTED）
            LambdaQueryWrapperX<ExportRecordDO> exportQuery = new LambdaQueryWrapperX();
            exportQuery.eq(ExportRecordDO::getDeleted, false)
                    .eq(ExportRecordDO::getUserId, userId)
                    .in(ExportRecordDO::getStatus, "PENDING", "APPROVED")
                    .ne(ExportRecordDO::getExportStatus, "EXPORTED");
            long exportCount = exportRecordMapper.selectCount(exportQuery);

            // 封裝響應
            Map<String, Object> rtnData = new HashMap<>();
            rtnData.put("approvalCount",  approvalCount);
            rtnData.put("exportCount", exportCount);
            return CommonResult.success(rtnData);
        } catch (Exception e) {
            return CommonResult.error(500, e.getMessage());
        }
    }

    @Override
    public CommonResult<Map<String, Object>> checkRestriction(CardPageReqVO pageReqVO) {
        Map<String, Object> rtnMap = new HashMap<>();
        boolean admin = SecurityFrameworkUtils.isAdmin();
        if (admin) {
            rtnMap.put("isRestricted", false);
            return CommonResult.success(rtnMap);
        }
        // 獲取當前使用者編號者
        Long userId = getCurrentUserId();
        AdminUserRespDTO currentUser = adminUserApi.getUser(userId);
        // 查詢名片數據
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<CardRespVO> pageResult = cardService.exportCardExcel(pageReqVO);
        List<CardRespVO> list = pageResult.getList();
        int exportCount = list.size();
        log.info("check 本次導出名片數：{}", exportCount);

        // 查詢使用者的累計導出數量
        ExportTotalDO exportTotalDO = exportTotalMapper.findByUserId(userId);
        int total = exportTotalDO != null ? exportTotalDO.getTotalCount() : 0;

        // 判斷是否達到限制（>=50 次）
        int totalCount = exportCount + total;
        boolean isRestricted = totalCount >= 50;

        // 組裝提示語
        String msg = String.format(
                "本次導出%d張，以往導出%d張，本次加上以往30天內累計導出%d張名片，%s",
                exportCount,
                total,
                totalCount,
                isRestricted ? "已達到導出限制，請提交導出審批申請" : "未達到限制，可直接導出"
        );

        AdminUserRespDTO approver = exportService.getApprover(currentUser);
        // 封裝響應用

        rtnMap.put("isRestricted", isRestricted);
        rtnMap.put("exportTotal", exportCount);
        rtnMap.put("totalCount", totalCount);
        rtnMap.put("approverId", approver.getId());
        rtnMap.put("approverName", approver.getNickname());
        rtnMap.put("msg", msg);

        return CommonResult.success(rtnMap);
    }

    @Override
    public CommonResult<Boolean> approveByEmail(String guid, boolean isApproved, String approverComment) {
        if (StringUtils.isEmpty(guid)) {
            return CommonResult.error(ErrorCodeConstants.EXPORT_RECORD_NOT_FOUND);
        }
        ExportRecordDO record = exportRecordMapper.selectOne(ExportRecordDO::getGuid, guid);
        if (record != null) {
            // 根據 guid 查詢 ExportRecordDO
            CommonResult<ExportRecordDO> approveResult = exportService.approveExport(record.getId(), record.getApproverId(), isApproved, approverComment);
            if (!approveResult.isSuccess()) {
                return CommonResult.error(approveResult.getCode(), approveResult.getMsg());
            }
            return CommonResult.success(true);
       }
        return CommonResult.success(false);
    }

    @Override
    public CommonResult<ExportRecordVO> getApproveByGuid(String guid) {
        if (StringUtils.isEmpty(guid)) {
            return CommonResult.error(ErrorCodeConstants.EXPORT_RECORD_NOT_FOUND);
        }
        ExportRecordDO exportRecordDO = exportRecordMapper.selectOne(ExportRecordDO::getGuid, guid);
        return CommonResult.success(BeanUtils.toBean(exportRecordDO, ExportRecordVO.class));
    }

    /**
     * 獲取當前使用者編號
     * @return 使用者編號
     */
    private Long getCurrentUserId() {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        if (userId == null) {
            throw new SecurityException("未登錄");
        }
        return userId;
    }

    /**
     * 將 ExportRecordDO 轉換為 ExportRecordVO
     * @param record DO 對象
     * @return VO 對象
     */
    private ExportRecordVO convertToVO(ExportRecordDO record) {
        ExportRecordVO vo = new ExportRecordVO();
        vo.setId(record.getId());
        vo.setUserId(record.getUserId());
        vo.setExportCount(record.getExportCount());
        vo.setStatus(record.getStatus());
        vo.setRequesterComment(record.getRequesterComment());
        vo.setApproverId(record.getApproverId());
        vo.setApproverComment(record.getApproverComment());
        vo.setCreateTime(record.getCreateTime());
        return vo;
    }
}
