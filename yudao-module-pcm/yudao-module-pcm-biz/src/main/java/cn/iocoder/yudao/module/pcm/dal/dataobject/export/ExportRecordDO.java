package cn.iocoder.yudao.module.pcm.dal.dataobject.export;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

@TableName("pcm_card_export_records")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportRecordDO {

    /**
     * 記錄ID
     */
    private Long id;

    /**
     * 發起導出請求的用戶ID
     */
    private Long userId;

    /**
     * 發起導出請求的用戶名
     */
    private String requesterName;

    /**
     * 導出名片數
     */
    private int exportCount;

    /**
     * 導出請求狀態，待審批(PENDING)、已通過(APPROVED)、已拒絕(REJECTED)
     */
    private String status;

    /**
     * 申請人備註
     */
    private String requesterComment;

    /**
     * 審批人ID
     */
    private Long approverId;

    /**
     * 審批人名
     */
    private String approverName;

    /**
     * 審批時間
     */
    private LocalDateTime approveTime;

    /**
     * 審批人備註
     */
    private String approverComment;

    /**
     * 名片主鍵ID列表
     */
    private String cardIds;

    /**
     * GUID
     */
    private String guid;

    /**
     * 導出狀態：未導出(UNEXPORTED)、已導出(EXPORTED)
     */
    private String exportStatus;

    /**
     * 建立時間
     */
    private LocalDateTime createTime;

    /**
     * 更新時間
     */
    private LocalDateTime updateTime;

    /**
     * 創建者
     */
    private String creator;

    /**
     * 更新者
     *
     */
    private String updater;

    /**
     * 是否刪除
     */
    private Boolean deleted;

}
