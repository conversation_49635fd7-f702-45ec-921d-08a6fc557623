package cn.iocoder.yudao.module.pcm.dal.mysql.export;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportTotalDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 累計匯出數量資料庫操作接口
 */
@Mapper
public interface ExportTotalMapper extends BaseMapperX<ExportTotalDO> {
    /**
     * 根據使用者編號查詢累計記錄
     */
    @Select("SELECT * FROM pcm_card_export_totals WHERE user_id = #{userId} AND deleted = 0")
    ExportTotalDO findByUserId(Long userId);
}
