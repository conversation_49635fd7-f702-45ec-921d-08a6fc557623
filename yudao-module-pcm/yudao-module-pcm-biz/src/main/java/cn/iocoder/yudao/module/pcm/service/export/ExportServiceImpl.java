package cn.iocoder.yudao.module.pcm.service.export;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportRecordDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportTotalDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportRecordMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportTotalMapper;
import cn.iocoder.yudao.module.system.api.mail.MailSendApi;
import cn.iocoder.yudao.module.system.api.mail.dto.MailSendSingleToUserReqDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.*;

@Service
public class ExportServiceImpl implements ExportService {

    private static final Logger log = LoggerFactory.getLogger(ExportServiceImpl.class);
    @Resource
    private AdminUserApi adminUserApi;
    @Autowired
    private ExportRecordMapper exportRecordMapper;
    @Autowired
    private ExportTotalMapper exportTotalMapper;
    @Resource
    private MailSendApi mailSendApi;

    private static final int THRESHOLD = 50; // 累計匯出數量閾值
    private static final int PERIOD_DAYS = 30; // 統計週期（天）

    /**
     * 提交匯出請求
     * @param userId 申請人編號
     * @param count 本次匯出數量
     * @param requesterComment 申請人備註
     * @param cardIds 名片主鍵ID列表（逗號分隔）
     * @return 匯出記錄
     */
    @Override
    @Transactional
    public CommonResult<ExportRecordDO> requestExport(Long userId, int count, String requesterComment, String cardIds) {
        AdminUserRespDTO requestUser = adminUserApi.getUser(userId);
        if (requestUser == null) {
            throw new IllegalArgumentException("使用者不存在");
        }
        if (requesterComment == null || requesterComment.trim().isEmpty()) {
            throw new IllegalArgumentException("申請人備註不可為空");
        }
        if (cardIds == null || cardIds.trim().isEmpty()) {
            throw new IllegalArgumentException("名片ID列表不可為空");
        }

        // 檢查是否存在待審批記錄
        LambdaQueryWrapper<ExportRecordDO> pendingQuery = new LambdaQueryWrapper<ExportRecordDO>()
                .eq(ExportRecordDO::getUserId, userId)
                .eq(ExportRecordDO::getStatus, "PENDING")
                .eq(ExportRecordDO::getDeleted, false);
        if (exportRecordMapper.selectCount(pendingQuery) > 0) {
            return CommonResult.error(EXPORT_PENDING_APPROVAL_REQUIRED);
        }

        // 獲取當前累計匯出數量
        ExportTotalDO total = exportTotalMapper.findByUserId(userId);
        int currentTotal = total != null ? total.getTotalCount() : 0;

        String currentUser = getCurrentUser();
        ExportRecordDO exportRecordDO = new ExportRecordDO();
        exportRecordDO.setUserId(userId);
        exportRecordDO.setRequesterName(requestUser.getNickname());
        exportRecordDO.setExportCount(count);
        exportRecordDO.setRequesterComment(requesterComment);
        exportRecordDO.setCardIds(cardIds);
        exportRecordDO.setGuid(UUID.randomUUID().toString().replace("-", ""));
        exportRecordDO.setExportStatus("UNEXPORTED");
        exportRecordDO.setCreateTime(LocalDateTime.now());
        exportRecordDO.setCreator(currentUser);
        exportRecordDO.setUpdater(currentUser);

        // 超級管理員直接通過
        if (SecurityFrameworkUtils.isAdmin()) {
            exportRecordDO.setStatus("APPROVED");
            exportRecordDO.setApproverId(userId);
            exportRecordDO.setApproverName(requestUser.getNickname());
            exportRecordDO.setApproveTime(LocalDateTime.now());
            exportRecordDO.setApproverComment("超級管理員自動通過");
            updateExportTotal(userId, count, currentTotal, currentUser);
            exportRecordMapper.insert(exportRecordDO);
            return CommonResult.success(exportRecordDO);
        }

        // 累計數量小於閾值直接可以導出，也就是審批通過狀態
        if (currentTotal + count < THRESHOLD) {
            exportRecordDO.setStatus("APPROVED");
            exportRecordDO.setApproverId(null);
            exportRecordDO.setApproverName(null);
            exportRecordDO.setApproveTime(LocalDateTime.now());
            exportRecordDO.setApproverComment("累計數量未達審批閾值");
            updateExportTotal(userId, count, currentTotal, currentUser);
            exportRecordMapper.insert(exportRecordDO);
            return CommonResult.success(exportRecordDO);
        }

        // 發送審批郵件
        AdminUserRespDTO approve = getApprover(requestUser);

        // 需要審批
        exportRecordDO.setStatus("PENDING");
        Long approveId = requestUser.getLeaderId() != null ? requestUser.getLeaderId() : approve.getId();
        exportRecordDO.setApproverId(approveId);
        exportRecordDO.setApproverName(approve.getNickname());
        exportRecordMapper.insert(exportRecordDO);

        // 發送郵件
        MailSendSingleToUserReqDTO reqDTO = new MailSendSingleToUserReqDTO();
        reqDTO.setMail(approve.getEmail());
        reqDTO.setUserId(userId);
        reqDTO.setTemplateCode("test_02");

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("approverName", approve.getNickname());
        templateParams.put("requesterName", requestUser.getNickname());
        templateParams.put("requesterTime", getDateStr());
        templateParams.put("guid", exportRecordDO.getGuid());
        templateParams.put("endTime", afterDays());
        reqDTO.setTemplateParams(templateParams);

        log.info("發送郵件：{}", reqDTO.getMail());
        mailSendApi.sendSingleMailToAdmin(reqDTO);

        return CommonResult.success(exportRecordDO);
    }


    /**
     * 處理匯出審批
     * @param recordId 匯出記錄編號
     * @param approverId 審批人編號
     * @param isApproved 是否通過
     * @param approverComment 審批人備註
     * @return 匯出記錄
     */
    @Override
    public CommonResult<ExportRecordDO> approveExport(Long recordId, Long approverId, boolean isApproved, String approverComment) {
        if (approverComment == null || approverComment.trim().isEmpty()) {
            return CommonResult.error(APPROVER_COMMENT_EMPTY);
        }
        ExportRecordDO record = exportRecordMapper.selectById(recordId);
        if (record == null) {
            return CommonResult.error(EXPORT_RECORD_NOT_FOUND);
        }
        AdminUserRespDTO requester = adminUserApi.getUser(record.getUserId());
        if (requester == null) {
            return CommonResult.error(REQUESTER_NOT_FOUND);
        }
        AdminUserRespDTO approver = adminUserApi.getUser(approverId);
        if (approver == null) {
            return CommonResult.error(APPROVER_NOT_FOUND);
        }

        // 驗證審批權限
        if (!canApprove(requester, approver)) {
            return CommonResult.error(APPROVAL_PERMISSION_DENIED);
        }

        String currentUser = getCurrentUser();

        record.setStatus(isApproved ? "APPROVED" : "REJECTED");
        record.setApproverId(approverId);
        record.setApproveTime(LocalDateTime.now());
        record.setApproverComment(approverComment);
        record.setUpdateTime(LocalDateTime.now());
        record.setUpdater(currentUser);

        if (isApproved) {
            ExportTotalDO total = exportTotalMapper.findByUserId(requester.getId());
            int currentTotal = total != null ? total.getTotalCount() : 0;
            updateExportTotal(requester.getId(), record.getExportCount(), currentTotal, currentUser);
        }

        exportRecordMapper.updateById(record);
        return CommonResult.success(record);
    }

    /**
     * 獲取匯出記錄
     * @param recordId 匯出記錄編號
     * @return 匯出記錄
     */
    @Override
    public ExportRecordDO getExportRecord(Long recordId) {
        return exportRecordMapper.selectById(recordId);
    }

    /**
     * 獲取審批人
     * @param user 申請人
     * @return 審批人
     */
    public AdminUserRespDTO getApprover(AdminUserRespDTO user) {
        if (user.getLeaderId() == null) {
            return adminUserApi.getUser(1L);//假設超級管理員ID為1
        }
        return adminUserApi.getUser(user.getLeaderId());
    }

    /**
     * 驗證審批權限
     * @param requester 申請人
     * @param approver 審批人
     * @return 是否有權審批
     */
    private boolean canApprove(AdminUserRespDTO requester, AdminUserRespDTO approver) {
        if (approver.getId().equals(1L)) {
            return true;
        }
        if (requester.getLeaderId() != null && requester.getLeaderId().equals(approver.getId())) {
            return true;
        }
        return requester.getLeaderId() == null && approver.getId().equals(1L);
    }

    /**
     * 更新累計匯出數量
     * @param userId 使用者編號
     * @param count 本次匯出數量
     * @param currentTotal 當前累計數量
     * @param currentUser 當前使用者
     */
    private void updateExportTotal(Long userId, int count, int currentTotal, String currentUser) {
        ExportTotalDO total = exportTotalMapper.findByUserId(userId);
        if (total == null) {
            total = new ExportTotalDO();
            total.setUserId(userId);
            total.setPeriodStart(truncateTime(new Date()));
            total.setPeriodEnd(truncateTime(new Date()));
            total.setCreator(currentUser);
            total.setUpdater(currentUser);
            total.setCreateTime(LocalDateTime.now());
        }

        total.setTotalCount(currentTotal + count);
        total.setUpdater(currentUser);

        if (total.getId() == null) {
            exportTotalMapper.insert(total);
        } else {
            total.setUpdateTime(LocalDateTime.now());
            exportTotalMapper.updateById(total);
        }
    }

    /**
     * 將日期的時間部分設為零
     * @param date 日期
     * @return 僅保留日期部分的日期
     */
    private Date truncateTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    private String getDateStr() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }

    private String afterDays() {
        // 当前日期
        LocalDate currentDate = LocalDate.now();
        // 当前日期加30天
        LocalDate dateAfter30Days = currentDate.plusDays(30);
        // 日期格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

        // 格式化后的字符串
        String formattedCurrentDate = currentDate.format(formatter);
        String formattedDateAfter30Days = dateAfter30Days.format(formatter);

        System.out.println("当前日期为：" + formattedCurrentDate);
        System.out.println("30天后的日期为：" + formattedDateAfter30Days);
        return formattedDateAfter30Days;
    }

    /**
     * 獲取當前使用者
     * @return 當前使用者標識
     */
    private String getCurrentUser() {
        String userName = SecurityFrameworkUtils.getUserName();
        return StringUtils.isNotBlank(userName) ? userName : "SYSTEM";
    }

}
