package cn.iocoder.yudao.module.pcm.service.tags;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class Test {
    public static void main(String[] args) {
        String fullName = "张伟";

        String lastName;
        String firstName;

        // 假设常见姓氏为1-2个字符
        if (fullName.length() >= 3) {
            // 尝试按复姓（2个字符）拆分
            lastName = fullName.substring(0, 2);
            firstName = fullName.substring(2);
        } else {
            // 单姓（1个字符）
            lastName = fullName.substring(0, 1);
            firstName = fullName.substring(1);
        }

        System.out.println("姓氏: " + lastName);
        System.out.println("名字: " + firstName);
    }
}
