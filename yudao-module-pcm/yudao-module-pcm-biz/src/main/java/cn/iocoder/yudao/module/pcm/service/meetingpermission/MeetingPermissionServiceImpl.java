package cn.iocoder.yudao.module.pcm.service.meetingpermission;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo.MeetingPermissionPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo.MeetingPermissionSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meetingpermission.MeetingPermissionDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.meetingpermission.MeetingPermissionMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.*;

@Service
public class MeetingPermissionServiceImpl implements MeetingPermissionService {

    @Resource
    private MeetingPermissionMapper meetingPermissionMapper;

    /**
     * 分配权限给用户、部门或角色
     *
     * @param meetingId 会议记录ID
     * @param userIds 用户ID列表
     * @param deptIds 部门ID列表
     * @param roleIds 角色ID列表
     */
    @Transactional
    public void assignPermissions(Long meetingId, List<Long> userIds, List<Long> deptIds, List<Long> roleIds) {
        // 删除原有权限
        meetingPermissionMapper.deleteByMeetingId(meetingId);

        // 插入新的权限
        if (userIds != null) {
            userIds.forEach(userId -> meetingPermissionMapper.insert(new MeetingPermissionDO()
                    .setMeetingId(meetingId).setUserId(userId)));
        }
        if (deptIds != null) {
            deptIds.forEach(deptId -> meetingPermissionMapper.insert(new MeetingPermissionDO()
                    .setMeetingId(meetingId).setDeptId(deptId)));
        }
        if (roleIds != null) {
            roleIds.forEach(roleId -> meetingPermissionMapper.insert(new MeetingPermissionDO()
                    .setMeetingId(meetingId).setRoleId(roleId)));
        }
    }

    /**
     * 校验用户是否有权限查看会议记录
     *
     * @param meetingId 会议记录ID
     * @param userId 当前用户ID
     * @param deptId 当前用户所属部门ID
     * @param roleIds 当前用户的角色ID列表
     * @return 是否有权限
     */
    public boolean checkPermission(Long meetingId, Long userId, Long deptId, List<Long> roleIds) {
        // 假设管理员的 userId 为 1 或 roleIds 包含管理员角色 ID 100
        if (userId.equals(1L) || roleIds.contains(100L)) {
            return true;
        }
        return meetingPermissionMapper.hasPermission(meetingId, userId, deptId, roleIds) > 0;
    }

    @Override
    public Long createMeetingPermission(MeetingPermissionSaveReqVO createReqVO) {
        // 插入
        MeetingPermissionDO meetingPermission = BeanUtils.toBean(createReqVO, MeetingPermissionDO.class);
        meetingPermissionMapper.insert(meetingPermission);
        // 返回
        return meetingPermission.getId();
    }

    @Override
    public void updateMeetingPermission(MeetingPermissionSaveReqVO updateReqVO) {
        // 校验存在
        validateMeetingPermissionExists(updateReqVO.getId());
        // 更新
        MeetingPermissionDO updateObj = BeanUtils.toBean(updateReqVO, MeetingPermissionDO.class);
        meetingPermissionMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeetingPermission(Long id) {
        // 校验存在
        validateMeetingPermissionExists(id);
        // 删除
        meetingPermissionMapper.update(Wrappers.<MeetingPermissionDO>lambdaUpdate()
                .set(MeetingPermissionDO::getDeleted, 1)
                .eq(MeetingPermissionDO::getId, id));
    }

    private void validateMeetingPermissionExists(Long id) {
        if (meetingPermissionMapper.selectById(id) == null) {
            throw exception(MEETING_PERMISSION_NOT_EXISTS);
        }
    }

    @Override
    public MeetingPermissionDO getMeetingPermission(Long id) {
        return meetingPermissionMapper.selectById(id);
    }

    @Override
    public MeetingPermissionDO getMeetingPermission(Long meetingId, Long userId, Long deptId, List<Long> roleIds) {
        // 校验用户是否有权限查看会议记录
        if (!checkPermission(meetingId, userId, deptId, roleIds)) {
            throw exception(MEETING_PERMISSION_DENIED);
        }
        return getMeetingPermission(meetingId);
    }

    @Override
    public PageResult<MeetingPermissionDO> getMeetingPermissionPage(MeetingPermissionPageReqVO pageReqVO) {
        return meetingPermissionMapper.selectPage(pageReqVO);
    }

}