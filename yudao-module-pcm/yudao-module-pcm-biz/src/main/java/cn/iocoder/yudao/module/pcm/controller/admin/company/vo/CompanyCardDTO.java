package cn.iocoder.yudao.module.pcm.controller.admin.company.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CompanyCardDTO implements Serializable {

    private Long id;
    private String name;
    private String type;
    private List<CompanyCardDTO> children;

    /**
     * 构造函数
     */
    public CompanyCardDTO() {
        this.children = new ArrayList<>();
    }

    // getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<CompanyCardDTO> getChildren() {
        return children;
    }

    public void setChildren(List<CompanyCardDTO> children) {
        this.children = children;
    }

}