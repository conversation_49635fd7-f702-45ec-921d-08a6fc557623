package cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商谈记录权限分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CardPermissionPageReqVO extends PageParam {

    @Schema(description = "名片ID", example = "13600")
    private Long cardId;

    @Schema(description = "用户ID", example = "15179")
    private Long userId;

    @Schema(description = "有权看的字段")
    private String fieldName; // 以数组格式，存储字段权限

    @Schema(description = "是否可编辑")
    private Boolean isEdit;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}