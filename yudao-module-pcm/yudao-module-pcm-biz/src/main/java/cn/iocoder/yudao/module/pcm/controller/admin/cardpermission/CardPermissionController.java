package cn.iocoder.yudao.module.pcm.controller.admin.cardpermission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pcm.service.cardpermission.CardPermissionService;
import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 名片权限")
@RestController
@RequestMapping("/pcm/permission")
@Validated
public class CardPermissionController {

    @Resource
    private CardPermissionService cardPermissionService;

    @PostMapping("/savePermission")
    @Operation(summary = "保存名片权限")
    public CommonResult<Long> savePermission(@RequestParam("cardId") Long cardId, @RequestBody JSONArray permissionArray) {
        return cardPermissionService.savePermission(cardId, permissionArray);
    }

    @GetMapping("/getCardPermissionByCardId")
    @Operation(summary = "根据名片ID获取权限")
    @Parameter(name = "cardId", description = "名片ID", required = true, example = "1024")
    public CommonResult<JSONArray> getCardPermissionByCardId(@RequestParam("cardId") Long cardId) {
        return success(cardPermissionService.getCardPermissionByCardId(cardId));
    }

}