package cn.iocoder.yudao.module.pcm.dal.dataobject.meetingpermission;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pcm_meeting_permission")
public class MeetingPermissionDO extends BaseDO {

    @TableId
    private Long id;

    private Long meetingId;

    private Long userId;

    private Long deptId;

    private Long roleId;

}