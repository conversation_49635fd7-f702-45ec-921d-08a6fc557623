package cn.iocoder.yudao.module.pcm.service.card;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 名片 Service 接口
 *
 * <AUTHOR>
 */
public interface CardService {

    /**
     * 创建名片
     *
     * @param cardSaveReqVO 创建信息
     * @return 编号
     */
    Long createCard(@Valid CardSaveReqVO cardSaveReqVO);

    /**
     * 更新名片
     *
     * @param updateReqVO 更新信息
     */
    CommonResult<Boolean> updateCard(@Valid CardSaveReqVO updateReqVO);

    /**
     * 编辑
     * @param updateReqVO
     * @return
     */
    CommonResult<Map<String, Object>> saveAndReturnPrevious(CardSaveReqVO updateReqVO);

    /**
     * 删除名片
     *
     * @param id 编号
     */
    void deleteCard(Long id);

    /**
     * 校验是否使用标签
     * @param tagId
     * @return
     */
    Boolean validateTagUsed(Long tagId);

    /**
     * 获得名片
     *
     * @param id 编号
     * @return 名片
     */
    CommonResult<CardRespVO> getCard(Long id);

    /**
     * 获得名片分页
     *
     * @param pageReqVO 分页查询
     * @return 名片分页
     */
    PageResult<CardRespVO> getCardPage(CardPageReqVO pageReqVO);

    /**
     * 获得名片分页
     *
     * @param pageReqVO 分页查询
     * @return 名片分页
     */
    PageResult<CardRespVO> getCardPageByCompanyId(CardPageReqVO pageReqVO);

    /**
     * 获取表字段名
     * @return
     */
    List<Map<String, Object>> getTableFieldNamesWithDesc();

    /**
     * 批量更新字段
     * @param ids 名片ID列表
     * @param fieldName 字段名
     * @param fieldValue 字段值
     */
    void batchUpdateField(List<Long> ids, String fieldName, String fieldValue);

    /**
     * 合并名片逻辑
     * @param ids 名片ID列表，第一个ID为主名片，其余为待合并名片
     */
    void mergeCards(List<Long> ids);

    /**
     * 获取回收站名片分页
     * @param pageReqVO 分页参数
     * @return 回收站名片分页
     */
    PageResult<CardDO> getCardTrash(CardPageReqVO pageReqVO);

    /**
     * 彻底删除
     * @param ids 要彻底删除的名片ID数组
     */
    void permanentlyDelete(List<Long> ids);

    /**
     * 恢复
     * @param ids 要恢复的名片ID数组
     */
    void restore(List<Long> ids);

    /**
     * 批量导入名片
     *
     * @param importCards     导入名片
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    CardImportRespVO importCardList(List<CardImportExcelVO> importCards, boolean isUpdateSupport);

    /**
     * 统计创建者名片数
     * @param companyId 公司ID
     */
    List<Map<String, Object>> countCardsByCreatorInDept(Long companyId);

    /**
     * 根据公司ID获取名片列表
     * @param companyId 公司ID
     * @return 名片列表
     */
    List<CardDO> getCardListByCompanyId(Long companyId);

    /**
     * 根据分组ID获取名片列表
     * @param groupId 分组ID
     * @return 名片列表
     */
    List<Map<String, Object>> selectByGroudId(String groupId);

    /**
     * 获取GroupId去重分页
     * @param pageReqVO 分页参数
     * @return 区域名片分页
     */
    PageResult<Map<String, Object>> getDistrictPage(PcmCardPageReqVO pageReqVO);

    List<Long> getDistrictIds(PcmCardPageReqVO pageReqVO);

    /**
     * 根据用户ID统计名片数
     * @param userId 用户ID
     * @return 名片数
     */
    Long countCardByUserId(Long userId);

    /**
     * 導出名片 Excel
     * @param pageReqVO
     * @return
     */
    PageResult<CardRespVO> exportCardExcel(CardPageReqVO pageReqVO);

    /**
     * 获取上一张名片
     * @param cardId 名片ID
     * @return 上一张名片ID
     */
    Long getPreviousCardId(Long cardId);

    /**
     * 检查名片是否唯一
     * @param cardPageReqVO
     * @return true: 名片已存在; false: 名片不存在
     */
    Long checkCardUnique(CardPageReqVO cardPageReqVO);

    /**
     * 检查名片是否唯一
     * @param cardPageReqVO
     * @return
     */
    List<CardDO> selectUniqueCard(CardPageReqVO cardPageReqVO);

    /**
     * 批量获取名片
     * @param ids
     * @return
     */
    List<CardRespVO> getCardsByIds(List<Long> ids);

}