package cn.iocoder.yudao.module.pcm.controller.admin.tags;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.tags.TagsDO;
import cn.iocoder.yudao.module.pcm.service.tags.TagsService;

@Tag(name = "管理后台 - 标签")
@RestController
@RequestMapping("/pcm/tags")
@Validated
public class TagsController {

    @Resource
    private TagsService tagsService;

    @PostMapping("/create")
    @Operation(summary = "创建标签")
    public CommonResult<Long> createTags(@Valid @RequestBody TagsSaveReqVO createReqVO) {
        return success(tagsService.createTags(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标签")
    public CommonResult<Boolean> updateTags(@Valid @RequestBody TagsSaveReqVO updateReqVO) {
        tagsService.updateTags(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除标签")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteTags(@RequestParam("id") Long id) {
        tagsService.deleteTags(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得标签")
    @Parameter(name = "id", description = "编号", required = true, example = "10000001")
    public CommonResult<TagsRespVO> getTags(@RequestParam("id") Long id) {
        TagsDO tags = tagsService.getTags(id);
        return success(BeanUtils.toBean(tags, TagsRespVO.class));
    }

    @GetMapping("/getTagByCompanyId")
    @Operation(summary = "根据公司ID获得标签")
    @Parameter(name = "companyId", description = "公司ID", required = true, example = "10000001")
    public CommonResult<List<TagsRespVO>> getTagByCompanyId(@RequestParam("companyId") Long companyId) {
        List<TagsDO> pageResult = tagsService.getTagByCompanyId(companyId);
        return success(BeanUtils.toBean(pageResult, TagsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得标签分页")
    public CommonResult<PageResult<TagsRespVO>> getTagsPage(@Valid TagsPageReqVO pageReqVO) {
        PageResult<TagsDO> pageResult = tagsService.getTagsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TagsRespVO.class));
    }

    @GetMapping("/getTagsAll")
    @Operation(summary = "所有标签")
    public CommonResult<PageResult<TagsRespVO>> getTagsAll(@Valid TagsPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);//不分页
        PageResult<TagsDO> pageResult = tagsService.getTagsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TagsRespVO.class));
    }

    @PostMapping("/deleteTagsByIds")
    @Operation(summary = "删除标签")
    @Parameter(name = "ids", description = "编号集合", required = true)
    public CommonResult<Boolean> deleteTagsByIds(@RequestBody JSONObject params) {
        JSONArray jsonArray = params.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return CommonResult.error(403,  "至少需要勾选一个标签");
        }
        List<Long> ids = jsonArray.stream()
                .map(Object::toString)// 先转字符串，避免转换异常
                .map(Long::valueOf)// 转换成 Long 类型
                .collect(Collectors.toList());
        for (Long id : ids) {
            tagsService.deleteTags(id);
        }
        return success(true);
    }

}