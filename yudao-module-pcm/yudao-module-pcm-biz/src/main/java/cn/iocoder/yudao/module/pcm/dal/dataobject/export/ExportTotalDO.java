package cn.iocoder.yudao.module.pcm.dal.dataobject.export;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

@TableName("pcm_card_export_totals")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportTotalDO {

    /**
     * 累計記錄編號
     */
    private Long id;

    /**
     * 使用者編號
     */
    private Long userId;

    /**
     * 30天內累計匯出名片數量
     */
    private Integer totalCount;

    /**
     * 統計週期開始日期
     */
    private Date periodStart;

    /**
     * 統計週期結束日期
     */
    private Date periodEnd;

    /**
     * 建立時間
     */
    private LocalDateTime createTime;

    /**
     * 更新時間
     */
    private LocalDateTime updateTime;

    /**
     * 創建者
     */
    private String creator;

    /**
     * 更新者
     *
     */
    private String updater;

    /**
     * 是否刪除
     */
    private Boolean deleted;

}
