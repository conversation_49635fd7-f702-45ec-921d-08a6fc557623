package cn.iocoder.yudao.module.pcm.service.meeting;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingRespVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meeting.MeetingDO;
import com.alibaba.fastjson.JSONObject;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 商谈记录 Service 接口
 *
 * <AUTHOR>
 */
public interface MeetingService {

    /**
     * 创建商谈记录表，存储名片相关的商谈记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeeting(@Valid MeetingSaveReqVO createReqVO);

    /**
     * 更新商谈记录表，存储名片相关的商谈记录
     *
     * @param updateReqVO 更新信息
     */
    void updateMeeting(@Valid MeetingSaveReqVO updateReqVO);

    /**
     * 删除商谈记录表，存储名片相关的商谈记录
     *
     * @param id 编号
     */
    void deleteMeeting(Long id);

    /**
     * 获得商谈记录表，存储名片相关的商谈记录
     *
     * @param id 编号
     * @return 商谈记录表，存储名片相关的商谈记录
     */
    MeetingDO getMeeting(Long id);

    /**
     * 获得商谈记录表，存储名片相关的商谈记录分页
     *
     * @param pageReqVO 分页查询
     * @return 商谈记录表，存储名片相关的商谈记录分页
     */
    CommonResult<PageResult<MeetingRespVO>> getMeetingPage(MeetingPageReqVO pageReqVO);

    /**
     * 根据cardId获得商谈记录
     *
     * @param cardId cardId
     * @return 商谈记录
     */
    PageResult<MeetingDO> getMeetingByCardId(Long cardId);

    /**
     * 根据用户ID统计商谈记录数
     *
     * @param params 用户ID
     * @return 商谈记录数
     */
    Map<String, Object> countMeetingByUserId(JSONObject params);

    /**
     * 根据cardId删除商谈记录
     *
     * @param cardId cardId
     */
    void deleteMeetingByCardId(Long cardId);

    /**
     * 批量彻底删除商谈记录
     * @param cardIds
     */
    void permanentlyDeleteMeeting(List<Long> cardIds);

    /**
     * 批量恢复商谈记录
     * @param cardIds
     */
    void restoreMeeting(List<Long> cardIds);
}