package cn.iocoder.yudao.module.pcm.service.company;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.pcm.controller.admin.company.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.company.CompanyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 公司 Service 接口
 *
 * <AUTHOR>
 */
public interface CompanyService {

    /**
     * 创建公司
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCompany(@Valid CompanySaveReqVO createReqVO);

    /**
     * 更新公司
     *
     * @param updateReqVO 更新信息
     */
    void updateCompany(@Valid CompanySaveReqVO updateReqVO);

    /**
     * 更新或插入公司记录（upsert）。
     *
     * @param id      公司ID，可为空（为空时表示新增）。
     * @param orgName 公司名称，不能为空或仅包含空白字符。
     * @return 公司ID（更新或插入后的ID）。
     */
    Long upsertCompany(Long id, String orgName);

    /**
     * 删除公司
     *
     * @param id 编号
     */
    void deleteCompany(Long id);

    /**
     * 物理刪除
     * @param id
     */
    void delete(Long id);

    /**
     * 获得公司
     *
     * @param id 编号
     * @return 公司
     */
    CompanyDO getCompany(Long id);

    /**
     * 获得公司分页
     *
     * @param pageReqVO 分页查询
     * @return 公司分页
     */
    CommonResult<PageResult<CompanyRespVO>> getCompanyPage(CompanyPageReqVO pageReqVO);

    /**
     * 根据公司名称获取公司
     *
     * @param name 公司名称
     * @return 公司
     */
    CompanyDO getCompanyByName(String name);

    /**
     * 获取公司及其所有子机构
     * @param id 公司ID
     * @return 公司及其子机构树形结构
     */
    CompanyCardDTO getCompanyTreeWithCards(Long id);

    /**
     * 获取所有公司
     * @return 所有公司列表
     */
    CommonResult<List<CompanySimpleRespVO>> getCompanyList(String type);

}