package cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 商谈记录权限 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeetingPermissionRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31215")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "会议记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13600")
    @ExcelProperty("会议记录ID")
    private Long meetingId;

    @Schema(description = "用户ID", example = "15179")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "部门ID", example = "6958")
    @ExcelProperty("部门ID")
    private Long deptId;

    @Schema(description = "角色ID", example = "11906")
    @ExcelProperty("角色ID")
    private Long roleId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}