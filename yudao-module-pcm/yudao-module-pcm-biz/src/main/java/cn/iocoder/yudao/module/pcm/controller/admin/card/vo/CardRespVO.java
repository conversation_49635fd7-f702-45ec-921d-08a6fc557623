package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.TagsRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 名片 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CardRespVO {

    @Schema(description = "名片ID")
    private Long id;

    @Schema(description = "分组ID,用于合并名片")
    private String groupId;

    @Schema(description = "Title (Mr, Ms, Mrs, Others)")
    @ExcelProperty("Title")
    private String titleEn;

    @Schema(description = "First Name (e.g. Tai-man)")
    @ExcelProperty("First Name")
    private String firstNameEn;

    @Schema(description = "Last Name (e.g. CHAN)")
    @ExcelProperty("Last Name")
    private String lastNameEn;

    @Schema(description = "Honour (e.g. MH, JP)")
    @ExcelProperty("Honour")
    private String honourEn;

    @Schema(description = "稱謂（先生/ 小姐/ 女士/其他）")
    @ExcelProperty("稱謂")
    private String title;

    @Schema(description = "姓氏")
    @ExcelProperty("姓氏")
    private String firstName;

    @Schema(description = "名字")
    @ExcelProperty("名字")
    private String lastName;

    @Schema(description = "勳銜")
    @ExcelProperty("勳銜")
    private String honour;

    @Schema(description = "全名")
    private String fullName;

    @Schema(description = "暱稱")
    @ExcelProperty("暱稱")
    private String nickName;

    @Schema(description = "ID of Organisation")
    private Long companyIdEn;

    @Schema(description = "Name of Organisation")
    @ExcelProperty("Name of Organisation")
    private String companyNameEn;

    @Schema(description = "Department")
    @ExcelProperty("Department")
    private String departmentEn;

    @Schema(description = "Job Title")
    @ExcelProperty("Job Title")
    private String jobTitleEn;

    @Schema(description = "機構ID")
    private Long companyId;

    @Schema(description = "機構名稱")
    @ExcelProperty("機構名稱")
    private String companyName;

    @Schema(description = "部門")
    @ExcelProperty("部門")
    private String department;

    @Schema(description = "職位")
    @ExcelProperty("職位")
    private String jobTitle;

    @Schema(description = "流動電話")
    @ExcelProperty("流動電話")
    private String phoneMobile;

    @Schema(description = "流動電話區碼")
    private String phoneMobileAreaCode;

    @Schema(description = "內地號碼")
    @ExcelProperty("內地號碼")
    private String phoneMainland;

    @Schema(description = "內地號碼區碼")
    @ExcelProperty("內地號碼區碼")
    private String phoneMainlandAreaCode;

    @Schema(description = "電郵")
    @ExcelProperty("電郵")
    private String email;

    @Schema(description = "電郵1")
    @ExcelProperty("電郵1")
    private String email1;

    @Schema(description = "電郵2")
    @ExcelProperty("電郵2")
    private String email2;

    @Schema(description = "電話（辦公室）")
    @ExcelProperty("辦公室電話")
    private String phoneOffice;

    @Schema(description = "辦公電話區碼")
    @ExcelProperty("辦公電話區碼")
    private String phoneOfficeAreaCode;

    @Schema(description = "電話（直線）")
    @ExcelProperty("直線電話")
    private String phoneDirectLine;

    @Schema(description = "直線電話區號")
    @ExcelProperty("直線電話區號")
    private String phoneDirectLineAreaCode;

    @Schema(description = "傳真")
    @ExcelProperty("傳真")
    private String faxNumber;

    @Schema(description = "address line 1")
    @ExcelProperty("英文地址1")
    private String addressLine1En;

    @Schema(description = "address line 2")
    @ExcelProperty("英文地址2")
    private String addressLine2En;

    @Schema(description = "district(e.g. Tsim Sha Tsui)")
    @ExcelProperty("英文地區")
    private String districtEn;

    @Schema(description = "area(e.g. Kowloon)")
    @ExcelProperty("英文區域")
    private String areaEn;

    @Schema(description = "country")
    @ExcelProperty("英文國家")
    private String countryEn;

    @Schema(description = "地址1")
    @ExcelProperty("地址1")
    private String addressLine1;

    @Schema(description = "地址2")
    @ExcelProperty("地址2")
    private String addressLine2;

    @Schema(description = "地區")
    @ExcelProperty("地區")
    private String district;

    @Schema(description = "區域")
    @ExcelProperty("區域")
    private String area;

    @Schema(description = "国家")
    @ExcelProperty("國家")
    private String country;

    @Schema(description = "省")
    @ExcelProperty("省")
    private String province;

    @Schema(description = "城市")
    @ExcelProperty("城市")
    private String city;

    @Schema(description = "(e.g. Technology, Healthcare) 行業類型")
    @ExcelProperty("行業類型")
    private String industryType;

    @Schema(description = "業務類型（政府或相關組織、企業、基金會、非政府組織、其他）")
    private String businessType;

    @Schema(description = "其他業務類型")
    private String otherBusiness;

    @Schema(description = "linkedin profile url")
    @ExcelProperty("linkedin profile url")
    private String linkedinProfileUrl;

    @Schema(description = "facebook page url")
    @ExcelProperty("facebook page url")
    private String facebookPageUrl;

    @Schema(description = "instagram url")
    @ExcelProperty("instagram url")
    private String instagramUrl;

    @Schema(description = "wechat id")
    @ExcelProperty("wechat id")
    private String wechatId;

    @Schema(description = "會面詳情")
    private String communicationHistory;

    @Schema(description = "標籤")
    private String customTags;

    @Schema(description = "狀態（Active:活躍，Inactive:不活躍）")
    @ExcelProperty("狀態")
    private String status;

    @Schema(description = "備註")
    @ExcelProperty("備註")
    private String notes;

    @Schema(description = "簡介")
    @ExcelProperty("簡介")
    private String description;

    @Schema(description = "图片头像地址")
    @ExcelProperty("名片圖片")
    private String imageUrl;

    @Schema(description = "反面名片的URL")
    @ExcelProperty("反面圖片")
    private String backUrl;

    private String address;
    private String socialMedia;

    /**
     * 有权查看的同事
     */
    private String userId;
    /**
     * 有权查看的部门
     */
    private String deptId;

    @Schema(description = "网址")
    @ExcelProperty("网址")
    private String website;

    @Schema(description = "即时消息")
    private String instantMessaging;

    @Schema(description = "纪念日")
    private String anniversary;

    @Schema(description = "交换日期")
    private String exchangeDate;

    @Schema(description = "是否双面：true 为双面，false 为单面")
    private Boolean doubleSided;

    @Schema(description = "創建時間")
    private LocalDateTime createTime;

    @Schema(description = "更新時間")
    private LocalDateTime updateTime;

    @Schema(description = "創建者ID")
    private Long createUserId;

    @Schema(description = "創建者")
    private String creator;

    @Schema(description = "最後更新者")
    private String updater;

    @Schema(description = "是否删除")
    private Boolean deleted;

    private List<TagsRespVO> tags;//关联的标签表

    private JSONArray users;

    private JSONArray depts;

    /**
     * 是否可编辑名片：1 可编辑, 0 不可编辑
     */
    private Boolean isEdit;


}
