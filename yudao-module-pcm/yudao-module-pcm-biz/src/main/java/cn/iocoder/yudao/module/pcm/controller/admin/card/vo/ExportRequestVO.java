package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 導出申請請求 VO
 */
@Data
public class ExportRequestVO {

    @NotEmpty(message = "申請人備註不可為空")
    private String requesterComment; // 申請人備註

    @NotNull(message = "查詢條件不可為空")
    private CardPageReqVO pageReqVO; // 原查詢條件

    private String guid; // 權限驗證唯一標識

}
