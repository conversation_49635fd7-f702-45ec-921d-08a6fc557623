package cn.iocoder.yudao.module.pcm.service.meeting;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingRespVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission.CardPermissionDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meeting.MeetingDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.card.CardMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.cardpermission.CardPermissionMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.meeting.MeetingMapper;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.CARD_NOT_EXISTS;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.MEETING_NOT_EXISTS;

/**
 * 商谈记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeetingServiceImpl implements MeetingService {

    private static final Logger log = LoggerFactory.getLogger(MeetingServiceImpl.class);

    @Resource
    private MeetingMapper meetingMapper;

    @Resource
    private CardMapper cardMapper;

    @Resource
    private CardPermissionMapper cardPermissionMapper;

    @Override
    public Long createMeeting(MeetingSaveReqVO createReqVO) {
        CardDO cardDO = cardMapper.selectById(createReqVO.getCardId());
        if (cardDO == null) {
            throw exception(CARD_NOT_EXISTS);
        }
        // 插入
        MeetingDO meeting = BeanUtils.toBean(createReqVO, MeetingDO.class);
        meeting.setUserName(cardDO.getFullName());
        meeting.setJobTitle(cardDO.getJobTitle());
        meeting.setCreateUserId(SecurityFrameworkUtils.getLoginUserId());
        meeting.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        meeting.setDeptId(String.valueOf(SecurityFrameworkUtils.getLoginUserDeptId()));
        meeting.setCreateTime(LocalDateTime.now());
        meeting.setUpdateTime(LocalDateTime.now());
        meetingMapper.insert(meeting);
        // 返回
        return meeting.getId();
    }

    @Override
    public void updateMeeting(MeetingSaveReqVO updateReqVO) {
        // 校验存在
        validateMeetingExists(updateReqVO.getId());
        // 更新
        MeetingDO updateObj = BeanUtils.toBean(updateReqVO, MeetingDO.class);
        meetingMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeeting(Long id) {
        // 校验存在
        validateMeetingExists(id);
        // 删除
        meetingMapper.update(Wrappers.<MeetingDO>lambdaUpdate()
                .set(MeetingDO::getDeleted, 1)
                .eq(MeetingDO::getId, id));
    }

    private void validateMeetingExists(Long id) {
        if (meetingMapper.selectById(id) == null) {
            throw exception(MEETING_NOT_EXISTS);
        }
    }

    @Override
    public MeetingDO getMeeting(Long id) {
        return meetingMapper.selectById(id);
    }

    @Override
    public CommonResult<PageResult<MeetingRespVO>> getMeetingPage(MeetingPageReqVO pageReqVO) {

        try {
            Long userId = SecurityFrameworkUtils.getLoginUserId();
            if (userId == null) {
                return CommonResult.error(400, "用戶未登錄");
            }

            // 初始化分页结果，防止空指针
            PageResult<MeetingDO> meetingDOPageResult = new PageResult<>(Collections.emptyList(), 0L);
            boolean isAdmin = SecurityFrameworkUtils.isAdmin();
            Long cardId = pageReqVO.getCardId();

            if (!isAdmin) {
                // 非管理员默认查询自己的会议记录
                if (pageReqVO.getCreateUserId() == null) {
                    pageReqVO.setCreateUserId(userId);
                }

                // 执行分页查询
                meetingDOPageResult = meetingMapper.selectPage(pageReqVO);

                // 如果未提供 cardId，则直接返回
                if (cardId == null) {
                    return CommonResult.success(BeanUtils.toBean(meetingDOPageResult, MeetingRespVO.class));
                }

                // 检查卡片权限
                CardDO cardDO = cardMapper.selectById(cardId);
                if (cardDO == null || StringUtils.isBlank(cardDO.getUserId())) {
                    return CommonResult.success(BeanUtils.toBean(meetingDOPageResult, MeetingRespVO.class));
                }

                // 使用 Set 优化权限检查
                Set<Long> permittedUserIds = Arrays.stream(cardDO.getUserId().split(";"))
                        .map(Long::parseLong)
                        .collect(Collectors.toSet());
                if (!permittedUserIds.contains(userId)) {
                    return CommonResult.success(BeanUtils.toBean(meetingDOPageResult, MeetingRespVO.class));
                }

                // 检查卡片权限字段
                List<CardPermissionDO> cardPermissions = cardPermissionMapper.getByCardIdAndUserId(cardId, userId);
                boolean hasCommunicationHistory = cardPermissions.stream()
                        .filter(perm -> StringUtils.isNotBlank(perm.getFieldName()))
                        .flatMap(perm -> {
                            try {
                                return JSON.parseArray(perm.getFieldName(), String.class).stream();
                            } catch (Exception e) {
                                log.error("解析名片權限字段失敗, cardId: {}, userId: {}", cardId, userId, e);
                                return Stream.empty();
                            }
                        })
                        .anyMatch("communicationHistory"::equals);

                // 如果有通信历史权限，合并共享会议
                if (hasCommunicationHistory) {
                    List<MeetingDO> shareList = meetingMapper.getMeetingByCardId(cardId);
                    List<MeetingDO> mergedList = new ArrayList<>(meetingDOPageResult.getList());
                    mergedList.addAll(shareList);
                    meetingDOPageResult.setList(mergedList);
                    meetingDOPageResult.setTotal(meetingDOPageResult.getTotal() + shareList.size());
                }
            } else {
                // 管理员直接查询所有会议
                meetingDOPageResult = meetingMapper.selectPage(pageReqVO);
            }

            return CommonResult.success(BeanUtils.toBean(meetingDOPageResult, MeetingRespVO.class));
        } catch (IllegalStateException e) {
            return CommonResult.error(400, e.getMessage());
        }
    }

    @Override
    public PageResult<MeetingDO> getMeetingByCardId(Long cardId) {
        boolean admin = SecurityFrameworkUtils.isAdmin();
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        MeetingPageReqVO pageReqVO = new MeetingPageReqVO();
        pageReqVO.setCardId(cardId);
        PageResult<MeetingDO> meetingList = meetingMapper.selectPage(pageReqVO);

        if (meetingList == null || meetingList.getList().isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 檢查是否為自己的商談記錄
        boolean isMine = meetingList.getList().stream()
                .anyMatch(meetingDO -> loginUserId.equals(meetingDO.getCreateUserId()));

        // 如果是admin或自己的數據，直接返回
        if (admin || isMine) {
            return meetingList;
        }

        // 非admin且非自己的數據，進行權限校驗
        LambdaQueryWrapperX<CardPermissionDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CardPermissionDO::getCardId, cardId)
                .eq(CardPermissionDO::getUserId, loginUserId)
                .eq(CardPermissionDO::getDeleted, 0);
        CardPermissionDO cardPermissionDO = cardPermissionMapper.selectOne(wrapper);

        // 檢查 fieldName 是否包含 communicationHistory
        boolean hasCommunicationHistory = false;
        if (cardPermissionDO != null && cardPermissionDO.getFieldName() != null) {
            String fieldName = cardPermissionDO.getFieldName();
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                List<String> fieldList = objectMapper.readValue(fieldName, new TypeReference<List<String>>() {});
                hasCommunicationHistory = fieldList.contains("communicationHistory");
            } catch (JsonProcessingException e) {
                log.error("解析 fieldName 失敗", e);
            }
        }

        // 無權限則返回空結果
        if (!hasCommunicationHistory) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        return meetingList;
    }

    @Override
    public Map<String, Object> countMeetingByUserId(JSONObject params) {

        Long userId = params.getLong("userId");
        Long cardId = params.getLong("cardId");
        LambdaQueryWrapperX<MeetingDO> mineWrapper = new LambdaQueryWrapperX<>();
        mineWrapper.eq(MeetingDO::getDeleted, 0)
                .eq(MeetingDO::getCreateUserId, userId)
                .eqIfPresent(MeetingDO::getCardId, cardId);

        Long userCount = meetingMapper.selectCount(mineWrapper);

        LambdaQueryWrapperX<MeetingDO> allWrapper = new LambdaQueryWrapperX<>();
        allWrapper.eq(MeetingDO::getDeleted, 0);

        Long allCount = meetingMapper.selectCount(allWrapper);

        Map<String, Object> map = new HashMap<>();
        map.put("userCount", userCount);
        map.put("allCount", allCount);
        return map;
    }

    @Override
    public void deleteMeetingByCardId(Long cardId) {
        // 删除
        int rows = meetingMapper.update(Wrappers.<MeetingDO>lambdaUpdate()
                .set(MeetingDO::getDeleted, 1)
                .eq(MeetingDO::getCardId, cardId));
        log.info("删除了{}条数据商谈记录", rows);
    }

    @Override
    public void permanentlyDeleteMeeting(List<Long> cardIds) {
        int rows = meetingMapper.delete(Wrappers.<MeetingDO>lambdaQuery()
                .in(MeetingDO::getCardId, cardIds));
        log.info("彻底删除了{}条数据商谈记录", rows);
    }

    @Override
    public void restoreMeeting(List<Long> cardIds) {
        //批量更新 deleted 字段为 0 （未删除状态）
        int rows = meetingMapper.update(Wrappers.<MeetingDO>lambdaUpdate()
                .set(MeetingDO::getDeleted, 0)// 设置删除状态为0（未删除）
                .in(MeetingDO::getCardId, cardIds));// 设置要更新的ID列表
        log.info("恢复{}条数据商谈记录", rows);
    }


}