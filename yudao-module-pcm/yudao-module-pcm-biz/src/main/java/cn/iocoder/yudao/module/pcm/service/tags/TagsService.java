package cn.iocoder.yudao.module.pcm.service.tags;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.tags.TagsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 标签 Service 接口
 *
 * <AUTHOR>
 */
public interface TagsService {

    /**
     * 创建标签
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTags(@Valid TagsSaveReqVO createReqVO);

    /**
     * 更新标签
     *
     * @param updateReqVO 更新信息
     */
    void updateTags(@Valid TagsSaveReqVO updateReqVO);

    /**
     * 删除标签
     *
     * @param id 编号
     */
    void deleteTags(Long id);

    /**
     * 获得标签
     *
     * @param id 编号
     * @return 标签
     */
    TagsDO getTags(Long id);

    List<TagsDO> getTagByCompanyId(Long companyId);

    /**
     * 获得标签分页
     *
     * @param pageReqVO 分页查询
     * @return 标签分页
     */
    PageResult<TagsDO> getTagsPage(TagsPageReqVO pageReqVO);

}