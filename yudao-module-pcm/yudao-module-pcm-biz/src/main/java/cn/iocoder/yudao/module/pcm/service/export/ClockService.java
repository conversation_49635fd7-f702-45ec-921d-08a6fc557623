package cn.iocoder.yudao.module.pcm.service.export;

import cn.iocoder.yudao.module.pcm.dal.dataobject.export.ExportTotalDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.export.ExportTotalMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 定時服務，每天清理過期的匯出記錄和累計數據
 */
@Component
public class ClockService {

    private static final Logger log = LoggerFactory.getLogger(ClockService.class);
    @Autowired
    private ExportTotalMapper exportTotalMapper;

    /**
     * 每天00:00執行，更新所有使用者的累計匯出數量
     */
    @Scheduled(cron = "0 0 0 * * ?")
//    @Scheduled(cron = "0 * * * * ?")
    public void cleanExpiredRecords() {
        // 計算 30 天前的時間點
        LocalDateTime thirtyDaysAgo = LocalDateTime.now(ZoneId.systemDefault()).minusDays(30);
        log.info("開始清理過期匯出總數記錄，截止時間：{}", thirtyDaysAgo);

        // 構建刪除條件：create_time < 30 天前
        LambdaQueryWrapper<ExportTotalDO> queryWrapper = new LambdaQueryWrapper<ExportTotalDO>()
                .lt(ExportTotalDO::getCreateTime, thirtyDaysAgo);

        // 執行刪除
        int deletedRows = exportTotalMapper.delete(queryWrapper);
        log.info("清理過期匯出總數記錄完成，受影響行數：{}", deletedRows);
    }

}
