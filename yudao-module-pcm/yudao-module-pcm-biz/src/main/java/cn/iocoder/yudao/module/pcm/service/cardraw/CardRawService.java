package cn.iocoder.yudao.module.pcm.service.cardraw;

import cn.iocoder.yudao.module.pcm.controller.admin.cardraw.vo.CardRawSaveReqVO;

import javax.validation.Valid;

/**
 * 識別名片的原始記錄 Service 接口
 */
public interface CardRawService {

    /**
     * 创建名片原始記錄
     *
     * @param cardSaveReqVO 创建信息
     * @return 编号
     */
    Long createCard(@Valid CardRawSaveReqVO cardSaveReqVO);

    /**
     * 更新名片原始記錄
     *
     * @param updateReqVO 更新信息
     */
    void updateCard(@Valid CardRawSaveReqVO updateReqVO);

    /**
     * 删除名片原始記錄
     *
     * @param id 编号
     */
    void deleteCard(Long id);

}