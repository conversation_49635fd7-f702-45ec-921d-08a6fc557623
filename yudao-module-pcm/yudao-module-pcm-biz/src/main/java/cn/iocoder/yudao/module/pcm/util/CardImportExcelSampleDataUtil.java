package cn.iocoder.yudao.module.pcm.util;

import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardImportExcelVO;

import java.util.Arrays;
import java.util.List;

public class CardImportExcelSampleDataUtil {

    /**
     * 生成 CardImportExcelVO 示例数据
     */
    public static List<CardImportExcelVO> generateSampleCards() {
        CardImportExcelVO card1 = CardImportExcelVO.builder()
                .firstName("Jones")
                .lastName("Michael")
                .nickName("MJ")
                .customTags("Tech,AI")
                .companyName("INTSIG Information Co.,Ltd")
                .department("R&D")
                .jobTitle("Senior Engineer")
                .email("<EMAIL>")
                .title("Mr")
                .honour("PhD")
                .titleEn("Mr")
                .firstNameEn("Jones")
                .lastNameEn("<PERSON>")
                .honourEn("PhD")
                .companyNameEn("INTSIG Information")
                .departmentEn("Research and Development")
                .jobTitleEn("Senior Engineer")
                .phoneMobile("+852 9123 4567")
                .email2("<EMAIL>")
                .phoneOffice("+852 2233 4455")
                .phoneDirectLine("+852 9988 7766")
                .faxNumber("+852 3344 5566")
                .addressLine1En("123 Innovation Road")
                .addressLine2En("Science Park")
                .districtEn("Sha Tin")
                .areaEn("New Territories")
                .countryEn("Hong Kong")
                .addressLine1("創新道123號")
                .addressLine2("科學園")
                .address("新界沙田創新道123號科學園")
                .district("沙田")
                .area("新界")
                .country("中國香港")
                .city("香港")
                .industryType("信息技術")
                .businessType("人工智能")
                .otherBusiness("數據分析")
                .linkedinProfileUrl("https://www.linkedin.com/in/mjones")
                .facebookPageUrl("https://facebook.com/michael.jones")
                .instagramUrl("https://instagram.com/mj_ai_life")
                .wechatId("mjones_ai")
                .socialMedia("LinkedIn, Facebook, Instagram, WeChat")
                .communicationHistory("2024年12月於科技博覽會首次會面，表現出合作意願")
                .build();

        CardImportExcelVO card2 = CardImportExcelVO.builder()
                .firstName("Chen")
                .lastName("Lily")
                .nickName("Lils")
                .customTags("Finance,Strategy")
                .companyName("SunTech Financial")
                .department("Finance")
                .jobTitle("CFO")
                .email("<EMAIL>")
                .title("Ms")
                .honour("MBA")
                .titleEn("Ms")
                .firstNameEn("Lily")
                .lastNameEn("Chen")
                .honourEn("MBA")
                .companyNameEn("SunTech Financial")
                .departmentEn("Finance")
                .jobTitleEn("Chief Financial Officer")
                .phoneMobile("+86 13900001111")
                .email2("<EMAIL>")
                .phoneOffice("+86 10 8888 8888")
                .phoneDirectLine("+86 10 9999 9999")
                .faxNumber("+86 10 7777 6666")
                .addressLine1En("88 Fortune St")
                .addressLine2En("Finance District")
                .districtEn("Chaoyang")
                .areaEn("Beijing")
                .countryEn("China")
                .addressLine1("财富街88號")
                .addressLine2("金融區")
                .address("北京市朝陽區金融區财富街88號")
                .district("朝陽")
                .area("北京")
                .country("中國")
                .city("北京")
                .industryType("金融")
                .businessType("投資管理")
                .otherBusiness("企業融資")
                .linkedinProfileUrl("https://linkedin.com/in/lilychen")
                .facebookPageUrl("")
                .instagramUrl("")
                .wechatId("lilyc_88")
                .socialMedia("LinkedIn, WeChat")
                .communicationHistory("2025年3月拜訪客戶，洽談財務合作")
                .build();

        CardImportExcelVO card3 = CardImportExcelVO.builder()
                .firstName("Wong")
                .lastName("Andy")
                .nickName("AW")
                .customTags("Startup,Blockchain")
                .companyName("ChainWave Tech")
                .department("Innovation")
                .jobTitle("CTO")
                .email("<EMAIL>")
                .title("Dr")
                .honour("PhD")
                .titleEn("Dr")
                .firstNameEn("Andy")
                .lastNameEn("Wong")
                .honourEn("PhD")
                .companyNameEn("ChainWave Technologies")
                .departmentEn("Innovation Department")
                .jobTitleEn("Chief Technology Officer")
                .phoneMobile("+65 9123 3210")
                .email2("<EMAIL>")
                .phoneOffice("+65 6234 5678")
                .phoneDirectLine("+65 6543 2109")
                .faxNumber("")
                .addressLine1En("12 Tech Crescent")
                .addressLine2En("Startup Hub")
                .districtEn("One North")
                .areaEn("Singapore")
                .countryEn("Singapore")
                .addressLine1("科技新月12號")
                .addressLine2("創業園區")
                .address("新加坡One North創業園區科技新月12號")
                .district("One North")
                .area("新加坡")
                .country("新加坡")
                .city("新加坡")
                .industryType("區塊鏈")
                .businessType("技術研發")
                .otherBusiness("加密技術")
                .linkedinProfileUrl("https://linkedin.com/in/andywong")
                .facebookPageUrl("")
                .instagramUrl("https://instagram.com/andyblockchain")
                .wechatId("andy_w_cto")
                .socialMedia("LinkedIn, Instagram, WeChat")
                .communicationHistory("2025年1月於新加坡創業論壇交流")
                .build();

        return Arrays.asList(card1, card2, card3);
    }

}
