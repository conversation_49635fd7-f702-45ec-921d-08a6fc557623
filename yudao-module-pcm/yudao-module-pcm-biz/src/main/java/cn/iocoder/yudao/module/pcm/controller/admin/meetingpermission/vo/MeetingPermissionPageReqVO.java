package cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商谈记录权限分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeetingPermissionPageReqVO extends PageParam {

    @Schema(description = "会议记录ID", example = "13600")
    private Long meetingId;

    @Schema(description = "用户ID", example = "15179")
    private Long userId;

    @Schema(description = "部门ID", example = "6958")
    private Long deptId;

    @Schema(description = "角色ID", example = "11906")
    private Long roleId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}