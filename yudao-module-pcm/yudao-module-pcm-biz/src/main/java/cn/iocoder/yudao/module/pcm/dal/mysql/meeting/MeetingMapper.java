package cn.iocoder.yudao.module.pcm.dal.mysql.meeting;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingPageReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission.CardPermissionDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meeting.MeetingDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商谈记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeetingMapper extends BaseMapperX<MeetingDO> {

    default PageResult<MeetingDO> selectPage(MeetingPageReqVO reqVO) {

        LambdaQueryWrapperX<MeetingDO> wrapper = new LambdaQueryWrapperX<>();
        if (StringUtils.isNotBlank(reqVO.getKeyword())) {
            wrapper.and(wrapper1 -> wrapper1
                    .or(w -> w.like(MeetingDO::getUserName, reqVO.getKeyword()))
                    .or(w -> w.like(MeetingDO::getTitle, reqVO.getKeyword()))
                    .or(w -> w.like(MeetingDO::getJobTitle, reqVO.getKeyword()))
                    .or(w -> w.like(MeetingDO::getContent, reqVO.getKeyword()))
            );
        }

        // 处理创建人ID
        wrapper.eqIfPresent(MeetingDO::getCreateUserId, reqVO.getCreateUserId())
                .eqIfPresent(MeetingDO::getCardId, reqVO.getCardId());

        // 下属的商谈记录
        if (StringUtils.isNotBlank(reqVO.getSubordinateId())) {
            wrapper.inIfPresent(MeetingDO::getCreateUserId, reqVO.getSubordinateId().split(";"));
        }

        wrapper.orderByDesc(MeetingDO::getUpdateTime);
        wrapper.eq(MeetingDO::getDeleted, 0);
        return selectPage(reqVO, wrapper);
    }

    default List<MeetingDO> getMeetingByCardId(Long cardId) {
        return selectList(MeetingDO::getCardId, cardId);
    }

}