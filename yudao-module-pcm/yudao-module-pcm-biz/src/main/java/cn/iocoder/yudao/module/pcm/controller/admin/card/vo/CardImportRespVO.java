package cn.iocoder.yudao.module.pcm.controller.admin.card.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理後臺 - 名片導入 Response VO")
@Data
@Builder
public class CardImportRespVO {

    @Schema(description = "創建成功的名片數組", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> createCardNames;

    @Schema(description = "更新成功的名片數組", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> updateCardNames;

    @Schema(description = "導入失敗的名片集合，key 爲用名字，value 爲失敗原因", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, String> failureCardNames;

}
