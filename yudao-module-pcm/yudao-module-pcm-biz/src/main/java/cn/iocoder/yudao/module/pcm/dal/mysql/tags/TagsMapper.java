package cn.iocoder.yudao.module.pcm.dal.mysql.tags;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pcm.dal.dataobject.tags.TagsDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.*;

/**
 * 标签 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TagsMapper extends BaseMapperX<TagsDO> {

    default PageResult<TagsDO> selectPage(TagsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TagsDO>()
                .eqIfPresent(TagsDO::getCompanyId, reqVO.getCompanyId())
                .likeIfPresent(TagsDO::getName, reqVO.getName())
                .eqIfPresent(TagsDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(TagsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TagsDO::getId));
    }

    // 添加: 根据 companyId 查询标签列表
    default List<TagsDO> selectListByCompanyId(Long companyId) {
        return selectList(new LambdaQueryWrapperX<TagsDO>().eq(TagsDO::getCompanyId, companyId));
    }

    default List<TagsDO> selectTagsByIds(List<Long> ids) {
        return selectList(new LambdaQueryWrapperX<TagsDO>()
                .eq(TagsDO::getDeleted, false)
                .in(TagsDO::getId, ids));
    }


}