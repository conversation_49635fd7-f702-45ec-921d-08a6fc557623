package cn.iocoder.yudao.module.pcm.controller.admin.company.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理後臺 - 公司精簡信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanySimpleRespVO {

    @Schema(description = "公司編號", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Long id;

    @Schema(description = "公司名稱", requiredMode = Schema.RequiredMode.REQUIRED, example = "香港青年協會")
    private String name;

    @Schema(description = "父公司 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Long parentId;

}
