package cn.iocoder.yudao.module.pcm.controller.admin.meeting;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingRespVO;
import cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo.MeetingSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meeting.MeetingDO;
import cn.iocoder.yudao.module.pcm.service.meeting.MeetingService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理後臺 - 商談記錄")
@RestController
@RequestMapping("/pcm/meeting")
@Validated
public class MeetingController {

    @Resource
    private MeetingService meetingService;

    @PostMapping("/create")
    @Operation(summary = "创建商谈记录")
    public CommonResult<Long> createMeeting(@Valid @RequestBody MeetingSaveReqVO createReqVO) {
        return success(meetingService.createMeeting(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商谈记录")
    public CommonResult<Boolean> updateMeeting(@Valid @RequestBody MeetingSaveReqVO updateReqVO) {
        meetingService.updateMeeting(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商谈记录")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteMeeting(@RequestParam("id") Long id) {
        meetingService.deleteMeeting(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商谈记录")
    @Parameter(name = "id", description = "主键", required = true, example = "10000001")
    public CommonResult<MeetingRespVO> getMeeting(@RequestParam("id") Long id) {
        MeetingDO meeting = meetingService.getMeeting(id);
        return success(BeanUtils.toBean(meeting, MeetingRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商谈记录")
    public CommonResult<PageResult<MeetingRespVO>> getMeetingPage(@Valid MeetingPageReqVO pageReqVO) {
        return meetingService.getMeetingPage(pageReqVO);
    }

    @GetMapping("/getMeetingByCardId")
    @Operation(summary = "根据名片ID获得商谈记录")
    public CommonResult<PageResult<MeetingRespVO>> getMeetingByCardId(@RequestParam("cardId") Long cardId) {
        PageResult<MeetingDO> pageResult = meetingService.getMeetingByCardId(cardId);
        return success(BeanUtils.toBean(pageResult, MeetingRespVO.class));
    }

    @PostMapping("/countMeetingByUserId")
    @Operation(summary = "根据用户ID统计商谈记录数")
    public CommonResult<Map<String, Object>> countMeetingByUserId(@RequestBody JSONObject params) {
        return success(meetingService.countMeetingByUserId(params));
    }

}