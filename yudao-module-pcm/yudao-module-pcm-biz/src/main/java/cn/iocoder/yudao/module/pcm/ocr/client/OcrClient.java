package cn.iocoder.yudao.module.pcm.ocr.client;

import cn.iocoder.yudao.framework.common.util.http.HttpUtils;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OcrClient {

    public static void main(String[] args) {
        try {
            // 从文件读取图片并转换为 Base64
            String rootPath = "E:\\outsourcing\\cards\\uatcard\\202505\\pcm_ocr\\";
            String imagePath = rootPath + "CLAPJC1.jpeg";
            byte[] imageBytes = Files.readAllBytes(Paths.get(imagePath));
            String imageBase64 = Base64.getEncoder().encodeToString(imageBytes);

            // 文件信息
            String filename = "AKA1.jpeg";
            String mimeType = "image/jpeg";
            long fileSize = imageBytes.length; // 动态获取文件大小

            // 调用 callOcrApi 方法
            String response = callOcrApi(imageBase64, filename, mimeType, fileSize);
            System.out.println("Response: " + response);

            // 解析响应并提取 text 字段
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> responseMap = objectMapper.readValue(response, new TypeReference<Map<String, Object>>(){});

            // 提取 text 字段
            List<Map<String, Object>> candidates = (List<Map<String, Object>>) responseMap.get("candidates");
            Map<String, Object> content = (Map<String, Object>) candidates.get(0).get("content");
            List<Map<String, Object>> parts = (List<Map<String, Object>>) content.get("parts");
            String text = (String) parts.get(0).get("text");


            // 提取非结构化文本部分
            String rawText = text.split("```json")[0].trim();
            System.out.println("Raw Text: " + rawText);

            // 提取并解析结构化 JSON 部分
            String jsonPart = text.split("```json")[1].replace("\n```", "").trim();
            List<Map<String, String>> structuredData = objectMapper.readValue(jsonPart, new TypeReference<List<Map<String, String>>>(){});
            System.out.println("Structured Data: " + structuredData);
            // 可选：合并为单一对象
            Map<String, String> mergedData = new HashMap<>();
            for (Map<String, String> entry : structuredData) {
                mergedData.putAll(entry);
            }
            System.out.println("Merged Data: " + mergedData);

        } catch (Exception e) {
            System.err.println("Error calling OCR API: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 调用 OCR 接口的通用方法
     * @param imageBase64 图片的 Base64 编码
     * @param filename 图片文件名
     * @param mimeType 图片的 MIME 类型
     * @param fileSize 图片文件大小（字节）
     * @return 接口响应
     * @throws Exception 如果请求失败
     */
    public static String callOcrApi(String imageBase64, String filename, String mimeType, long fileSize) throws Exception {
        String url = "https://n8n-byjlblmx.ap-northeast-1.clawcloudrun.com/webhook/56fa0dd0-c4ce-4a3a-b5e3-53e7469547e6";

        // 构造请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("accept", "*/*");
        headers.put("accept-language", "zh-CN,zh;q=0.9,en;q=0.8");
        headers.put("content-type", "application/json");
        headers.put("priority", "u=1, i");
        headers.put("sec-ch-ua", "\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"");
        headers.put("sec-ch-ua-mobile", "?0");
        headers.put("sec-ch-ua-platform", "\"Windows\"");
        headers.put("sec-fetch-dest", "empty");
        headers.put("sec-fetch-mode", "cors");
        headers.put("sec-fetch-site", "cross-site");
        headers.put("referrer", "https://ocr-demo.pages.dev/");

        // 构造请求体
        Map<String, Object> body = new HashMap<>();
        Map<String, Object> innerBody = new HashMap<>();
        innerBody.put("image", imageBase64 != null ? imageBase64 : "");
        innerBody.put("filename", filename != null ? filename : "AKA1.jpeg");
        innerBody.put("mimeType", mimeType != null ? mimeType : "image/jpeg");
        innerBody.put("fileSize", fileSize);
        body.put("body", innerBody);

        // 将请求体转换为 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String requestBody = objectMapper.writeValueAsString(body);

        // 发送 POST 请求
        return HttpUtils.post(url, headers, requestBody);
    }

}
