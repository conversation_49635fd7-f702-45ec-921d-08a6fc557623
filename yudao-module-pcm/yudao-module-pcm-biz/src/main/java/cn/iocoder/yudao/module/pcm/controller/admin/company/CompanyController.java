package cn.iocoder.yudao.module.pcm.controller.admin.company;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.company.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.company.CompanyDO;
import cn.iocoder.yudao.module.pcm.service.company.CompanyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理後臺 - 公司")
@RestController
@RequestMapping("/pcm/company")
@Validated
public class CompanyController {

    @Resource
    private CompanyService companyService;

    @PostMapping("/create")
    @Operation(summary = "創建公司")
    public CommonResult<Long> createCompany(@Valid @RequestBody CompanySaveReqVO createReqVO) {
        return success(companyService.createCompany(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新公司")
    public CommonResult<Boolean> updateCompany(@Valid @RequestBody CompanySaveReqVO updateReqVO) {
        companyService.updateCompany(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "刪除公司")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteCompany(@RequestParam("id") Long id) {
        companyService.deleteCompany(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "獲得公司")
    @Parameter(name = "id", description = "编号", required = true, example = "10000001")
    public CommonResult<CompanyRespVO> getCompany(@RequestParam("id") Long id) {
        CompanyDO company = companyService.getCompany(id);
        return success(BeanUtils.toBean(company, CompanyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得公司分页")
    public CommonResult<PageResult<CompanyRespVO>> getCompanyPage(@Valid CompanyPageReqVO pageReqVO) {
        return companyService.getCompanyPage(pageReqVO);
    }

    @GetMapping("/tree/{companyId}")
    @Operation(summary = "获取所有子机构及名片")
    public CommonResult<CompanyCardDTO> getCompanyWithChildren(@PathVariable("companyId") Long companyId) {
        return success(companyService.getCompanyTreeWithCards(companyId));
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "獲取公司精簡信息列表", description = "只包含被開啓的公司，主要用於前端的下拉選項")
    public CommonResult<List<CompanySimpleRespVO>> getSimpleDeptList(@RequestParam("type") String type) {
        return companyService.getCompanyList(type);
    }



}