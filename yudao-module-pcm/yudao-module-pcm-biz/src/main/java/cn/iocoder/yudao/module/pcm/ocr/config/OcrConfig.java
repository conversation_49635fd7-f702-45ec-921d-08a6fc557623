package cn.iocoder.yudao.module.pcm.ocr.config;

import cn.iocoder.yudao.module.pcm.ocr.client.TencentOcrClient;
import com.tencentcloudapi.common.Credential;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OcrConfig {

    /**
     * 创建认证对象
     * @return
     */
    @Bean
    public Credential credential(@Value("${tencent.cloud.secret.id}") String secretId,
                                 @Value("${tencent.cloud.secret.key}") String secretKey) {
        return new Credential(secretId, secretKey);
    }

    /**
     * 腾讯云OCR服务的客户端对象，
     * 用于发起OCR请求指定服务区域为广州（腾讯云支持多个区域，如北京、上海等）
     * @param credential 认证对象
     * @return 腾讯云OCR服务的客户端对象
     */
    @Bean
    public TencentOcrClient tencentOcrClient(Credential credential) {
        return new TencentOcrClient(credential, "ap-guangzhou");
    }
}
