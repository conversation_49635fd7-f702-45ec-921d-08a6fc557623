package cn.iocoder.yudao.module.pcm.dal.dataobject.tags;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 标签 DO
 *
 * <AUTHOR>
 */
@TableName("pcm_tags")
@KeySequence("pcm_tags_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagsDO extends BaseDO {

    /**
     * 标签ID
     */
    @TableId
    private Long id;
    /**
     * 公司ID
     */
    private Long companyId;
    /**
     * 标签名称
     */
    private String name;
    /**
     * 描述/备注
     */
    private String description;

}