package cn.iocoder.yudao.module.pcm.dal.dataobject.meeting;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import org.apache.ibatis.type.JdbcType;

/**
 * 商谈记录 DO
 *
 * <AUTHOR>
 */
@TableName("pcm_meeting")
@KeySequence("pcm_meeting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingDO {

    /**
     * 商谈记录ID
     */
    @TableId
    private Long id;
    /**
     * 名片ID
     */
    private Long cardId;

    /**
     * 商谈类型
     */
    private String type;
    /**
     * 商谈主题
     */
    private String title;
    /**
     * 商谈内容
     */
    private String content;
    /**
     * 会议场合
     */
    private String occasion;
    /**
     * 商谈时间
     */
    private String meetingTime;
    /**
     * 图片
     */
    private String imageUrl;

    /**
     * 商谈人姓名
     */
    private String userName;

    /**
     * 商谈人职位
     */
    private String jobTitle;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 创建人ID
     */
    private Long createUserId;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT, jdbcType = JdbcType.VARCHAR)
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.VARCHAR)
    private String updater;

    /**
     * 是否删除
     */
    private Boolean deleted;

}