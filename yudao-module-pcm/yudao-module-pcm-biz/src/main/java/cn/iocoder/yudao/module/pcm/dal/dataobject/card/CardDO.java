package cn.iocoder.yudao.module.pcm.dal.dataobject.card;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 名片 DO
 *
 * <AUTHOR>
 */
@TableName("pcm_card")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardDO implements Serializable, TransPojo {

    /**
     * 名片ID
     */
    @TableId
    private Long id;
    /**
     * 分组ID,用于合并名片
     */
    private String groupId;

    /**
     * Title (Mr, Ms, Mrs, Others)
     */
    private String titleEn;

    /**
     * First Name (e.g. Tai-man)
     */
    private String firstNameEn;

    /**
     * Last Name (e.g. CHAN)
     */
    private String lastNameEn;
    /**
     * Honour (e.g. MH, JP)
     */
    private String honourEn;

    /**
     * 稱謂（先生/ 小姐/ 女士/其他）
     */
    private String title;

    /**
     * 姓氏
     */
    private String firstName;

    /**
     * 名字
     */
    private String lastName;

    /**
     * 勳銜
     */
    private String honour;

    /**
     * 全名
     */
    private String fullName;

    /**
     * 暱稱
     */
    private String nickName;

    /**
     * ID of Organisation
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long companyIdEn;

    /**
     * Name of Organisation
     */
    private String companyNameEn;

    /**
     * Department
     */
    private String departmentEn;

    /**
     * Job Title
     */
    private String jobTitleEn;

    /**
     * 機構ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long companyId;
    /**
     * 機構名稱
     */
    private String companyName;

    /**
     * 部門
     */
    private String department;

    /**
     * 職位
     */
    private String jobTitle;
    /**
     * 流動電話
     */
    private String phoneMobile;

    /**
     * 流動電話區號
     */
    private String phoneMobileAreaCode;

    /**
     * 內地號碼
     */
    private String phoneMainland;

    /**
     * 內地號碼區號
     */
    private String phoneMainlandAreaCode;

    /**
     * Email Address 電郵
     */
    private String email;

    /**
     * Email Address 電郵1
     */
    private String email1;

    /**
     * Email Address 電郵2
     */
    private String email2;

    /**
     * Phone(office) 電話（辦公室）
     */
    private String phoneOffice;
    /**
     * 辦公電話區號
     */
    private String phoneOfficeAreaCode;

    /**
     * Phone(Direct Line)電話（直線）
     */
    private String phoneDirectLine;

    /**
     * 直線電話區號
     */
    private String phoneDirectLineAreaCode;

    /**
     * fax number 傳真（如適用）
     */
    private String faxNumber;
    /**
     * address line 1
     */
    private String addressLine1En;
    /**
     * address line 2
     */
    private String addressLine2En;
    /**
     * district(e.g. Tsim Sha Tsui)
     */
    private String districtEn;
    /**
     * area(e.g. Kowloon)
     */
    private String areaEn;
    /**
     * country
     */
    private String countryEn;
    /**
     * 地址1
     */
    private String addressLine1;
    /**
     * 地址2
     */
    private String addressLine2;
    /**
     * 地區
     */
    private String district;
    /**
     * 區域
     */
    private String area;
    /**
     * 国家
     */
    private String country;
    /**
     * 省
     */
    private String province;

    /**
     * 城市
     */
    private String city;
    /**
     * (e.g. Technology, Healthcare) 行業類型
     */
    private String industryType;
    /**
     * 業務類型（政府或相關組織、企業、基金會、非政府組織、其他）
     */
    private String businessType;

    /**
     * 其他業務類型
     */
    private String otherBusiness;
    /**
     * linkedin profile url
     */
    private String linkedinProfileUrl;
    /**
     * facebook page url
     */
    private String facebookPageUrl;
    /**
     * instagram url
     */
    private String instagramUrl;
    /**
     * wechat id
     */
    private String wechatId;
    /**
     * 會面詳情
     */
    private String communicationHistory;
    /**
     * 標籤
     */
    private String customTags;
    /**
     * 状态（Active:活跃，Inactive:非活跃）
     */
    private String status;
    /**
     * 備註
     */
    private String notes;
    /**
     * 简介
     */
    private String description;
    /**
     * 图片头像地址
     */
    private String imageUrl;

    /**
     * 反面名片的URL
     */
    private String backUrl;

    /**
     * 正面图片ID
     */
    private Long imageId;

    /**
     * 反面图片ID
     */
    private Long backId;

    private String address;
    private String socialMedia;

    /**
     * 有权查看的同事
     */
    private String userId;
    /**
     * 有权查看的部门
     */
    private String deptId;

    /**
     * 网址
     */
    private String website;

    /**
     * 即时消息
     */
    private String instantMessaging;

    /**
     * 纪念日
     */
    private String anniversary;

    /**
     * 交换日期
     */
    private String exchangeDate;

    /**
     * 是否双面：true 为双面，false 为单面
     */
    private Boolean doubleSided;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者id
     */
    private Long createUserId;

    /**
     * 创建者
     */
    private String creator;
    /**
     * 更新者
     *
     */
    private String updater;
    /**
     * 是否删除
     */
    private Boolean deleted;

}