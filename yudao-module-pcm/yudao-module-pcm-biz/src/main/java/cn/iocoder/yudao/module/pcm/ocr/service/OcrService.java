package cn.iocoder.yudao.module.pcm.ocr.service;

import cn.iocoder.yudao.module.pcm.ocr.vo.ProcessedCard;
import com.tencentcloudapi.ocr.v20181119.models.BusinessCardOCRResponse;

public interface OcrService {

    /**
     * 识别名片
     *
     * @param imagePath
     * @return
     */
    BusinessCardOCRResponse recognizeBusinessCard(String imagePath);

    /**
     * 识别名片
     *
     * @param imageData
     * @return
     */
    BusinessCardOCRResponse recognizeBusinessCard(byte[] imageData);

    /**
     * 識別雙面名片
     * @param frontFileId
     * @param backFileId
     * @return
     */
    ProcessedCard recognitionDoubleSideCard(Long frontFileId, Long backFileId);

    /**
     * 識別雙面名片Qwen
     * @param frontFileId
     * @param backFileId
     * @return
     */
    ProcessedCard recognitionDoubleSideCardQwen(Long frontFileId, Long backFileId);

    /**
     * 单张保存名片
     *
     * @param resp
     * @param url
     * @return
     */
    Long singleSaveCard(BusinessCardOCRResponse resp, String url);


}
