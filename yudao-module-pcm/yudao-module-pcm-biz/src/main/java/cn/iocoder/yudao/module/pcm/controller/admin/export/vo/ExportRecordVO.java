package cn.iocoder.yudao.module.pcm.controller.admin.export.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 匯出記錄響應物件
 */
@Data
public class ExportRecordVO {
    private Long id; // 匯出記錄編號
    private Long userId; // 申請人編號
    private String requesterName; // 申請人名稱
    private Integer exportCount; //匯出名片數量
    private String status; // 審批狀態
    private String requesterComment; // 申請人備註
    private Long approverId; // 審批人編號
    private String approverName; // 審批人名稱
    private LocalDateTime approveTime; // 審批時間
    private String approverComment; // 審批人備註
    private String guid;
    private String exportStatus;
    private LocalDateTime createTime; // 創建時間
}
