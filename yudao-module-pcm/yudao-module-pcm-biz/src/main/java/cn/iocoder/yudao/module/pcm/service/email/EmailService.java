package cn.iocoder.yudao.module.pcm.service.email;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EmailService {

    /*@Autowired
    private MailSendService mailSendService;*/

    /**
     * 發送審批請求郵件
     * @param requester 申請人
     * @param approver 審批人
     * @param record 匯出記錄
     */
    /*public void sendApprovalEmail(User requester, User approver, ExportRecord record) {
        String subject = "名片匯出審批請求";
        String content = String.format(
                "您好，%s，\n\n" +
                        "%s（編號: %d）請求匯出 %d 張名片。\n" +
                        "申請人備註：%s\n\n" +
                        "請審閱並在系統內進行審批。\n\n" +
                        "請求編號：%d\n" +
                        "提交時間：%s\n\n" +
                        "此郵件由系統自動發送，請勿回覆。",
                approver.getName(),
                requester.getName(),
                requester.getId(),
                record.getExportCount(),
                record.getRequesterComment(),
                record.getId(),
                record.getCreatedAt()
        );
        mailSendService.sendSingleMail(
                approver.getEmail(),
                null, // 發送人郵箱，null表示使用預設配置
                subject,
                content
        );
    }*/

}
