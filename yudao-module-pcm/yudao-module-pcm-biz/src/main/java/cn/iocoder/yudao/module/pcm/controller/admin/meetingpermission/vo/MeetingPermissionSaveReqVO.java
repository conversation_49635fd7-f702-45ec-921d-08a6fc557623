package cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商谈记录权限新增/修改 Request VO")
@Data
public class MeetingPermissionSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31215")
    private Long id;

    @Schema(description = "会议记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13600")
    @NotNull(message = "会议记录ID不能为空")
    private Long meetingId;

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "部门ID", example = "100")
    private Long deptId;

    @Schema(description = "角色ID", example = "1")
    private Long roleId;

}