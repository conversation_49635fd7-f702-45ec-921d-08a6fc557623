package cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 名片权限 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CardPermissionRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31215")
    private Long id;

    @Schema(description = "名片ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13600")
    private Long cardId;

    @Schema(description = "用户ID", example = "15179")
    private Long userId;

    @Schema(description = "有权看的字段")
    private String fieldName; // 以数组格式，存储字段权限

    @Schema(description = "是否可编辑")
    private Boolean isEdit;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}