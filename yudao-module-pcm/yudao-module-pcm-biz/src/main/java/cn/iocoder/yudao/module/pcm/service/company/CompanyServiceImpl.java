package cn.iocoder.yudao.module.pcm.service.company;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.company.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.company.CompanyDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.card.CardMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.company.CompanyMapper;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.*;

/**
 * 公司 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CompanyServiceImpl implements CompanyService {

    private static final Logger log = LoggerFactory.getLogger(CompanyServiceImpl.class);
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private CardMapper cardMapper;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;

    // 常量定义
    private static final String TYPE_COMPANY = "company";
    private static final String TYPE_USER = "user";
    private static final String TYPE_CARD = "card";
    private static final String NAME_EMPTY = "name is empty";
    private static final String USER_ID_DELIMITER = ";";


    @Override
    public Long createCompany(CompanySaveReqVO createReqVO) {
        try {
            // 驗證並處理電話號碼
            String phone = createReqVO.getPhone();
            if (StringUtils.isNotBlank(phone)) {
                // 移除電話號碼中的英文字符
                phone = phone.replaceAll("[a-zA-Z]", "");
                // 檢查長度並從左端截斷至 20 個字符
                if (phone.length() > 20) {
                    phone = phone.substring(0, 20);
                    createReqVO.setPhone(phone);
                    log.warn("電話號碼過長，已從左端截斷至 20 個字符: {}", phone);
                }
            }

            if (StringUtils.isBlank(createReqVO.getLanguageType())) {
                String languageType = createReqVO.getName().matches(".*[\\u4E00-\\u9FFF].*") ? "zh" : "en";
                createReqVO.setLanguageType(languageType);
            }

            CompanyDO company = BeanUtils.toBean(createReqVO, CompanyDO.class);
            String nickname = SecurityFrameworkUtils.getLoginUserNickname();
            LocalDateTime now = LocalDateTime.now();

            company.setCreator(nickname);
            company.setCreateTime(now);

            company.setUpdater(nickname);
            company.setUpdateTime(now);
            companyMapper.insert(company);
            return company.getId();
        } catch (Exception e) {
            log.error("創建公司失敗, createReqVO: {}, 錯誤: {}", createReqVO, e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional
    public void updateCompany(CompanySaveReqVO updateReqVO) {
        try {
            // 校验存在
            validateCompanyExists(updateReqVO.getId());

            // 更新
            CompanyDO updateObj = BeanUtils.toBean(updateReqVO, CompanyDO.class);

            // 去名片表校验：如果是中文公司字段：company_name,LanguageType=zh
            List<Map<String, Object>> cardZh = cardMapper.findByCompanyIdOrEn(updateReqVO.getId(), false);

            // 去名片表校验：如果是中文公司字段：company_name_en,LanguageType=en
            List<Map<String, Object>> cardEn = cardMapper.findByCompanyIdOrEn(updateReqVO.getId(), true);

            if (!cardZh.isEmpty()) {
                updateObj.setLanguageType("zh");
            } else if (!cardEn.isEmpty()) {
                updateObj.setLanguageType("en");
            } else {
                updateObj.setLanguageType("unknown");
            }

            // 修改LanguageType会影像公司列表，不显示公司名字
            /*if (updateObj.getName().matches(".*[\\u4E00-\\u9FFF].*")) {
                updateObj.setLanguageType("zh");
            } else {
                updateObj.setLanguageType("en");
            }*/

            companyMapper.updateById(updateObj);

            // 修改了公司名称后，把对应名片的公司名称也一起更新
            Map<String, Object> companyFields = new HashMap<>();
            Map<String, Object> companyConditions = new HashMap<>();
            if (cardMapper.checkRecordExists(updateObj.getId(), null)) {
                log.info("公司名称已修改，将更新对应名片的所属公司名称");
                companyFields.put("companyName", updateObj.getName());
                companyConditions.put("companyId", updateObj.getId());
                updateObj.setLanguageType("zh");

                cardMapper.updateDynamically(companyFields, companyConditions);
            }
            if (cardMapper.checkRecordExists(null, updateObj.getId())) {
                log.info("公司名称已修改，将更新对应名片的所属公司英文名称");
                companyFields.put("companyNameEn", updateObj.getName());
                companyConditions.put("companyIdEn", updateObj.getId());
                updateObj.setLanguageType("en");

                cardMapper.updateDynamically(companyFields, companyConditions);
            }


        } catch (Exception e) {
            log.error("updateCompany error: {}", e.getMessage());
        }
    }

    /**
     * 更新或插入公司记录（upsert）。
     *
     * @param id      公司ID，可为空（为空时表示新增）。
     * @param orgName 公司名称，不能为空或仅包含空白字符。
     * @return 公司ID（更新或插入后的ID）。
     * @throws IllegalArgumentException 如果输入参数无效。
     * @throws ServiceException         如果数据库操作失败或业务逻辑异常。
     */
    @Override
    public Long upsertCompany(Long id, String orgName) {
        // 输入校验
        if (StringUtils.isBlank(orgName)) {
            throw new IllegalArgumentException("公司名称不能为空");
        }

        try {
            if (id != null) {
                return updateCompany(id, orgName.trim());
            } else {
                return insertCompany(orgName.trim());
            }
        } catch (DataAccessException e) {
            log.error("数据库操作失败, id: {}, orgName: {}, 错误: {}", id, orgName, e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("更新或新增公司记录失败, id: {}, orgName: {}, 错误: {}", id, orgName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新公司记录。
     */
    private Long updateCompany(Long id, String orgName) {
        CompanyDO existingCompany = companyMapper.selectById(id);
        if (existingCompany == null) {
            // 不存在则插入新记录
            return insertCompany(orgName);
        }

        existingCompany.setName(orgName);
        existingCompany.setUpdateTime(LocalDateTime.now());
        existingCompany.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        companyMapper.updateById(existingCompany);
        return existingCompany.getId();
    }

    /**
     * 插入新公司记录。
     */
    private Long insertCompany(String orgName) {
        String creator = SecurityFrameworkUtils.getLoginUserNickname();
        LocalDateTime now = LocalDateTime.now();
        CompanyDO newCompany = new CompanyDO();
        String languageType = orgName.matches(".*[\\u4E00-\\u9FFF].*") ? "zh" : "en";
        newCompany.setName(orgName);
        newCompany.setLanguageType(languageType);
        newCompany.setParentId(0L);
        newCompany.setAddress("");
        newCompany.setCreator(creator);
        newCompany.setCreateTime(now);
        newCompany.setUpdateTime(now);

        companyMapper.insert(newCompany);
        return newCompany.getId();
    }

    @Override
    public void deleteCompany(Long id) {
        // 校验存在
        validateCompanyExists(id);
        List<CardDO> cardList = cardMapper.getCardListByCompanyId(id);
        log.info("刪除該公司前，檢查該公司存在多 {} 名片： {}", cardList.size());
        if (CollectionUtils.isEmpty(cardList)) {
            companyMapper.deleteById(id);
        } else {
            throw exception(COMPANY_CANNOT_DELETE_WITH_CARDS);
        }

    }

    @Override
    public void delete(Long id) {
        companyMapper.deleteById(id);
    }

    private void validateCompanyExists(Long id) {
        if (companyMapper.selectById(id) == null) {
            throw exception(COMPANY_NOT_EXISTS);
        }
    }

    @Override
    public CompanyDO getCompany(Long id) {
        return companyMapper.selectById(id);
    }

    /**
     * 获取公司分页数据，并删除无名片的公司
     *
     * @param pageReqVO 分页请求参数
     * @return 分页结果
     */
    @Override
    public CommonResult<PageResult<CompanyRespVO>> getCompanyPage(CompanyPageReqVO pageReqVO) {
        return getPageResultCommonResult(pageReqVO);
    }

    private CommonResult<PageResult<CompanyRespVO>> getPageResultCommonResult(CompanyPageReqVO pageReqVO) {
        // 获取公司分页数据
        PageResult<CompanyDO> companyPageResult = companyMapper.selectPage(pageReqVO);
        if (companyPageResult.getList().isEmpty()) {
            return CommonResult.success(BeanUtils.toBean(companyPageResult, CompanyRespVO.class));
        }

        // 删除无名片的公司
        removeCompaniesWithoutCards(companyPageResult.getList());

        if (pageReqVO == null || StringUtils.isBlank(pageReqVO.getName())) {
            List<Map<String, Object>> companyNamePreferChinese = companyMapper.getCompanyNamePreferChinese();
            List<CompanyRespVO> companyList = BeanUtils.toBean(companyNamePreferChinese, CompanyRespVO.class);

            PageResult<CompanyRespVO> result = new PageResult<>();
            result.setList(companyList);
            result.setTotal((long) companyList.size());
            return CommonResult.success(result);
        } else {
            // 返回最新分页数据（重新查询以反映删除后的结果）
            return CommonResult.success(BeanUtils.toBean(companyMapper.selectPage(pageReqVO), CompanyRespVO.class));
        }
    }

    private CommonResult<PageResult<CompanyRespVO>> getCompanyList(CompanyPageReqVO pageReqVO) {
        // 获取公司分页数据
        PageResult<CompanyDO> companyPageResult = companyMapper.selectPage(pageReqVO);
        if (companyPageResult.getList().isEmpty()) {
            return CommonResult.success(BeanUtils.toBean(companyPageResult, CompanyRespVO.class));
        }

        // 删除无名片的公司
        removeCompaniesWithoutCards(companyPageResult.getList());

        if (pageReqVO == null || StringUtils.isBlank(pageReqVO.getName())) {
            List<Map<String, Object>> mapperCompanyList = cardMapper.getCompanyList();
            List<CompanyRespVO> companyList = BeanUtils.toBean(mapperCompanyList, CompanyRespVO.class);
            PageResult<CompanyRespVO> result = new PageResult<>();
            result.setList(companyList);
            result.setTotal((long) companyList.size());
            return CommonResult.success(result);
        } else {
            // 返回最新分页数据（重新查询以反映删除后的结果）
            return CommonResult.success(BeanUtils.toBean(companyMapper.selectPage(pageReqVO), CompanyRespVO.class));
        }

    }


    /**
     * 删除没有名片的公司
     *
     * @param companies 公司列表
     */
    @Transactional
    public void removeCompaniesWithoutCards(List<CompanyDO> companies) {
        // 提取公司 ID
        List<Long> companyIds = companies.stream()
                .map(CompanyDO::getId)
                .collect(Collectors.toList());

        // 批量查询公司对应的名片
        Map<Long, List<CardDO>> cardMap = cardMapper.getCardListByCompanyIds(companyIds)
                .stream()
                .collect(Collectors.groupingBy(CardDO::getCompanyId));

        // 找出无名片的公司并批量删除
        List<Long> companiesToDelete = companyIds.stream()
                .filter(companyId -> !cardMap.containsKey(companyId) || cardMap.get(companyId).isEmpty())
                .collect(Collectors.toList());

        log.info("【批量删除无名片的公司】 companiesToDelete: {}", companiesToDelete);
        if (!companiesToDelete.isEmpty()) {
            try {
                companyMapper.deleteBatchIds(companiesToDelete);
                log.info("Deleted companies without cards: {}", companiesToDelete);
            } catch (Exception e) {
                log.error("Failed to delete companies: {}", companiesToDelete, e);
                throw new RuntimeException("Failed to delete companies", e);
            }
        }
    }


    @Override
    public CompanyDO getCompanyByName(String name) {
        return companyMapper.selectOne(Wrappers.<CompanyDO>lambdaQuery()
                .eq(CompanyDO::getName, name)
                .eq(CompanyDO::getDeleted, 0)
                .orderByAsc(CompanyDO::getId)
                .last("LIMIT 1"));
    }

    @Override
    public CompanyCardDTO getCompanyTreeWithCards(Long companyId) {
        return buildCompanyTree(companyId);
    }

    private CompanyCardDTO buildCompanyTree(Long companyId) {
        // 获取公司信息
        CompanyDO company = companyMapper.selectById(companyId);
        if (company == null) {
            return null;
        }

        // 构建公司节点
        CompanyCardDTO companyNode = buildCompanyNode(company);

        // 递归构建子公司树
        addSubCompanies(companyNode, companyId);

        // 添加公司名片
        addCompanyCards(companyNode, companyId);

        // 添加关联用户信息
        //addRelatedUsers(companyNode, companyId);

        return companyNode;
    }

    /**
     * 构建公司节点
     */
    private CompanyCardDTO buildCompanyNode(CompanyDO company) {
        CompanyCardDTO node = new CompanyCardDTO();
        node.setId(company.getId());
        node.setName(company.getName());
        node.setType(TYPE_COMPANY);
        node.setChildren(new ArrayList<>()); // 确保 children 不为 null
        return node;
    }

    /**
     * 递归添加子公司
     */
    private void addSubCompanies(CompanyCardDTO parentNode, Long companyId) {
        List<CompanyDO> subCompanies = companyMapper.selectByParentId(companyId);
        if (subCompanies == null || subCompanies.isEmpty()) {
            return;
        }

        for (CompanyDO subCompany : subCompanies) {
            CompanyCardDTO subNode = buildCompanyTree(subCompany.getId());
            if (subNode != null) {
                parentNode.getChildren().add(subNode);
            }
        }
    }

    /**
     * 添加公司名片
     */
    private void addCompanyCards(CompanyCardDTO companyNode, Long companyId) {
        List<CardDO> cards = cardMapper.selectByCompanyId(companyId);
        if (cards == null || cards.isEmpty()) {
            return;
        }

        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        Long loginUserDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();

        for (CardDO card : cards) {
            if (isAdmin || isUserAuthorized(card, loginUserId, loginUserDeptId)) {
                companyNode.getChildren().add(createCardNode(card));
            }
        }
    }

    /**
     * 创建卡片节点
     */
    private CompanyCardDTO createCardNode(CardDO card) {
        CompanyCardDTO cardNode = new CompanyCardDTO();
        cardNode.setId(card.getId());
        cardNode.setName(buildCardName(card));
        cardNode.setType(TYPE_CARD);
        cardNode.setChildren(new ArrayList<>());
        return cardNode;
    }

    /**
     * 检查用户是否有权限访问卡片
     */
    private boolean isUserAuthorized(CardDO card, Long loginUserId, Long loginUserDeptId) {

        String userId = card.getUserId();
        String deptId = card.getDeptId();

        if (StringUtils.isBlank(userId) &&  StringUtils.isBlank(deptId)) {
            return true;
        }

        Set<String> userIds = StringUtils.isBlank(userId)
                ? Collections.emptySet()
                : new HashSet<>(Arrays.asList(userId.split(";")));

        Set<String> deptIds = StringUtils.isBlank(deptId)
                ? Collections.emptySet()
                : new HashSet<>(Arrays.asList(deptId.split(";")));

        return userIds.contains(String.valueOf(loginUserId))
                || deptIds.contains(String.valueOf(loginUserDeptId))
                || Objects.equals(card.getCreateUserId(), loginUserId);

    }

    /**
     * 构建名片名称
     */
    private String buildCardName(CardDO card) {
        if (StringUtils.isNotBlank(card.getFirstName()) || StringUtils.isNotBlank(card.getLastName())) {
            return (StringUtils.defaultString(card.getFirstName()) + StringUtils.defaultString(card.getLastName())).trim();
        }
        if (StringUtils.isNotBlank(card.getFirstNameEn()) || StringUtils.isNotBlank(card.getLastNameEn())) {
            return (StringUtils.defaultString(card.getFirstNameEn()) + " " + StringUtils.defaultString(card.getLastNameEn())).trim();
        }
        return NAME_EMPTY;
    }

    /**
     * 添加关联用户信息
     */
    private void addRelatedUsers(CompanyCardDTO companyNode, Long companyId) {
        List<CardDO> cards = cardMapper.selectByCompanyId(companyId);
        if (cards == null || cards.isEmpty()) {
            return;
        }

        // 提取用户 ID 并去重
        Set<Long> uniqueUserIds = cards.stream()
                .flatMap(card -> {
                    Stream<Long> createUserIdStream = Stream.of(card.getCreateUserId());
                    Stream<Long> userIdStream = StringUtils.isBlank(card.getUserId())
                            ? Stream.empty()
                            : Arrays.stream(card.getUserId().split(USER_ID_DELIMITER))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(s -> {
                                try {
                                    return Long.parseLong(s);
                                } catch (NumberFormatException e) {
                                    // 可添加日志：log.warn("Invalid user ID format: {}", s);
                                    return null;
                                }
                            })
                            .filter(Objects::nonNull);
                    return Stream.concat(createUserIdStream, userIdStream);
                })
                .collect(Collectors.toSet());

        if (uniqueUserIds.isEmpty()) {
            return;
        }

        // 批量查询用户信息
        JSONArray userList = adminUserApi.getUserListByIds(new ArrayList<>(uniqueUserIds));
        if (userList == null || userList.isEmpty()) {
            // 可添加日志：log.warn("No users found for IDs: {}", uniqueUserIds);
            return;
        }

        // 添加用户节点
        userList.stream()
                .map(obj -> (JSONObject) obj)
                .forEach(user -> {
                    CompanyCardDTO userNode = new CompanyCardDTO();
                    userNode.setId(user.getLong("id"));
                    userNode.setName(user.getString("nickname"));
                    userNode.setType(TYPE_USER);
                    userNode.setChildren(new ArrayList<>());
                    companyNode.getChildren().add(userNode);
                });
    }

    @Override
    public CommonResult<List<CompanySimpleRespVO>> getCompanyList(String type) {
        if (StringUtils.isBlank(type))  {
            return CommonResult.success(new ArrayList<>());
        }

        //旧方法：先注释掉
        List<CompanyDO> filteredList = getCompanyDOS(type);
        return CommonResult.success(BeanUtils.toBean(filteredList, CompanySimpleRespVO.class));
    }

    private List<CompanyDO> getCompanyDOS(String type) {
        List<CompanyDO> list = companyMapper.selectSimpleList(type);
        List<CompanyDO> filteredList = list.stream()
                .filter(company -> StringUtils.isNotBlank(company.getName()))
                .sorted(Comparator.comparing(CompanyDO::getName))
                .collect(Collectors.toList());
        return filteredList;
    }

    private List<CompanyDO> getCompanyByCardDOS(String type) {
        List<Map<String, Object>> companyList = cardMapper.getSimpleCompanyList(type);
        List<CompanyDO> filteredList = companyList.stream()
                .filter(company -> StringUtils.isNotBlank((String) company.get("name")))
                .map(company -> {
                    CompanyDO companyDO = new CompanyDO();
                    companyDO.setId((Long) company.get("id"));
                    companyDO.setName((String) company.get("name"));
                    companyDO.setParentId(0L);
                    return companyDO;
                })
                .sorted(Comparator.comparing(CompanyDO::getName))
                .collect(Collectors.toList());
        return filteredList;
    }

    public boolean isAllFieldsNullOrEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        try {
            for (Field field : obj.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value != null) {
                    if (value instanceof String && !((String) value).trim().isEmpty()) {
                        return false;
                    } else if (value instanceof Collection && !((Collection<?>) value).isEmpty()) {
                        return false;
                    } else if (value instanceof Map && !((Map<?, ?>) value).isEmpty()) {
                        return false;
                    } else if (!(value instanceof String || value instanceof Collection || value instanceof Map)) {
                        return false;
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return true;
    }


}