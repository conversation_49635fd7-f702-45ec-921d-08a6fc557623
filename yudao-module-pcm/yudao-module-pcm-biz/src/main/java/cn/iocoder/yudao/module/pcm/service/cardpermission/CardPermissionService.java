package cn.iocoder.yudao.module.pcm.service.cardpermission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo.CardPermissionPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo.CardPermissionSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission.CardPermissionDO;
import com.alibaba.fastjson.JSONArray;

import javax.validation.Valid;
import java.util.List;

/**
 * 名片权限 Service 接口
 *
 * <AUTHOR>
 */
public interface CardPermissionService {

    /**
     * 创建名片权限
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCardPermission(@Valid CardPermissionSaveReqVO createReqVO);

    /**
     * 创建名片权限
     * @param cardId
     * @param permissionArray 创建信息
     * @return 编号
     */
    CommonResult<Long> savePermission(Long cardId, JSONArray permissionArray);

    /**
     * 更新名片权限
     *
     * @param updateReqVO 更新信息
     */
    void updateCardPermission(@Valid CardPermissionSaveReqVO updateReqVO);

    /**
     * 删除名片权限
     *
     * @param id 编号
     */
    void deleteCardPermission(Long id);

    /**
     * 获得名片权限
     *
     * @param id 编号
     * @return 名片权限
     */
    CardPermissionDO getCardPermission(Long id);

    /**
     * 获得名片权限分页
     *
     * @param pageReqVO 分页查询
     * @return 名片权限分页
     */
    PageResult<CardPermissionDO> getCardPermissionPage(CardPermissionPageReqVO pageReqVO);

    /**
     * 获得名片权限
     *
     * @param cardId 名片ID
     * @return 名片权限
     */
    JSONArray getCardPermissionByCardId(Long cardId);

}