package cn.iocoder.yudao.module.pcm.service.card;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.*;
import cn.iocoder.yudao.module.pcm.controller.admin.company.vo.CompanySaveReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.TagsRespVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardpermission.CardPermissionDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.company.CompanyDO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.tags.TagsDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.card.CardMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.cardpermission.CardPermissionMapper;
import cn.iocoder.yudao.module.pcm.dal.mysql.tags.TagsMapper;
import cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.pcm.service.company.CompanyService;
import cn.iocoder.yudao.module.system.api.dept.DeptApi;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.*;

/**
 * 名片 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CardServiceImpl implements CardService {

    private static final Logger log = LoggerFactory.getLogger(CardServiceImpl.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    // 定义跳过掩码的字段
    private static final Set<String> UNCONTROLLED_FIELDS = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList("id", "groupId", "firstName","lastName","fullName", "createTime", "createTime",
                    "updateTime", "creator","updater", "deleted", "users", "depts", "isEdit"))
    );

    @Resource
    private CardMapper cardMapper;
    @Resource
    private TagsMapper tagsMapper;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private DeptApi deptApi;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private CompanyService companyService;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private CardPermissionMapper cardPermissionMapper;


    @Override
    public Long createCard(CardSaveReqVO createReqVO) {
        try {
            CardDO card = BeanUtils.toBean(createReqVO, CardDO.class);
            // 判断 firstName 和 lastName 是否相同（忽略大小写和前后空格）
            String firstName = card.getFirstName() != null ? card.getFirstName().trim() : "";
            String lastName = card.getLastName() != null ? card.getLastName().trim() : "";
            if (firstName.equalsIgnoreCase(lastName)) {
                card.setFullName(firstName);
            } else {
                card.setFullName(firstName + " " + lastName);
            }

            // 判断 firstNameEn 和 lastNameEn 是否相同（忽略大小写和前后空格）
            String firstNameEn = card.getFirstNameEn() != null ? card.getFirstNameEn().trim() : "";
            String lastNameEn = card.getLastNameEn() != null ? card.getLastNameEn().trim() : "";
            if (StringUtils.isBlank(card.getFullName())) {
                if (firstNameEn.equalsIgnoreCase(lastNameEn)) {
                    card.setFullName(firstNameEn);
                } else {
                    card.setFullName(firstNameEn + " " + lastNameEn);
                }
            }

            card.setCreateUserId(SecurityFrameworkUtils.getLoginUserId());
            card.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            card.setGroupId(UUID.randomUUID().toString().replaceAll("-", ""));
            cardMapper.insert(card);
            return card.getId();
        } catch (Exception e) {
            log.error("[createCard][createReqVO({}) create error]", createReqVO, e);
            throw exception(CARD_INSERT_EXECPTION);
        }
    }

    @Override
    public CommonResult<Boolean> updateCard(CardSaveReqVO updateReqVO) {
        try {
            // 校验卡片是否存在
            validateCardExists(updateReqVO.getId());
            String deptId = updateReqVO.getDeptId();
            CardDO cardDO = cardMapper.selectById(updateReqVO.getId());

            // 檢查欲新增的部門是否與現有有權限查看的同事的部門重疊
            if (StringUtils.isNotBlank(deptId) && StringUtils.isNotBlank(cardDO.getUserId())) {
                String[] deptIds = deptId.split(";");
                String[] userIds = cardDO.getUserId().split(";");

                //根據用戶id批量獲取用戶信息
                List<Long> userIdList = Arrays.stream(userIds).map(Long::parseLong).collect(Collectors.toList());
                JSONArray userListByIds = adminUserApi.getUserListByIds(userIdList);
                Map<Long, JSONObject> userMap = userListByIds.stream()
                        .map(obj -> (JSONObject) obj)
                        .collect(Collectors.toMap(
                                json -> json.getLong("id"),
                                json -> json,
                                (existing, replacement) -> existing // 处理键冲突，保留现有值
                        ));

                // 用來收集部門已在清單中的 userId
                List<String> conflictUserIds = new ArrayList<>();

                // 遍歷現有的有權限查看的同事
                for (String userId : userIds) {
                    // 獲取該同事的部門 ID
                    String userDeptId = getUserDeptId(userId, userMap);
                    // 檢查該同事的部門是否在欲新增的部門清單中
                    if (ArrayUtils.contains(deptIds, userDeptId)) {
                        String userNickname = getUserNickname(userId, userMap);
                        conflictUserIds.add(userNickname);
                    }
                }

                // 如果有衝突的 userId，統一拋出異常
                if (!conflictUserIds.isEmpty()) {
                    String errorMessage = String.format("%s 所屬部門已在『有權限查看的部門』名單中，無需重複新增個人。", String.join(", ", conflictUserIds));
                    throw new IllegalArgumentException(errorMessage);
                }
            }

            CardDO updateObj = BeanUtils.toBean(updateReqVO, CardDO.class);
            cardMapper.updateById(updateObj);
            return CommonResult.success(true);
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            return CommonResult.error(400, e.getMessage());
        }
    }

    /**
     * 根据用户ID获取用户的部门ID
     * 此方法通过查询用户部门映射表来获取部门ID，旨在用于处理用户部门的识别与区分
     *
     * @param userId 用户ID，用于查询用户部门映射表
     * @param userDeptMap 用户与部门ID的映射表，存储了用户ID与部门信息的映射关系
     * @return 用户的部门ID，如果用户不存在于映射表中，则返回null
     */
    private String getUserDeptId(String userId, Map<Long,JSONObject> userDeptMap) {
        JSONObject userDept = userDeptMap.get(Long.parseLong(userId));
        return userDept != null ? String.valueOf(userDept.getLong("deptId")) : null;
    }
    /**
     * 根据用户ID获取用户的昵称
     * 该方法从用户部门映射中获取用户信息，并提取用户的昵称
     *
     * @param userId 用户ID，用于在用户部门映射中查找特定用户
     * @param userDeptMap 包含用户信息的映射表，键为用户ID，值为包含用户信息的JSONObject
     * @return 用户的昵称如果未找到用户信息，则返回null
     */
    private String getUserNickname(String userId, Map<Long,JSONObject> userDeptMap) {
        JSONObject userDept = userDeptMap.get(Long.parseLong(userId));
        return userDept != null ? userDept.getString("nickname") : null;
    }


    @Override
    public CommonResult<Map<String, Object>> saveAndReturnPrevious(CardSaveReqVO updateReqVO) {
        try {
            // 校验名片存在
            validateCardExistsByIDAndDel(updateReqVO.getId());

            // 转换VO为DO
            CardDO cardDO = BeanUtils.toBean(updateReqVO, CardDO.class);

            // 处理公司名称（中文）
            updateCompanyInfo(cardDO, cardDO.getCompanyId(), cardDO.getCompanyName(), "companyName", "companyId");

            // 处理公司名称（英文）
            updateCompanyInfo(cardDO, cardDO.getCompanyIdEn(), cardDO.getCompanyNameEn(), "companyNameEn", "companyIdEn");

            if (StringUtils.isNotBlank(cardDO.getCompanyName()) && cardDO.getCompanyName().length() > 500) {
                log.info("[saveAndReturnPrevious][companyName too long, id: {}, companyName: {}]", cardDO.getId(), cardDO.getCompanyName());
                cardDO.setCompanyName(cardDO.getCompanyName().substring(0, 500));
                log.warn("公司名称过长，已截断：{}", cardDO.getCompanyName());
            }
            if (StringUtils.isNotBlank(cardDO.getCompanyName()) && cardDO.getCompanyName().length() > 500) {
                log.info("[saveAndReturnPrevious][companyNameEn too long, id: {}, companyNameEn: {}]", cardDO.getId(), cardDO.getCompanyNameEn());
                cardDO.setCompanyName(cardDO.getCompanyName().substring(0, 500));
                log.warn("公司名称英文过长，已截断：{}", cardDO.getCompanyNameEn());
            }

            cardDO.setDeleted(false); // 所有名片都要設置未刪除狀態
            // 更新名片
            cardMapper.updateById(cardDO);

            // 合并公司
            removeCompanyIfNoCards(cardDO.getCompanyId());
            removeCompanyIfNoCards(cardDO.getCompanyIdEn());

            Long previousCardId = getPreviousCardId(cardDO.getId());

            Map<String, Object> map = new HashMap<>();
            map.put("previousCardId", previousCardId);
            return CommonResult.success(map);
        } catch (Exception e) {
            log.error("Failed to update card, id: {}, error: {}", updateReqVO.getId(), e.getMessage());
            return CommonResult.error(400, e.getMessage());
        }
    }

    /**
     * 更新公司信息并同步到名片
     * @param cardDO 名片DO对象
     * @param companyId 公司ID
     * @param companyName 公司名称
     * @param fieldName 字段名（companyName 或 companyNameEn）
     * @param conditionField 条件字段名（companyId 或 companyIdEn）
     */
    private void updateCompanyInfo(CardDO cardDO, Long companyId, String companyName,
                                   String fieldName, String conditionField) {
        if (StringUtils.isBlank(companyName)) {
            if ("companyName".equals(fieldName)) {
                cardDO.setCompanyId(null);
            } else if ("companyNameEn".equals(fieldName)){
                cardDO.setCompanyIdEn(null);
            }
            return;
        }

        CompanyDO companyDO = companyService.getCompanyByName(companyName);
        if (companyDO == null) {
            // 公司不存在，创建或更新公司
            Long newCompanyId = companyService.upsertCompany(companyId, companyName);
            if (newCompanyId != null) {
                Map<String, Object> fields = new HashMap<>();
                fields.put(fieldName, companyName);
                Map<String, Object> conditions = new HashMap<>();
                conditions.put(conditionField, newCompanyId);
                //修改其他名片的公司信息
                cardMapper.updateDynamically(fields, conditions);
            }
        } else {
            // 公司存在，更新名片的公司信息并删除旧公司
            if ("companyId".equals(conditionField) && "companyName".equals(fieldName)) {
                cardDO.setCompanyId(companyDO.getId());
                cardDO.setCompanyName(companyDO.getName());
            }
            if ("companyIdEn".equals(conditionField) && "companyNameEn".equals(fieldName)) {
                cardDO.setCompanyIdEn(companyDO.getId());
                cardDO.setCompanyNameEn(companyDO.getName());
            }
        }
    }

    /**
     * 删除无名片的公司
     * @param companyId 公司ID
     */
    private void removeCompanyIfNoCards(Long companyId) {
        if (companyId == null) {
            return;
        }

        List<CardDO> cardList = cardMapper.getCardListByCompanyId(companyId);
        if (CollectionUtils.isEmpty(cardList)) {
            log.info("公司ID={} 无名片信息，执行删除。", companyId);
            companyService.delete(companyId); // 物理删除
        } else {
            log.info("公司ID={} 無名片資訊，執行刪除。", companyId);

        }
    }



    @Override
    public void deleteCard(Long id) {
        // 校验存在
        validateCardExists(id);

        cardMapper.update(Wrappers.<CardDO>lambdaUpdate()
                .set(CardDO::getDeleted, 1)
                .eq(CardDO::getId, id));
    }

    @Override
    public Boolean validateTagUsed(Long tagId) {
        if (tagId == null) {
            return false; // 如果 tagId 为空，直接返回 false
        }

        String customTag = String.valueOf(tagId);
        String separator = "%"; // 定义分隔符，与存储格式保持一致
        String pattern = separator + customTag + separator; // 构造匹配模式，如 ";111;"

        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.like(CardDO::getCustomTags, pattern); // 使用分号分隔的模式匹配

        Long count = cardMapper.selectCount(wrapper); // 统计符合条件的记录数
        log.info("CardServiceImpl.validateTagUsed: count={}", count);
        return count > 0; // 判断是否至少有 1 条记录
    }

    /**
     * 验证名片是否存在
     *
     * @param id 名片的唯一标识符
     * 自定义异常 如果找不到具有指定ID的名片，则抛出CARD_NOT_EXISTS异常
     */
    private void validateCardExists(Long id) {
        // 检查数据库中是否存在与给定ID匹配的名片
        if (cardMapper.selectById(id) == null) {
            // 如果没有找到名片，则抛出预定义的异常
            throw exception(CARD_NOT_EXISTS);
        }
    }

    /**
     * 验证名片是否存在
     *
     * @param id 名片的唯一标识符
     * 自定义异常 如果找不到具有指定ID的名片，则抛出CARD_NOT_EXISTS异常
     */
    private void validateCardExistsByIDAndDel(Long id) {
        // 检查数据库中是否存在与给定ID匹配的名片
        if (selectCardByIdAndDel(id) == null) {
            // 如果没有找到名片，则抛出预定义的异常
            throw exception(CARD_NOT_EXISTS);
        }
    }

    private CardDO selectCardByIdAndDel(Long id) {
        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CardDO::getId, id);
        wrapper.and(w-> {
            w.eq(CardDO::getDeleted, 0);
            w.or();
            w.eq(CardDO::getDeleted, 1);
        });
        return cardMapper.selectOne(wrapper);
    }


    @Override
    public CommonResult<CardRespVO> getCard(Long id) {
        CardDO cardDO = selectCardByIdAndDel(id);
        // CardDO cardDO = cardMapper.selectById(id);
        if (cardDO == null) {
            log.warn("名片不存在，id: {}", id);
            return CommonResult.error(404, "名片不存在");
        }

        CardRespVO cardRespVO = convertCardDOToRespVO(cardDO);
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            log.warn("未發現登錄用戶，id: {}", id);
            return CommonResult.error(401, "未登錄用戶，請先登錄");
        }

        if (!isAdmin) {
            // 檢查用戶權限
            boolean hasUserPerm = false;
            // 檢查部門權限
            boolean hasDeptPerm = false;

            if (StringUtils.isBlank(cardRespVO.getUserId()) && StringUtils.isBlank(cardRespVO.getDeptId())) {
                cardRespVO.setIsEdit(true);
                // 如果 userId 和 deptId 同时为空，说明未分配过权限，这时所有人都有权限查看
                return success(cardRespVO);
            }

            // 檢查創建者權限
            if (Objects.equals(cardDO.getCreateUserId(), loginUserId)) {
                hasUserPerm = true;
                cardRespVO.setIsEdit(true);
            }

            // 檢查用戶權限
            if (!hasUserPerm && StringUtils.isNotBlank(cardDO.getUserId())) {
                hasUserPerm = Arrays.stream(cardDO.getUserId().split(";"))
                        .filter(StrUtil::isNotBlank)
                        .map(Long::valueOf)
                        .anyMatch(userId -> userId.equals(loginUserId));
            }

            // 檢查部門權限
            Set<Long> accessibleDeptIds = Sets.newHashSet();
            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
            if (currentDeptId != null) {
                List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
                accessibleDeptIds.addAll(deptIds);
                accessibleDeptIds.add(currentDeptId);
                hasDeptPerm = hasDepartmentPermission(cardDO.getDeptId(), accessibleDeptIds);
                hasUserPerm = hasUserPerm || hasDeptPerm;
            }

            if (!hasUserPerm) {
                log.warn("用戶無權限，loginUserId: {}, createUserId: {}, userId: {}, deptId: {}",
                        loginUserId, cardDO.getCreateUserId(), cardDO.getUserId(), cardDO.getDeptId());
                return CommonResult.error(403, "無權限訪問該數據");
            }

            // 如果有部門權限，設置可編輯並直接返回
            if (hasDeptPerm) {
                cardRespVO.setIsEdit(true);
                return success(cardRespVO);
            }

            // 查詢並處理個人權限
            List<CardPermissionDO> permissions = cardPermissionMapper.selectList(
                    Wrappers.lambdaQuery(CardPermissionDO.class)
                            .eq(CardPermissionDO::getUserId, loginUserId)
                            .eq(CardPermissionDO::getCardId, id));
            Map<Long, CardPermissionDO> permissionMap = permissions.stream()
                    .collect(Collectors.toMap(CardPermissionDO::getCardId, p -> p));
            hasPermission(loginUserId, id, cardRespVO, permissionMap, false);
        } else {
            cardRespVO.setIsEdit(true);
        }

        return success(cardRespVO);
    }


    /**
     * 檢查部門權限
     *
     * @param cardDeptIds 名片關聯的部門ID（格式: "2;3;4;5"）
     * @param accessibleDeptIds 可訪問的部門ID集合
     * @return 是否有部門權限
     */
    private boolean hasDepartmentPermission(String cardDeptIds, Set<Long> accessibleDeptIds) {
        if (StrUtil.isBlank(cardDeptIds) || accessibleDeptIds.isEmpty()) {
            return false;
        }
        try {
            return Arrays.stream(cardDeptIds.split(";"))
                    .filter(StrUtil::isNotBlank)
                    .map(Long::valueOf)
                    .anyMatch(accessibleDeptIds::contains);
        } catch (NumberFormatException e) {
            log.warn("部門ID格式錯誤，deptIds: {}", cardDeptIds);
            return false;
        }
    }

    /**
     * 检查用户权限并根据权限掩码 CardRespVO 字段
     *
     * @param currentUserId 当前登录用户 ID
     * @param cardId 名片 ID
     * @param cardRespVO 待处理的 CardRespVO 对象
     */
    private void hasPermission(Long currentUserId, Long cardId, CardRespVO cardRespVO,
                               Map<Long, CardPermissionDO> permissionMap, boolean useNullMask) {
        // 管理員或創建者直接設置可編輯並返回
        if (SecurityFrameworkUtils.isAdmin() || Objects.equals(currentUserId, cardRespVO.getCreateUserId())) {
            cardRespVO.setIsEdit(true);
            log.info("管理員或創建者，跳過字段過濾，userId: {}", currentUserId);
            return;
        }

        CardPermissionDO cardPermissionDO = permissionMap.get(cardId);
        if (cardPermissionDO == null || StringUtils.isBlank(cardPermissionDO.getFieldName())) {
            cardRespVO.setIsEdit(false);
            log.warn("未找到有效權限，cardId: {}，屏蔽所有字段", cardId);
            maskAllFields(cardRespVO, useNullMask);
            return;
        }

        cardRespVO.setIsEdit(cardPermissionDO.getIsEdit());

        // 解析允許字段
        List<String> allowedFields;
        try {
            String[] fieldNames = objectMapper.readValue(cardPermissionDO.getFieldName(), String[].class);
            allowedFields = Arrays.stream(fieldNames)
                    .filter(StrUtil::isNotBlank)
                    .map(String::trim)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("解析字段名稱失敗，cardId: {}, JSON: {}", cardId, cardPermissionDO.getFieldName(), e);
            allowedFields = Collections.emptyList();
        }

        // 反射處理字段
        for (Field field : CardRespVO.class.getDeclaredFields()) {
            String fieldName = field.getName();
            if ("serialVersionUID".equals(fieldName) || UNCONTROLLED_FIELDS.contains(fieldName)) {
                continue;
            }

            if (!allowedFields.contains(fieldName)) {
                try {
                    field.setAccessible(true);
                    field.set(cardRespVO, useNullMask || field.getType() != String.class ? null : "*");
                } catch (IllegalAccessException e) {
                    log.error("屏蔽字段失敗，cardId: {}, field: {}", cardId, fieldName, e);
                }
            }
        }
    }

    /**
     * 掩码 CardRespVO 的所有受控字段（无权限时的默认行为），跳过不受权限控制的字段
     *
     * @param cardRespVO 待掩码的 CardRespVO 对象
     */
    private void maskAllFields(CardRespVO cardRespVO, boolean useNullMask) {
        if (cardRespVO == null) {
            return;
        }
        Field[] fields = CardRespVO.class.getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            if (fieldName.equals("serialVersionUID") || UNCONTROLLED_FIELDS.contains(fieldName)) {
                log.info("跳过字段{}：不受权限控制", fieldName);
                continue;
            }
            log.info("屏蔽字段（无权限）：{}", fieldName);
            try {
                field.setAccessible(true);
                if (useNullMask) {
                    field.set(cardRespVO, null); // 使用 null 掩码
                } else if (field.getType() == String.class) {
                    field.set(cardRespVO, "*"); // 使用 * 掩码
                } else {
                    field.set(cardRespVO, null); // 非 String 类型默认 null
                }
            } catch (IllegalAccessException e) {
                log.error("无法屏蔽 CardRespVO 中的字段 {}", fieldName, e);
            }
        }
    }

    @Override
    public PageResult<CardRespVO> getCardPage(CardPageReqVO pageReqVO) {
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        // 設置非管理員的數據權限
        Set<Long> accessibleDeptIds = new HashSet<>();
        if (!isAdmin && loginUserId != null) {
            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
            if (currentDeptId != null) {
                List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
                log.info("可訪問的子部門ID：{}", JSON.toJSON(deptIds));
                if (CollectionUtil.isNotEmpty(deptIds)) {
                    pageReqVO.setDeptId(StringUtils.join(deptIds, ";"));
                    pageReqVO.setDeptIds(new HashSet<>(deptIds));
                    accessibleDeptIds.addAll(deptIds);
                    accessibleDeptIds.add(currentDeptId);
                }
            }
        }

        // 分頁查詢
        PageResult<CardDO> cardDOPageResult = cardMapper.selectPage(pageReqVO);
        List<CardDO> cardDOs = cardDOPageResult.getList();
        if (CollectionUtil.isEmpty(cardDOs)) {
            return PageResult.empty();
        }

        List<CardRespVO> cardRespVOs = BeanUtils.toBean(cardDOs, CardRespVO.class);
        Map<Long, CardPermissionDO> permissionMap = isAdmin ? Collections.emptyMap() : getPermissionMap(cardRespVOs, loginUserId);

        // 處理每條數據的權限
        for (int i = 0; i < cardRespVOs.size(); i++) {
            CardRespVO cardRespVO = cardRespVOs.get(i);
            CardDO cardDO = cardDOs.get(i);

            if (isAdmin) {
                cardRespVO.setIsEdit(true);
                continue;
            }

            // 检查userId和deptId都为空时，说明未分配过权限，这时所有人都有权限查看
            if (StringUtils.isBlank(cardRespVO.getUserId()) && StringUtils.isBlank(cardRespVO.getDeptId())) {
                cardRespVO.setIsEdit(true);
                continue;
            }

            // 检查是否为创建者
            if (Objects.equals(loginUserId, cardRespVO.getCreateUserId())) {
                cardRespVO.setIsEdit(true);
                continue;
            }

            // 检查部门权限
            if (hasDepartmentPermission(cardDO.getDeptId(), accessibleDeptIds)) {
                cardRespVO.setIsEdit(true);
                continue;
            }

            // 检查用户权限
            hasPermission(loginUserId, cardRespVO.getId(), cardRespVO, permissionMap, false);
        }

        return new PageResult<>(cardRespVOs, cardDOPageResult.getTotal());
    }

    /**
     * 把權限表轉換成Map
     * @param cardRespVOs 包含卡片响应信息的列表，用于提取卡片ID
     * @param loginUserId 登录用户的ID，用于查询该用户对卡片的权限
     * @return 返回一个Map，其中键为卡片ID，值为对应的权限对象
     */
    private Map<Long, CardPermissionDO> getPermissionMap(List<CardRespVO> cardRespVOs, Long loginUserId) {
        Set<Long> cardIds = cardRespVOs.stream().map(CardRespVO::getId).collect(Collectors.toSet());
        List<CardPermissionDO> permissions = cardPermissionMapper.selectList(
                Wrappers.lambdaQuery(CardPermissionDO.class)
                        .eq(CardPermissionDO::getUserId, loginUserId)
                        .in(CardPermissionDO::getCardId, cardIds));
        Map<Long, CardPermissionDO> permissionMap = permissions.stream().collect(Collectors.toMap(CardPermissionDO::getCardId, p -> p));
        return permissionMap;
    }

    @Override
    public PageResult<CardRespVO> getCardPageByCompanyId(CardPageReqVO pageReqVO) {
        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CardDO::getDeleted, 0);

        // 判斷是否管理员
        boolean admin = SecurityFrameworkUtils.isAdmin();
        if (!admin) {
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

            wrapper.and(w -> {
                w.or(w1 -> w1.isNull(CardDO::getUserId).isNull(CardDO::getDeptId)) // 默認所有人可見
                        .or(w1 -> w1.apply("user_id like {0}", "%" + loginUserId + "%")) // 分配给登录用户
                        .or(w1 -> {
                            // 分配给登录用户所在部门
                            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
                            List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
                            if (CollectionUtil.isNotEmpty(deptIds)) {
                                for (Long deptId : deptIds) {
                                    w1.apply("FIND_IN_SET({0}, REPLACE(dept_id, ';', ','))", deptId);
                                }
                            }

                        });
            });
        }

        if (null != pageReqVO.getCompanyId()) {
            wrapper.and(w -> w
                    .eq(CardDO::getCompanyId, pageReqVO.getCompanyId())
                    .or()
                    .eq(CardDO::getCompanyIdEn, pageReqVO.getCompanyId()));
        }

        // 添加排序
        if (StringUtils.isNotBlank(pageReqVO.getOrderBy())) {
            String[] fields = pageReqVO.getOrderBy().split("\\|");
            if (fields.length == 2) {
                String field = fields[0];
                String order = fields[1];

                // 映射字段名到 MyBatis-Plus 的 SFunction
                Map<String, SFunction<CardDO, ?>> fieldMapping = new HashMap<>();
                fieldMapping.put("createTime", CardDO::getCreateTime);
                fieldMapping.put("fullName", CardDO::getFullName);
                fieldMapping.put("companyName", CardDO::getCompanyName);

                SFunction<CardDO, ?> column = fieldMapping.get(field);
                if (column != null) {
                    boolean isAsc = "asc".equalsIgnoreCase(order);
                    if (isAsc) {
                        wrapper.orderByAsc(column);
                    } else {
                        wrapper.orderByDesc(column);
                    }
                }
            }
        }

        // 查询分页结果
        PageResult<CardDO> cardDOPageResult = cardMapper.selectPage(pageReqVO, wrapper);
        List<CardRespVO> cardRespVOs = BeanUtils.toBean(cardDOPageResult.getList(), CardRespVO.class);
        return new PageResult<>(cardRespVOs, cardDOPageResult.getTotal());
    }

    @Override
    public List<Map<String, Object>> getTableFieldNamesWithDesc() {
        Class<?> entityClass  = CardDO.class;
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        if (tableInfo == null) {
            throw new IllegalArgumentException("实体类未映射到数据库表");
        }
        String tableName = tableInfo.getTableName();

        String sql = "select column_name, column_comment from information_schema.columns where table_name = ? and table_schema = DATABASE()";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, tableName);

        // 存储字段及其注释
        Map<String, String> fieldComments = new HashMap<>();
        for (Map<String, Object> row : result) {
            String columnName = (String) row.get("column_name");
            String comment = (String) row.get("column_comment");
            // 转换为驼峰命名
            String camelCaseColumnName = toCamelCase(columnName);
            fieldComments.put(camelCaseColumnName, StrUtil.isNotBlank(comment) ? comment : columnName);
        }

        // 定义需要过滤的字段
        Set<String> excludedFields = new HashSet<>(Arrays.asList(
                "status", "id", "groupId", "imageId", "backId", "userId", "deptId",
                "anniversary", "exchangeDate", "doubleSided", "createUserId", "creator",
                "createTime", "updater", "updateTime", "deleted", "tenantId"
        ));

        // 定义分类规则
        Map<String, List<String>> categoryRules = new LinkedHashMap<>();
        categoryRules.put("基礎資訊", Arrays.asList("titleEn", "firstNameEn", "lastNameEn", "honourEn", "title",
                "firstName", "lastName", "honour", "fullName", "nickName"));
        categoryRules.put("機構資訊", Arrays.asList("companyIdEn", "companyNameEn", "departmentEn", "jobTitleEn",
                "companyId", "companyName", "department", "jobTitle"));
        categoryRules.put("聯繫資訊", Arrays.asList("phoneMobile", "phoneMainland", "email", "email2",
                "phoneOffice", "phoneDirectLine", "faxNumber"));
        categoryRules.put("地址資訊", Arrays.asList("addressLine1En", "addressLine2En", "districtEn", "areaEn",
                "countryEn", "addressLine1", "addressLine2", "address", "district", "area", "country", "province", "city"));
        categoryRules.put("行業與業務資訊", Arrays.asList("industryType", "businessType", "otherBusiness"));
        categoryRules.put("社交媒體", Arrays.asList("linkedinProfileUrl", "facebookPageUrl", "instagramUrl",
                "wechatId", "socialMedia"));
        categoryRules.put("名片管理資訊", Arrays.asList("id", "groupId", "status", "notes", "description",
                "imageUrl", "imageId", "backUrl", "backId", "userId", "deptId", "website", "instantMessaging",
                "anniversary", "exchangeDate", "doubleSided", "createUserId", "creator", "createTime", "updater",
                "updateTime", "deleted", "tenantId"));
        categoryRules.put("其他資訊", Arrays.asList("communicationHistory", "customTags"));

        // 按分类组织字段
        List<Map<String, Object>> categorizedFields = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : categoryRules.entrySet()) {
            String category = entry.getKey();
            List<String> fieldNames = entry.getValue();

            // 构建 fields 列表，过滤掉不需要的字段
            List<Map<String, String>> fields = fieldNames.stream()
                    .filter(field -> !excludedFields.contains(field)) // 过滤不需要的字段
                    .filter(fieldComments::containsKey) // 确保字段存在于数据库
                    .map(field -> {
                        Map<String, String> fieldMap = new HashMap<>();
                        fieldMap.put("fieldName", field);
                        fieldMap.put("comment", fieldComments.get(field));
                        return fieldMap;
                    })
                    .collect(Collectors.toList());

            // 只有当 fields 不为空时才添加分类
            if (!fields.isEmpty()) {
                Map<String, Object> categoryMap = new HashMap<>();
                categoryMap.put("label", category);
                categoryMap.put("fields", fields);
                categorizedFields.add(categoryMap);
            }
        }

        return categorizedFields;
    }

    /**
     * 辅助方法：将下划线命名转换为驼峰命名
     * @param str
     * @return
     */
    private String toCamelCase(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        for (char c : str.toLowerCase().toCharArray()) {
            if (c == '_') {
                nextUpperCase = true;
                continue;
            }
            if (nextUpperCase) {
                result.append(Character.toUpperCase(c));
                nextUpperCase = false;
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    @Override
    public void batchUpdateField(List<Long> ids, String fieldName, String fieldValue) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("ID列表不能为空");
        }

        if (StringUtils.isBlank(fieldName) || fieldValue == null) {
            throw new IllegalArgumentException("字段名或字段值不能为空");
        }

        // 动态构建更新条件
        LambdaUpdateWrapper<CardDO> updateWrapper = Wrappers.lambdaUpdate();
        switch (fieldName) {
            case "customTags": {
                updateWrapper.set(CardDO::getCustomTags, fieldValue);
                break;
            }
            case "creator": {
                if (fieldValue != null && !fieldValue.isEmpty()) {
                    AdminUserRespDTO user = adminUserApi.getUser(Long.parseLong(fieldValue));
                    updateWrapper.set(CardDO::getCreateUserId, user.getId());
                    updateWrapper.set(CardDO::getCreator, user.getNickname());
                } else {
                    AdminUserRespDTO user = adminUserApi.getUser(1L);
                    updateWrapper.set(CardDO::getCreateUserId, user.getId());
                    updateWrapper.set(CardDO::getCreator, user.getNickname());
                }
                break;
            }
            default:
                throw new IllegalArgumentException("不支持的字段名：" + fieldName);
        }
        updateWrapper.in(CardDO::getId, ids);
        // 执行更新
        cardMapper.update(null, updateWrapper);
    }

    /**
     * 将标签对象转换为标签数据传输对象
     * @param tag
     * @return
     */
    private TagsDO convertTagToDTO(TagsDO tag) {
        TagsDO dto = new TagsDO();
        dto.setId(tag.getId());
        dto.setName(tag.getName());
        return dto;
    }

    /**
     * 将CardDO转换为CardRespVO
     */
    private CardRespVO convertCardDOToRespVO(CardDO cardDO) {
        if (cardDO == null) {
            return null;
        }
        CardRespVO cardRespVO = BeanUtils.toBean(cardDO, CardRespVO.class);

        populateTags(cardRespVO);
        populateDepts(cardRespVO);
        populateUsers(cardRespVO);
        return cardRespVO;
    }

    /**
     * 将CardDO列表转换为CardRespVO列表
     */
    private List<CardRespVO> convertCardDOListToRespVOList(List<CardDO> cardDOs) {
        if (cardDOs == null || cardDOs.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 转换为基本VO并收集所有tagIds
        List<CardRespVO> cardRespVOs = BeanUtils.toBean(cardDOs, CardRespVO.class);
        Set<Long> allTagIds = new HashSet<>();
        for (CardRespVO card : cardRespVOs) {
            List<Long> tagIds = parseIds(card.getCommunicationHistory());
            allTagIds.addAll(tagIds);
        }

        // 2. 批量查询所有标签
        Map<Long, TagsDO> tagMap = fetchTagsMap(allTagIds);

        // 3. 为每个Card填充标签
        cardRespVOs.forEach(card -> populateTags(card, tagMap));

        return cardRespVOs;
    }

    /**
     * 填充CardRespVO的标签信息
     */
    private void populateTags(CardRespVO cardRespVO) {
        List<Long> tagIds = parseIds(cardRespVO.getCustomTags());
        if (tagIds.isEmpty()) {
            return;
        }

        List<TagsDO> tags = tagsMapper.selectTagsByIds(tagIds);
        if (tags == null || tags.isEmpty()) {
            return;
        }

        List<TagsRespVO> tagRespVOs = new ArrayList<>(tags.size());
        for (TagsDO tag : tags) {
            tagRespVOs.add(BeanUtils.toBean(convertTagToDTO(tag), TagsRespVO.class));
        }
        cardRespVO.setTags(tagRespVOs);
    }

    private void populateDepts(CardRespVO cardRespVO) {
        List<Long> deptIds = parseIds(cardRespVO.getDeptId());
        if (deptIds.isEmpty()) {
            return;
        }
        cardRespVO.setDepts(deptApi.getDeptListByIds(deptIds));
    }

    private void populateUsers(CardRespVO cardRespVO) {
        String userId = cardRespVO.getCreateUserId() + "";
        if (StringUtils.isNotBlank(cardRespVO.getUserId())) {
            userId += ";" + cardRespVO.getUserId();
        }
        List<Long> userIds = parseIds(userId);
        if (userIds.isEmpty()) {
            return;
        }
        cardRespVO.setUsers(adminUserApi.getUserListByIds(userIds));
    }

    /**
     * 填充CardRespVO的标签信息（使用预加载的tagMap）
     */
    private void populateTags(CardRespVO cardRespVO, Map<Long, TagsDO> tagMap) {
        List<Long> tagIds = parseIds(cardRespVO.getCommunicationHistory());
        if (!tagIds.isEmpty()) {
            List<TagsRespVO> tagRespVOs = tagIds.stream()
                    .map(tagMap::get)
                    .filter(Objects::nonNull)
                    .map(this::convertTagToDTO)
                    .map(tag -> BeanUtils.toBean(tag, TagsRespVO.class))
                    .collect(Collectors.toList());
            if (!tagRespVOs.isEmpty()) {
                cardRespVO.setTags(tagRespVOs);
            }
        }
    }

    /**
     * 解析Ids
     */
    private List<Long> parseIds(String ids) {
        if (StringUtils.isBlank(ids)) {
            return Collections.emptyList();
        }
        try {
            return Arrays.stream(ids.split(";"))
                    .filter(StringUtils::isNotBlank)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            log.error("解析Ids时发生异常：{}", ids, e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量获取标签Map
     */
    private Map<Long, TagsDO> fetchTagsMap(Set<Long> tagIds) {
        if (tagIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<TagsDO> tags = tagsMapper.selectTagsByIds(new ArrayList<>(tagIds));
        return tags.stream().collect(Collectors.toMap(TagsDO::getId, tag -> tag));
    }

    @Override
    public void mergeCards(List<Long> ids) {
        // 获取主名片和其他名片
        Long mainCardId = ids.get(0);
        CardDO cardDO = cardMapper.selectById(mainCardId);

        List<Long> otherCardIds = ids.subList(1, ids.size());
        for (Long id : otherCardIds) {
            CardSaveReqVO cardSaveReqVO = new CardSaveReqVO();
            cardSaveReqVO.setId(id);
            cardSaveReqVO.setGroupId(cardDO.getGroupId());

            // 更新
            CardDO updateObj = BeanUtils.toBean(cardSaveReqVO, CardDO.class);
            cardMapper.updateById(updateObj);
        }
    }

    @Override
    public PageResult<CardDO> getCardTrash(CardPageReqVO reqVO) {
        // 创建分页对象
        Page<CardDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CardDO::getDeleted, 1);

        if (StringUtils.isNotBlank(reqVO.getKeyword())) {
            wrapper.and(w1 -> w1
                    .or(w -> w.like(CardDO::getFirstName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getLastName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getFullName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getCompanyName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getEmail, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getPhoneMobile, reqVO.getKeyword()))
            );
        }
        wrapper.orderByDesc(CardDO::getUpdateTime);
        // 执行分页查询
        cardMapper.selectPage(page, wrapper);
        // 返回分页结果
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public void permanentlyDelete(List<Long> ids) {
        cardMapper.deleteByIds(ids);
    }

    @Override
    public void restore(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("ID列表不能为空");
        }

        //批量更新 deleted 字段为 0 （未删除状态）
        cardMapper.update(Wrappers.<CardDO>lambdaUpdate()
                .set(CardDO::getDeleted, 0)// 设置删除状态为0（未删除）
                .in(CardDO::getId, ids));// 设置要更新的ID列表
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CardImportRespVO importCardList(List<CardImportExcelVO> importCards, boolean isUpdateSupport) {
        long start = System.currentTimeMillis();
        log.info("开始导入名片列表，数量: {}, 支持更新: {}", importCards.size(), isUpdateSupport);
        // 1. 初始验证
        if (CollUtil.isEmpty(importCards)) {
            throw exception(ErrorCodeConstants.CARD_IMPORT_LIST_IS_EMPTY);
        }

        // 2. 准备响应对象
        CardImportRespVO respVO = CardImportRespVO.builder()
                .createCardNames(new ArrayList<>())
                .updateCardNames(new ArrayList<>())
                .failureCardNames(new LinkedHashMap<>())
                .build();

        // 3. 获取当前用户信息（只获取一次）
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        String currentUserNickname = SecurityFrameworkUtils.getLoginUserNickname();

        // 4. 处理每张名片
        for (int i = 0; i < importCards.size(); i++) {
            CardImportExcelVO importCard = importCards.get(i);
            String rowKey = "第" + (i + 2) + "行"; // Excel从第2行开始（含表头）

            try {

                String fullName = getFullName(importCard);

                // 4.1 验证必填字段
                /*if (!validateRequiredFields(importCard, respVO, rowKey)) {
                    continue;
                }*/

                // 4.2 转换为DO对象
                CardDO cardDO = buildCardDO(importCard, currentUserId, currentUserNickname, fullName);

                // 4.3 处理公司信息（分别处理英文和中文公司名）
                JSONObject companys = processCompany(importCard, currentUserNickname);
                if (companys != null) {
                    cardDO.setCompanyId(companys.getLong("companyId"));
                    cardDO.setCompanyName(companys.getString("companyName"));
                    cardDO.setCompanyIdEn(companys.getLong("companyIdEn"));
                    cardDO.setCompanyNameEn(companys.getString("companyNameEn"));
                }

                // 4.4 检查并处理名片（创建或更新）
                processCard(cardDO, fullName, isUpdateSupport, respVO);

            } catch (Exception e) {
                e.printStackTrace();
                respVO.getFailureCardNames().put(rowKey, "導入失敗: " + e.getMessage());
            }
        }

        // 5. 检查是否全部导入失败
        if (!respVO.getFailureCardNames().isEmpty() && respVO.getCreateCardNames().isEmpty() && respVO.getUpdateCardNames().isEmpty()) {
            throw exception(ErrorCodeConstants.CARD_IMPORT_ALL_FAILED);
        }

        // 6. 记录导入日志
        long end = System.currentTimeMillis();
        log.info("名片导入完成，耗时: {} 毫秒", (end - start));
        return respVO;
    }

    @Nullable
    private String getFullName(CardImportExcelVO importCard) {
        String fullName = null;
        if (StringUtils.isNotBlank(importCard.getFirstName()) || StringUtils.isNotBlank(importCard.getLastName())) {
            fullName = String.join(" ",
                    StringUtils.defaultString(importCard.getFirstName()),
                    StringUtils.defaultString(importCard.getLastName())).trim();
        } else if (StringUtils.isNotBlank(importCard.getFirstNameEn()) || StringUtils.isNotBlank(importCard.getLastNameEn())) {
            fullName = String.join(" ",
                    StringUtils.defaultString(importCard.getFirstNameEn()),
                    StringUtils.defaultString(importCard.getLastNameEn())).trim();
        }
        return fullName;
    }

    /**
     * 验证必填字段
     */
    private boolean validateRequiredFields(CardImportExcelVO importCard, CardImportRespVO respVO, String rowKey) {
        if (StrUtil.isEmpty(importCard.getFirstName()) || StrUtil.isEmpty(importCard.getLastName())) {
            respVO.getFailureCardNames().put(rowKey, "名字和姓氏為必填項");
            return false;
        }
        return true;
    }

    /**
     * 构建CardDO对象
     */
    private CardDO buildCardDO(CardImportExcelVO importCard, Long userId, String nickname, String fullName) {
        CardDO cardDO = BeanUtils.toBean(importCard, CardDO.class);
        String groupId = UUID.randomUUID().toString().replaceAll("-", "");
        cardDO.setGroupId(groupId);
        cardDO.setFullName(fullName);
        cardDO.setCreator(nickname);
        cardDO.setCreateUserId(userId);
        return cardDO;
    }

    /**
     * 处理公司信息（分别处理英文和中文公司名）
     */
    private JSONObject processCompany(CardImportExcelVO importCard, String creator) {
        JSONObject companys = new JSONObject();
        String companyNameEn = StrUtil.blankToDefault(importCard.getCompanyNameEn(), "");
        String companyName = StrUtil.blankToDefault(importCard.getCompanyName(), "");

        // 处理英文公司名
        if (StrUtil.isNotBlank(companyNameEn)) {
            processCompanyName(companys, companyNameEn, importCard.getPhoneOffice(), creator,
                    "en", "companyIdEn", "companyNameEn");
        }

        // 处理中文公司名
        if (StrUtil.isNotBlank(companyName)) {
            processCompanyName(companys, companyName, importCard.getPhoneOffice(), creator,
                    "zh","companyId", "companyName");
        }

        return companys;
    }

    /**
     * 处理公司名逻辑，创建或获取公司并存入 JSONObject
     *
     * @param companys     JSONObject 用于存储结果
     * @param name         公司名
     * @param phone        公司电话
     * @param creator      创建者
     * @param idKey        JSONObject 中存储 ID 的键
     * @param nameKey      JSONObject 中存储名称的键
     */
    private void processCompanyName(JSONObject companys, String name, String phone, String creator,String languageType, String idKey, String nameKey) {
        CompanyDO companyDO = companyService.getCompanyByName(name);

        if (companyDO == null) {
            companyDO = new CompanyDO();
            companyDO.setName(name);
            companyDO.setLanguageType(languageType);
            companyDO.setPhone(StrUtil.blankToDefault(phone, ""));
            companyDO.setParentId(0L);
            companyDO.setAddress("");
            companyDO.setCreator(creator);
            Long id = companyService.createCompany(BeanUtils.toBean(companyDO, CompanySaveReqVO.class));
            if (id == null) {
                log.error("创建公司 [{}] 失败，返回 ID 为空", name);
                throw new IllegalStateException("Failed to create company: " + name);
            }
            companyDO.setId(id);
            log.info("创建公司 [{}]，ID：{}", name, id);
        }

        companys.put(idKey, companyDO.getId());
        companys.put(nameKey, companyDO.getName());
    }

    /**
     * 处理名片创建或更新
     */
    private void processCard(CardDO cardDO, String fullName, boolean isUpdateSupport, CardImportRespVO respVO) {
        if (isUpdateSupport) {
            CardDO existingCard = findExistingCard(cardDO);
            if (existingCard != null) {
                cardDO.setId(existingCard.getId());
                cardMapper.updateById(cardDO);
                respVO.getUpdateCardNames().add(fullName);
            } else if (existingCard == null) {
                cardMapper.insert(cardDO);
                respVO.getCreateCardNames().add(fullName);
            } else {
                respVO.getFailureCardNames().put(fullName, "名片已存在且不允許更新");
            }

        } else {
            cardMapper.insert(cardDO);
            respVO.getCreateCardNames().add(fullName);
        }

    }

    /**
     * 查找现有名片
     */
    private CardDO findExistingCard(CardDO cardVO) {
        if (StrUtil.isNotEmpty(cardVO.getEmail()) && StrUtil.isNotEmpty(cardVO.getFullName())) {
            return cardMapper.selectOne(Wrappers.<CardDO>lambdaQuery()
                    .eq(CardDO::getDeleted, 0)
                    .eq(CardDO::getCreateUserId, cardVO.getCreateUserId())
                    .eq(CardDO::getEmail, cardVO.getEmail())
                    .eq(CardDO::getFullName, cardVO.getFullName())
                    .last("LIMIT 1"));
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> countCardsByCreatorInDept(Long companyId) {
        return cardMapper.countCardsByCreator(companyId);
    }

    @Override
    public List<CardDO> getCardListByCompanyId(Long companyId) {
        return cardMapper.getCardListByCompanyId(companyId);
    }

    @Override
    public List<Map<String, Object>> selectByGroudId(String groupId) {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CardDO::getDeleted, 0);
        wrapper.eq(CardDO::getGroupId, groupId);

        if (!isAdmin) {
            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
            List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
            wrapper.and(w -> {
                w.or(w1 -> w1.isNull(CardDO::getUserId).isNull(CardDO::getDeptId)) // 默认所有人可见
                        .or(w1 -> w1.apply("user_id like {0}", "%" + loginUserId + "%")) // 分配给登录用户
                        .or(w1 -> w1.eq(CardDO::getCreateUserId, loginUserId)) // 创建者是登录用户
                        .or(w1 -> {
                            // 分配给登录用户所在部门
                            if (CollectionUtil.isNotEmpty(deptIds)) {
                                for (Long deptId : deptIds) {
                                    w1.apply("FIND_IN_SET({0}, REPLACE(dept_id, ';', ','))", deptId);
                                }
                            }
                        });
            });
        }

        List<CardDO> cards = cardMapper.selectList(wrapper);

        List<Map<String, Object>> rtnList = cards.stream().map(cardDO -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", cardDO.getId());
            map.put("jobTitle", cardDO.getJobTitle());
            map.put("jobTitleEn", cardDO.getJobTitleEn());
            return map;
        }).collect(Collectors.toList());
        return rtnList;
    }

    @Override
    public PageResult<Map<String, Object>> getDistrictPage(PcmCardPageReqVO reqVO) {
        // 判断当前用户是否是管理员
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        if (!isAdmin) {
            // 非管理员：设置数据权限
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

            // 设置 userId
            if (loginUserId != null) {
                reqVO.setUserId(String.valueOf(loginUserId));
                reqVO.setCreateUserId(loginUserId);
            }

            // 获取递归部门 ID 并设置 deptId
            if (currentDeptId != null) {
                List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
                if (CollectionUtils.isNotEmpty(deptIds)) {
                    String deptIdStr = StringUtils.join(deptIds, ";");
                    reqVO.setDeptId(deptIdStr);
                }
            }
        }

        // 创建分页对象
        Page<CardDO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());

        // 执行分页查询
        List<Map<String, Object>> list = cardMapper.selectDistinctGroupIdPage(page, reqVO);

        List<Long> ids = list.stream().map(map -> (Long) map.get("id")).collect(Collectors.toList());
        // 返回分页结果
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<Long> getDistrictIds(PcmCardPageReqVO pageReqVO) {
        // 判断当前用户是否是管理员
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        if (!isAdmin) {
            // 非管理员：设置数据权限
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

            // 设置 userId
            if (loginUserId != null) {
                pageReqVO.setUserId(String.valueOf(loginUserId));
                pageReqVO.setCreateUserId(loginUserId);
            }

            // 获取递归部门 ID 并设置 deptId
            if (currentDeptId != null) {
                List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
                if (CollectionUtils.isNotEmpty(deptIds)) {
                    String deptIdStr = StringUtils.join(deptIds, ";");
                    pageReqVO.setDeptId(deptIdStr);
                }
            }
        }

        // 执行分页查询
        List<Map<String, Object>> list = cardMapper.selectDistinctGroupIds(pageReqVO);
        List<Long> ids = list.stream()
                .map(map -> (Long) map.get("id"))
                .collect(Collectors.toList());
        log.info("getDistrictIds：{}", JSON.toJSON(ids));
        return ids;
    }


    @Override
    public Long countCardByUserId(Long userId) {
        LambdaQueryWrapper<CardDO> wrapper = Wrappers.<CardDO>lambdaQuery()
                .eq(CardDO::getCreator, userId);
        return cardMapper.selectCount(wrapper);
    }

    @Override
    public PageResult<CardRespVO> exportCardExcel(CardPageReqVO pageReqVO) {
        // 判断当前用户是否是管理员
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        // 非管理员用户设置部门过滤条件
        if (!isAdmin) {
            Long currentDeptId = SecurityFrameworkUtils.getLoginUserDeptId();
            pageReqVO.setUserId(String.valueOf(loginUserId));
            pageReqVO.setCreateUserId(new Long[]{loginUserId});

            // 获取递归部门 ID 并设置 deptId
            if (currentDeptId != null) {
                List<Long> deptIds = deptApi.getDeptListByParentId(currentDeptId);
                if (CollectionUtils.isNotEmpty(deptIds)) {
                    String deptIdStr = StringUtils.join(deptIds, ";");
                    pageReqVO.setDeptId(deptIdStr);
                    pageReqVO.setDeptIds(deptIds.stream().collect(Collectors.toSet()));
                }
            }
        }

        // 查询分页数据并转换为VO
        PageResult<CardDO> cardDOPageResult = cardMapper.selectPage(pageReqVO);
        List<CardRespVO> cardRespVOs = BeanUtils.toBean(cardDOPageResult.getList(), CardRespVO.class);

        // 获取字典数据
        List<DictDataRespDTO> cardTitleCn = dictDataApi.getDictDataList("card_title_cn");
        List<DictDataRespDTO> cardTitleEn = dictDataApi.getDictDataList("card_title_en");
        Map<String, String> titleMapCn = cardTitleCn.stream()
                .collect(Collectors.toMap(
                        DictDataRespDTO::getValue,  // key: value 字段
                        DictDataRespDTO::getLabel,  // value: label 字段
                        (v1, v2) -> v1)             // 合并策略：如果有重复 key，保留第一个
                );

        Map<String, String> titleMapEn = cardTitleEn.stream()
                .collect(Collectors.toMap(
                        DictDataRespDTO::getValue,  // key: value 字段
                        DictDataRespDTO::getLabel,  // value: label 字段
                        (v1, v2) -> v1)             // 合并策略：如果有重复 key，保留第一个
                );

        // 批量权限检查
        if (!isAdmin) {
            // 批量查询权限
            Map<Long, CardPermissionDO> permissionMap = getPermissionMap(cardRespVOs, loginUserId);

            // 批量处理权限（为导出使用 null 掩码）
            cardRespVOs.forEach(cardRespVO -> {
                cardRespVO.setTitle(titleMapCn.getOrDefault(cardRespVO.getTitle(), cardRespVO.getTitle()));
                cardRespVO.setTitleEn(titleMapEn.getOrDefault(cardRespVO.getTitleEn(), cardRespVO.getTitleEn()));
                if (!loginUserId.equals(cardRespVO.getCreateUserId())) {
                    hasPermission(loginUserId, cardRespVO.getId(), cardRespVO, permissionMap, true);
                } else {
                    cardRespVO.setIsEdit(true);
                }
            });
        } else {
            // 管理员设置所有记录可编辑
            cardRespVOs.forEach(cardRespVO -> cardRespVO.setIsEdit(true));
        }

        return new PageResult<>(cardRespVOs, cardDOPageResult.getTotal());
    }

    @Override
    public Long getPreviousCardId(Long cardId) {
        // 1. 基本校验
        if (cardId == null) {
            return null; // 或者抛出异常，根据业务需求
        }

        // 2. 获取用户上下文
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();

        // 3. 检查是否为权限范围内最小值
        LambdaQueryWrapperX<CardDO> minCheckWrapper = new LambdaQueryWrapperX<>();
        minCheckWrapper.lt(CardDO::getId, cardId)       // id < 当前cardId
                .eq(CardDO::getDeleted, false);  // 未删除
        if (!isAdmin) {
            minCheckWrapper.and(wrapper -> wrapper.eq(CardDO::getCreateUserId, currentUserId)
                    .or()
                    .like(CardDO::getUserId, String.valueOf(currentUserId)));
        }
        minCheckWrapper.last("LIMIT 1"); // 只需检查是否存在

        // 如果没有小于 cardId 的记录，说明 cardId 是权限内的最小值
        boolean isMinCard = cardMapper.selectCount(minCheckWrapper) == 0;
        log.info("小于 cardId {} 的值是否存在: {}", cardId, isMinCard);
        if (isMinCard) {
            return cardId;
        }

        // 4. 构造查询前一个名片的条件
        LambdaQueryWrapperX<CardDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.lt(CardDO::getId, cardId)       // id < 当前cardId
                .eq(CardDO::getDeleted, false)   // 未删除
                .orderByDesc(CardDO::getId)      // 按id降序
                .last("LIMIT 1");                // 只取一条记录

        if (!isAdmin) {
            queryWrapper.and(wrapper -> wrapper.eq(CardDO::getCreateUserId, currentUserId)
                    .or()
                    .like(CardDO::getUserId, String.valueOf(currentUserId)));
        }

        // 5. 执行查询
        CardDO previousCard = cardMapper.selectOne(queryWrapper);
        return previousCard != null ? previousCard.getId() : null;
    }

    @Override
    public Long checkCardUnique(CardPageReqVO cardPageReqVO) {
        if (null == cardPageReqVO) {
            return null;
        }
        // 如果所有字段都为空，返回 false（避免查询所有记录）
        if (StringUtils.isAllBlank(cardPageReqVO.getFirstName(),
                cardPageReqVO.getLastName(),
                cardPageReqVO.getFirstNameEn(),
                cardPageReqVO.getLastNameEn())) {
            return null;
        }
        log.info("校验名片中文名, fristName: {}, lastName: {}", cardPageReqVO.getFirstName(), cardPageReqVO.getLastName());
        log.info("校验名片英文名, fristNameEn: {}, lastNameEn: {}", cardPageReqVO.getFirstNameEn(), cardPageReqVO.getLastNameEn());
        List<CardDO> list = this.selectUniqueCard(cardPageReqVO);
        log.info("校验名片唯一，当前重复名片个数: {}", list.size());
        return list != null && list.size() > 0 ? list.get(0).getId() : null;
    }

    /**
     * 查询唯一名片
     */
    @Override
    public List<CardDO> selectUniqueCard(CardPageReqVO cardPageReqVO) {
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();

        LambdaQueryWrapperX<CardDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(CardDO::getDeleted, false);

        // 检查中文和英文字段是否存在
        boolean hasChineseName = StringUtils.isNotBlank(cardPageReqVO.getFirstName()) || StringUtils.isNotBlank(cardPageReqVO.getLastName());
        boolean hasEnglishName = StringUtils.isNotBlank(cardPageReqVO.getFirstNameEn()) || StringUtils.isNotBlank(cardPageReqVO.getLastNameEn());

        // 优先校验中文姓名
        if (hasChineseName) {
            queryWrapperX.eqIfPresent(CardDO::getFirstName, cardPageReqVO.getFirstName()); // 名字匹配
            queryWrapperX.eqIfPresent(CardDO::getLastName, cardPageReqVO.getLastName()); // 姓氏匹配
        } else if (hasEnglishName) {
            // 如果没有中文姓名但有英文姓名，则校验英文姓名
            queryWrapperX.eqIfPresent(CardDO::getFirstNameEn, cardPageReqVO.getFirstNameEn()); // 英文名字匹配
            queryWrapperX.eqIfPresent(CardDO::getLastNameEn, cardPageReqVO.getLastNameEn()); // 英文姓氏匹配
        }

        if (!isAdmin) {

            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            Long loginUserDeptId = SecurityFrameworkUtils.getLoginUserDeptId();

            queryWrapperX.and(w -> {
                w.or(w1 -> w1.isNull(CardDO::getUserId).isNull(CardDO::getDeptId)) // 默認所有人都有權限查看
                        .or(w1 -> w1.apply("user_id like {0}", "%" + loginUserId + "%")) // 分配給登錄用戶
                        .or(w1 -> {
                            // 分配给登录用户所在部门
                            List<Long> deptIds = deptApi.getDeptListByParentId(loginUserDeptId);
                            if (CollectionUtil.isNotEmpty(deptIds)) {
                                for (Long deptId : deptIds) {
                                    w1.apply("FIND_IN_SET({0}, REPLACE(dept_id, ';', ','))", deptId);
                                }
                            }
                        }); // 分配給登錄用戶所在部門
            });

        }

        // 查询记录数
        return cardMapper.selectList(queryWrapperX);
    }

    public List<CardRespVO> getCardsByIds(List<Long> ids) {

        // 判断当前用户是否是管理员
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();

        List<CardDO> cardDOS = cardMapper.selectBatchIds(ids);
        List<CardRespVO> cardRespVOs = BeanUtils.toBean(cardDOS, CardRespVO.class);

        // 获取字典数据
        List<DictDataRespDTO> cardTitleCn = dictDataApi.getDictDataList("card_title_cn");
        List<DictDataRespDTO> cardTitleEn = dictDataApi.getDictDataList("card_title_en");
        Map<String, String> titleMapCn = cardTitleCn.stream()
                .collect(Collectors.toMap(
                        DictDataRespDTO::getValue,  // key: value 字段
                        DictDataRespDTO::getLabel,  // value: label 字段
                        (v1, v2) -> v1)             // 合并策略：如果有重复 key，保留第一个
                );

        Map<String, String> titleMapEn = cardTitleEn.stream()
                .collect(Collectors.toMap(
                        DictDataRespDTO::getValue,  // key: value 字段
                        DictDataRespDTO::getLabel,  // value: label 字段
                        (v1, v2) -> v1)             // 合并策略：如果有重复 key，保留第一个
                );

        // 批量权限检查
        if (!isAdmin) {
            // 批量查询权限
            Map<Long, CardPermissionDO> permissionMap = getPermissionMap(cardRespVOs, loginUserId);

            // 批量处理权限（为导出使用 null 掩码）
            cardRespVOs.forEach(cardRespVO -> {
                cardRespVO.setTitle(titleMapCn.getOrDefault(cardRespVO.getTitle(), cardRespVO.getTitle()));
                cardRespVO.setTitleEn(titleMapEn.getOrDefault(cardRespVO.getTitleEn(), cardRespVO.getTitleEn()));
                if (!loginUserId.equals(cardRespVO.getCreateUserId())) {
                    hasPermission(loginUserId, cardRespVO.getId(), cardRespVO, permissionMap, true);
                } else {
                    cardRespVO.setIsEdit(true);
                }
            });
        } else {
            // 管理员设置所有记录可编辑
            cardRespVOs.forEach(cardRespVO -> cardRespVO.setIsEdit(true));
        }

        return cardRespVOs;
    }

}
