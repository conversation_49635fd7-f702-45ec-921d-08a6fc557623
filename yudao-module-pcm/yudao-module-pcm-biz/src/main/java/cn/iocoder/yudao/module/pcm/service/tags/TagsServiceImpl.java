package cn.iocoder.yudao.module.pcm.service.tags;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.TagsPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.tags.vo.TagsSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.tags.TagsDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.tags.TagsMapper;
import cn.iocoder.yudao.module.pcm.service.card.CardService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.TAGS_ALREADY_IN_USE;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.TAGS_NOT_EXISTS;

/**
 * 标签 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TagsServiceImpl implements TagsService {

    @Resource
    private TagsMapper tagsMapper;

    @Resource
    private CardService cardService;

    @Override
    public Long createTags(TagsSaveReqVO createReqVO) {
        // 插入
        TagsDO tags = BeanUtils.toBean(createReqVO, TagsDO.class);
        tagsMapper.insert(tags);
        // 返回
        return tags.getId();
    }

    @Override
    public void updateTags(TagsSaveReqVO updateReqVO) {
        // 校验存在
        validateTagsExists(updateReqVO.getId());
        // 更新
        TagsDO updateObj = BeanUtils.toBean(updateReqVO, TagsDO.class);
        tagsMapper.updateById(updateObj);
    }

    @Override
    public void deleteTags(Long id) {
        // 校验存在
        validateTagsExists(id);
        // 校验标签是否被使用过
        validateNotUsed(id);
        // 删除
        tagsMapper.update(Wrappers.<TagsDO>lambdaUpdate()
                .set(TagsDO::getDeleted, 1)
                .eq(TagsDO::getId, id));
    }

    /**
     * 校验标签是否存在
     * @param id
     */
    private void validateTagsExists(Long id) {
        if (tagsMapper.selectById(id) == null) {
            throw exception(TAGS_NOT_EXISTS);
        }
    }

    /**
     * 校验标签是否被使用
     * @param id
     */
    private void validateNotUsed(Long id) {
        Boolean isUsed = cardService.validateTagUsed(id);
        if (isUsed) {
            throw exception(TAGS_ALREADY_IN_USE);
        }
    }

    @Override
    public TagsDO getTags(Long id) {
        return tagsMapper.selectById(id);
    }

    @Override
    public List<TagsDO> getTagByCompanyId(Long companyId) {
        return tagsMapper.selectListByCompanyId(companyId);
    }

    @Override
    public PageResult<TagsDO> getTagsPage(TagsPageReqVO pageReqVO) {
        return tagsMapper.selectPage(pageReqVO);
    }

}