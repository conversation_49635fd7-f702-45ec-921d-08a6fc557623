package cn.iocoder.yudao.module.pcm.framework.web.config;

import cn.iocoder.yudao.framework.swagger.config.YudaoSwaggerAutoConfiguration;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * pcm 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class PcmWebConfiguration {

    /**
     * pcm 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi pcmGroupedOpenApi() {
        return YudaoSwaggerAutoConfiguration.buildGroupedOpenApi("pcm");
    }

}
