package cn.iocoder.yudao.module.pcm.dal.mysql.meetingpermission;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meetingpermission.MeetingPermissionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo.*;

import java.util.List;

@Mapper
public interface MeetingPermissionMapper extends BaseMapperX<MeetingPermissionDO> {

    default PageResult<MeetingPermissionDO> selectPage(MeetingPermissionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeetingPermissionDO>()
                .eqIfPresent(MeetingPermissionDO::getMeetingId, reqVO.getMeetingId())
                .eqIfPresent(MeetingPermissionDO::getUserId, reqVO.getUserId())
                .eqIfPresent(MeetingPermissionDO::getDeptId, reqVO.getDeptId())
                .eqIfPresent(MeetingPermissionDO::getRoleId, reqVO.getRoleId())
                .betweenIfPresent(MeetingPermissionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MeetingPermissionDO::getId));
    }

    /**
     * 删除指定会议记录的所有权限
     *
     * @param meetingId 会议记录ID
     */
    void deleteByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 校验用户是否有权限查看会议记录
     *
     * @param meetingId 会议记录ID
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param roleIds 角色ID列表
     * @return 权限数量
     */
    int hasPermission(@Param("meetingId") Long meetingId,
                      @Param("userId") Long userId,
                      @Param("deptId") Long deptId,
                      @Param("roleIds") List<Long> roleIds);


}