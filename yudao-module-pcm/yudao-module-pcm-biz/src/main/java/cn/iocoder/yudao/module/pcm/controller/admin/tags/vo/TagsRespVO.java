package cn.iocoder.yudao.module.pcm.controller.admin.tags.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 标签 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TagsRespVO {

    @Schema(description = "标签ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000001")
    @ExcelProperty("标签ID")
    private Long id;

    @Schema(description = "标签名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "河南中原高速公路")
    @ExcelProperty("标签名称")
    private String name;

    @Schema(description = "标签描述", example = "河南中原高速公路")
    private String description;

}
