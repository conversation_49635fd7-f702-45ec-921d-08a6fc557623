package cn.iocoder.yudao.module.pcm.controller.admin.export.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import lombok.Data;

/**
 * 審批列表查詢參數物件
 */
@Data
public class ApprovalPageReqVO extends PageParam {
    private Long id;
    private String applicant;
    private String status; // 審批狀態（可選：PENDING、APPROVED、REJECTED）
    private String exportStatus;
    private String[] createTime; // 定義為 String 數組，接收時間範圍
}
