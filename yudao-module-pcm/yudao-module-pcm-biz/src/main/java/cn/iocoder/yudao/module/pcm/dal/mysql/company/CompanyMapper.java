package cn.iocoder.yudao.module.pcm.dal.mysql.company;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.pcm.dal.dataobject.company.CompanyDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.pcm.controller.admin.company.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 公司 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CompanyMapper extends BaseMapperX<CompanyDO> {

    default PageResult<CompanyDO> selectPage(CompanyPageReqVO reqVO) {
        reqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        return selectPage(reqVO, null, new LambdaQueryWrapperX<CompanyDO>()
                .eq(CompanyDO::getDeleted, false)
                .eq(CompanyDO::getLanguageType, "zh")
                .likeIfPresent(CompanyDO::getName, reqVO.getName())
                .orderByDesc(CompanyDO::getCreateTime));
    }

    /**
     * 查询某个父ID下的所有直接子机构
     * @param parentId
     * @return
     */
    @Select("SELECT id, name, parent_id FROM pcm_company WHERE deleted = 0 AND parent_id = #{parentId}")
    List<CompanyDO> selectByParentId(@Param("parentId") Long parentId);

    CompanyDO selectById(@Param("id") Long id);

    /**
     * 获取所有公司信息
     * @param type
     * @return
     */
    default List<CompanyDO> selectSimpleList(String type) {
        return selectList(new LambdaQueryWrapperX<CompanyDO>()
                .eq(CompanyDO::getDeleted, false)
                .eq(CompanyDO::getLanguageType, type));
    }

    /**
     * 获取公司名称，优先取中文名称
     * @return 公司名称
     */
    List<Map<String, Object>> getCompanyNamePreferChinese();

}