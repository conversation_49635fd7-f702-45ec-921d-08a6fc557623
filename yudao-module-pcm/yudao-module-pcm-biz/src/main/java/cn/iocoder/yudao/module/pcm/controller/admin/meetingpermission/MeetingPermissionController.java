package cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meetingpermission.MeetingPermissionDO;
import cn.iocoder.yudao.module.pcm.service.meetingpermission.MeetingPermissionService;

/*@Tag(name = "管理后台 - 商谈记录权限")
@RestController
@RequestMapping("/pcm/meeting-permission")
@Validated*/
public class MeetingPermissionController {

    /*@Resource
    private MeetingPermissionService meetingPermissionService;

    @PostMapping("/create")
    @Operation(summary = "创建商谈记录权限")
    @PreAuthorize("@ss.hasPermission('pcm:meeting-permission:create')")
    public CommonResult<Long> createMeetingPermission(@Valid @RequestBody MeetingPermissionSaveReqVO createReqVO) {
        return success(meetingPermissionService.createMeetingPermission(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商谈记录权限")
    @PreAuthorize("@ss.hasPermission('pcm:meeting-permission:update')")
    public CommonResult<Boolean> updateMeetingPermission(@Valid @RequestBody MeetingPermissionSaveReqVO updateReqVO) {
        meetingPermissionService.updateMeetingPermission(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商谈记录权限")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pcm:meeting-permission:delete')")
    public CommonResult<Boolean> deleteMeetingPermission(@RequestParam("id") Long id) {
        meetingPermissionService.deleteMeetingPermission(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商谈记录权限")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pcm:meeting-permission:query')")
    public CommonResult<MeetingPermissionRespVO> getMeetingPermission(@RequestParam("id") Long id) {
        MeetingPermissionDO meetingPermission = meetingPermissionService.getMeetingPermission(id);
        return success(BeanUtils.toBean(meetingPermission, MeetingPermissionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商谈记录权限分页")
    @PreAuthorize("@ss.hasPermission('pcm:meeting-permission:query')")
    public CommonResult<PageResult<MeetingPermissionRespVO>> getMeetingPermissionPage(@Valid MeetingPermissionPageReqVO pageReqVO) {
        PageResult<MeetingPermissionDO> pageResult = meetingPermissionService.getMeetingPermissionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MeetingPermissionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商谈记录权限 Excel")
    @PreAuthorize("@ss.hasPermission('pcm:meeting-permission:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeetingPermissionExcel(@Valid MeetingPermissionPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeetingPermissionDO> list = meetingPermissionService.getMeetingPermissionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商谈记录权限.xls", "数据", MeetingPermissionRespVO.class,
                BeanUtils.toBean(list, MeetingPermissionRespVO.class));
    }*/

}