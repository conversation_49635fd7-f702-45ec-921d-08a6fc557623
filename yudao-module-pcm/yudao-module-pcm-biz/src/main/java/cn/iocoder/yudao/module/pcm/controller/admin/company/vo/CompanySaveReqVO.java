package cn.iocoder.yudao.module.pcm.controller.admin.company.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 公司新增/修改 Request VO")
@Data
public class CompanySaveReqVO {

    @Schema(description = "公司ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000001")
    private Long id;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "东风汽车股份有限公司")
    @NotEmpty(message = "公司名称不能为空")
    private String name;

    @Schema(description = "父公司ID，0表示顶级公司", example = "10000001")
    private Long parentId;

    @Schema(description = "公司語言類型: zh-中文公司, en-英文公司", example = "zh_CN")
    private String languageType;

    @Schema(description = "公司地址")
    private String address;

    @Schema(description = "公司电话")
    private String phone;

}