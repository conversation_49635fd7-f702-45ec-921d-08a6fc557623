package cn.iocoder.yudao.module.pcm.ocr.exception;

public class CardRecognitionException extends RuntimeException {
    private Long frontFileId;
    private Long backFileId;
    private String failureType; // "FRONT" 或 "BACK"

    public CardRecognitionException(Long frontFileId, Long backFileId, String failureType, String message) {
        super(message);
        this.frontFileId = frontFileId;
        this.backFileId = backFileId;
        this.failureType = failureType;
    }

    public Long getFrontFileId() {
        return frontFileId;
    }

    public Long getBackFileId() {
        return backFileId;
    }

    public String getFailureType() {
        return failureType;
    }
}
