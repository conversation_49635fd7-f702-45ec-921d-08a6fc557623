package cn.iocoder.yudao.module.pcm.service.meetingpermission;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.pcm.controller.admin.meetingpermission.vo.*;
import cn.iocoder.yudao.module.pcm.dal.dataobject.meetingpermission.MeetingPermissionDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 商谈记录权限 Service 接口
 *
 * <AUTHOR>
 */
public interface MeetingPermissionService {

    /**
     * 创建商谈记录权限
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeetingPermission(@Valid MeetingPermissionSaveReqVO createReqVO);

    /**
     * 更新商谈记录权限
     *
     * @param updateReqVO 更新信息
     */
    void updateMeetingPermission(@Valid MeetingPermissionSaveReqVO updateReqVO);

    /**
     * 删除商谈记录权限
     *
     * @param id 编号
     */
    void deleteMeetingPermission(Long id);

    /**
     * 获得商谈记录权限
     *
     * @param id 编号
     * @return 商谈记录权限
     */
    MeetingPermissionDO getMeetingPermission(Long id);
    /**
     * 检查权限
     *
     * @param meetingId  会议ID
     * @param userId     用户ID
     * @param deptId     部门ID
     * @param roleIds    角色ID列表
     * @return true 表示有权限，false 表示无权限
     */
    MeetingPermissionDO getMeetingPermission(Long meetingId, Long userId, Long deptId, List<Long> roleIds);

    /**
     * 获得商谈记录权限分页
     *
     * @param pageReqVO 分页查询
     * @return 商谈记录权限分页
     */
    PageResult<MeetingPermissionDO> getMeetingPermissionPage(MeetingPermissionPageReqVO pageReqVO);

}