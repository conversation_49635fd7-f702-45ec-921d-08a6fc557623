package cn.iocoder.yudao.module.pcm.dal.mysql.card;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardPageReqVO;
import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.PcmCardPageReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.card.CardDO;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 名片 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CardMapper extends BaseMapperX<CardDO> {

    Logger log = LoggerFactory.getLogger(CardMapper.class);

    default PageResult<CardDO> selectPage(CardPageReqVO reqVO) {
        boolean isAdmin = SecurityFrameworkUtils.isAdmin();
        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CardDO::getDeleted, 0);

        // 获取当前登录用户 ID
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        String loginUserIdStr = loginUserId != null ? String.valueOf(loginUserId) : null;

        // 处理 cardType 过滤
        if ("mine".equals(reqVO.getCardType())) {
            // mine：匹配 createUserId
            wrapper.eq(CardDO::getCreateUserId, loginUserIdStr);
        } else if ("other".equals(reqVO.getCardType())) {
            // other：匹配 userId 中包含 loginUserId 的记录
            wrapper.apply("user_id like {0}", "%" + loginUserIdStr + "%");
        } else {
            // all：默认所有人可见，除非分配了 user_id 或 dept_id
            if (!isAdmin) {
                wrapper.and(w -> {
                    w.or(w1 -> w1.isNull(CardDO::getUserId).isNull(CardDO::getDeptId)) // 默认所有人可见
                            .or(w1 -> w1.apply("user_id like {0}", "%" + loginUserIdStr + "%")) // 分配给登录用户
                            .or(w1 -> w1.eq(CardDO::getCreateUserId, loginUserIdStr)) // 创建者是登录用户
                            .or(w1 -> {
                                // 分配给登录用户所在部门
                                Set<Long> deptIds = reqVO.getDeptIds();
                                if (CollectionUtil.isNotEmpty(deptIds)) {
                                    for (Long deptId : deptIds) {
                                        w1.apply("FIND_IN_SET({0}, REPLACE(dept_id, ';', ','))", deptId);
                                    }
                                }
                            });
                });
            }

        }

        if (StringUtils.isNotBlank(reqVO.getKeyword())) {
            String fullName = reqVO.getKeyword().replaceAll("\\s+", "");//去掉所有空格
            wrapper.and(wrapper1 -> wrapper1
                    .or(w -> w.like(CardDO::getFirstName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getLastName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getFullName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getCompanyName, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getEmail, reqVO.getKeyword()))
                    .or(w -> w.like(CardDO::getPhoneMobile, reqVO.getKeyword()))
                    //添加全名搜索条件
                    .or(w -> w.apply("CONCAT(first_name, '', last_name) LIKE {0}", "%" + fullName + "%"))
                    .or(w -> w.apply("CONCAT(last_name_en, '', first_name_en) LIKE {0}", "%" + fullName + "%"))

            );
        }

        // 处理 fullNames
        if (StringUtils.isNotBlank(reqVO.getFullNames())) {
            List<String> fullNameList = Arrays.asList(reqVO.getFullNames().split(";"));
            if (!fullNameList.isEmpty()) {
                wrapper.in(CardDO::getFullName, fullNameList);
            }
        }

        // 处理 jobTitles
        if (StringUtils.isNotBlank(reqVO.getJobTitles())) {
            List<String> jobTitleList = Arrays.asList(reqVO.getJobTitles().split(";"));
            if (!jobTitleList.isEmpty()) {
                wrapper.in(CardDO::getJobTitle, jobTitleList);
            }
        }

        // 处理 emails
        if (StringUtils.isNotBlank(reqVO.getEmails())) {
            List<String> emailList = Arrays.asList(reqVO.getEmails().split(";"));
            if (!emailList.isEmpty()) {
                wrapper.in(CardDO::getEmail, emailList);
            }
        }

        // 处理 companyNames
        if (StringUtils.isNotBlank(reqVO.getCompanyNames())) {
            List<String> companyNameList = Arrays.asList(reqVO.getCompanyNames().split(";"));
            if (!companyNameList.isEmpty()) {
                wrapper.in(CardDO::getCompanyName, companyNameList);
            }
        }

        if (null != reqVO.getCompanyId()) {
            wrapper.and(wrapper1 -> wrapper1
                    .or(w -> w.eq(CardDO::getCompanyId, reqVO.getCompanyId()))
                    .or(w -> w.eq(CardDO::getCompanyIdEn, reqVO.getCompanyId()))
            );
        }

        wrapper.eqIfPresent(CardDO::getGroupId, reqVO.getGroupId())
                .eqIfPresent(CardDO::getTitle, reqVO.getTitle())
                .likeIfPresent(CardDO::getFirstName, reqVO.getFirstName())
                .likeIfPresent(CardDO::getLastName, reqVO.getLastName())
                .likeIfPresent(CardDO::getFullName, reqVO.getFullName())
                .eqIfPresent(CardDO::getHonour, reqVO.getHonour())
                .eqIfPresent(CardDO::getPhoneMobile, reqVO.getPhoneMobile())
                .eqIfPresent(CardDO::getDepartment, reqVO.getDepartment())
                .eqIfPresent(CardDO::getJobTitle, reqVO.getJobTitle())
                .likeIfPresent(CardDO::getCustomTags, reqVO.getCustomTags())
                .likeIfPresent(CardDO::getCompanyName, reqVO.getCompanyName());

        // 添加 createTime 范围查询
        if (reqVO.getCreateTime() != null && reqVO.getCreateTime().length == 2) {
            String startTimeStr = reqVO.getCreateTime()[0];
            String endTimeStr = reqVO.getCreateTime()[1];
            if (StringUtils.isNotBlank(startTimeStr) && StringUtils.isNotBlank(endTimeStr)) {
                // 将字符串转换为 LocalDateTime，假设时间格式为 "yyyy-MM-dd"
                LocalDateTime startTime = LocalDate.parse(startTimeStr).atStartOfDay();
                LocalDateTime endTime = LocalDate.parse(endTimeStr).atTime(23, 59, 59); // 包含当天结束
                wrapper.between(CardDO::getCreateTime, startTime, endTime);
            }
        }

        if (reqVO.getIds()!=null && !reqVO.getIds().isEmpty()) {
            wrapper.inIfPresent(CardDO::getId, reqVO.getIds());
        }

        // 添加排序
        if (StringUtils.isNotBlank(reqVO.getOrderBy())) {
            String[] fields = reqVO.getOrderBy().split("\\|");
            if (fields.length == 2) {
                String field = fields[0];
                String order = fields[1];

                // 映射字段名到 MyBatis-Plus 的 SFunction
                Map<String, SFunction<CardDO, ?>> fieldMapping = new HashMap<>();
                fieldMapping.put("createTime", CardDO::getCreateTime);
                fieldMapping.put("fullName", CardDO::getFullName);
                fieldMapping.put("companyName", CardDO::getCompanyName);

                SFunction<CardDO, ?> column = fieldMapping.get(field);
                if (column != null) {
                    boolean isAsc = "asc".equalsIgnoreCase(order);
                    log.info("{} {}", field, isAsc ? "asc" : "desc");
                    if (isAsc) {
                        wrapper.orderByAsc(column);
                    } else {
                        wrapper.orderByDesc(column);
                    }
                }
            }
        }
        return selectPage(reqVO, wrapper);
    }

    /**
     * 根据公司ID查询名片列表
     *
     * @param companyId 公司ID
     * @return 名片列表
     */
    @Select("SELECT creator, COUNT(*) AS cards FROM pcm_card WHERE company_id = #{companyId} GROUP BY creator")
    List<Map<String, Object>> countCardsByCreator(Long companyId);

    /**
     * 根据公司ID查询名片列表
     *
     * @param companyId 公司ID
     * @return 名片列表
     */
    @Select("select id,first_name,last_name,first_name_en,last_name_en,full_name,company_id,company_name,company_id_en,company_name_en,user_id,create_user_id " +
            "from pcm_card where deleted = 0 and (company_id = #{companyId} or company_id_en = #{companyId})")
    List<CardDO> selectByCompanyId(@Param("companyId") Long companyId);

    /**
     * 根据分组ID查询名片列表
     *
     * @param groupId 分组ID
     * @return 名片列表
     */
    @Select("select id, job_title,job_title_en from pcm_card " +
            "where deleted = 0 AND group_id = #{groupId} AND ((user_id is null AND dept_id is null) OR user_id LIKE CONCAT('%', #{loginUserId}, '%') OR create_user_id LIKE CONCAT('%', #{loginUserId}, '%'))")
    @Deprecated
    List<CardDO> selectByGroudId(@Param("groupId") String groupId, @Param("loginUserId") String loginUserId);


    /**
     * 根据公司ID查询名片列表
     *
     * @param companyId 公司ID
     * @return 名片列表
     */
    default List<CardDO> getCardListByCompanyId(Long companyId) {
        if (companyId != null) {
            return selectList(Wrappers.<CardDO>lambdaQuery()
                    .eq(CardDO::getCompanyId, companyId)
                    .or()
                    .eq(CardDO::getCompanyIdEn, companyId));
        }
        return Collections.emptyList();
    }

    default List<CardDO> getCardListByCompanyIds(Collection<Long> companyIds) {
        if (CollUtil.isEmpty(companyIds)) {
            return Collections.emptyList();
        }
        return selectList(Wrappers.<CardDO>lambdaQuery()
                .in(CardDO::getCompanyId, companyIds)
                .or()
                .in(CardDO::getCompanyIdEn, companyIds));
    }

    /**
     * 根据公司ID查询名片列表
     *
     * @param reqVO 姓名、公司、职位、邮箱、手机
     * @return 名片列表
     */
    List<Map<String, Object>> selectDistinctGroupIdPage(Page<CardDO> page, @Param("reqVO") PcmCardPageReqVO reqVO);

    /**
     * 查询去重后的 group_id 对应的 id 列表
     * @param reqVO 查询条件，包含 userId, deptId, keyword, currentCardId, groupId, orderBy 等
     * @return 包含 id 的 Map 列表
     */
    List<Map<String, Object>> selectDistinctGroupIds(@Param("reqVO") PcmCardPageReqVO reqVO);

    /**
     * 动态更新
     * @param updateFields 更新字段
     * @param conditions 条件
     * @return
     */
    default int updateDynamically(Map<String, Object> updateFields, Map<String, Object> conditions) {
        LambdaUpdateWrapper<CardDO> updateWrapper = new LambdaUpdateWrapper<>();
        // 动态 SET 字段
        if (updateFields == null || updateFields.isEmpty()) {
            return 0;
        }

        updateFields.forEach((field, value) -> {
            if (value != null) {
                switch (field) {
                    case "companyName":
                        updateWrapper.set(CardDO::getCompanyName, value);
                        break;
                    case "companyNameEn":
                        updateWrapper.set(CardDO::getCompanyNameEn, value);
                        break;
                    default:
                        throw new IllegalArgumentException("Invalid field: " + field);
                }
            }
        });

        updateWrapper.set(CardDO::getUpdater,  SecurityFrameworkUtils.getLoginUserNickname());
        updateWrapper.set(CardDO::getUpdateTime, LocalDateTime.now());

        // 动态 WHERE 条件
        if (conditions == null || conditions.isEmpty()) {
            return 0;
        }
        conditions.forEach((field, value) -> {
            if (value != null) {
                switch (field) {
                    case "companyId":
                        updateWrapper.eq(CardDO::getCompanyId, value);
                        break;
                    case "companyIdEn":
                        updateWrapper.eq(CardDO::getCompanyIdEn, value);
                        break;
                    default:
                        throw new IllegalArgumentException("Invalid condition field: " + field);
                }
            }
        });

        // 执行更新
        return update(null, updateWrapper);
    }

    default boolean checkRecordExists(Long companyId, Long companyIdEn) {

        // 避免无条件查询
        if (companyId == null && companyIdEn == null) {
            return false;
        }

        LambdaQueryWrapperX<CardDO> wrapper = new LambdaQueryWrapperX<>();

        // 使用 eqIfPresent，简化非空判断
        wrapper.eqIfPresent(CardDO::getCompanyId, companyId)
                .eqIfPresent(CardDO::getCompanyIdEn, companyIdEn);

        // 自动处理 deleted = 0 和 tenant_id（如果框架启用多租户）
        return selectCount(wrapper) > 0;
    }


    /**
     * 優先選中文，沒有中文選英文
     * @return
     */
    List<Map<String, Object>> getCompanyList();

    /**
     * 查询有效的公司信息（中英文任选），并去重。
     * 有效定义为：company_id / company_id_en 和 company_name / company_name_en
     * 不为 NULL 且去除空格后不为空字符串。
     *
     * @param lang 字段语言类型，可选值：
     *             - "zh"：中文字段 company_id 和 company_name
     *             - "en"：英文字段 company_id_en 和 company_name_en
     * @return 去重后的公司信息列表
     */
    List<Map<String, Object>> getSimpleCompanyList(@Param("lang") String lang);


    /**
     * 根据公司ID查询公司信息
     * @param companyId
     * @param searchByEn
     * @return
     */
    List<Map<String, Object>> findByCompanyIdOrEn(@Param("companyId") Long companyId, @Param("searchByEn") boolean searchByEn);


}
