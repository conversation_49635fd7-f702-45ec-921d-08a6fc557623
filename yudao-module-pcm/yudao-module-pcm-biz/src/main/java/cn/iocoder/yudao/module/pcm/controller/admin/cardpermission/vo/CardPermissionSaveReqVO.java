package cn.iocoder.yudao.module.pcm.controller.admin.cardpermission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 名片权限新增/修改 Request VO")
@Data
public class CardPermissionSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31215")
    private Long id;

    @Schema(description = "名片ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13600")
    @NotNull(message = "名片ID不能为空")
    private Long cardId;

    @Schema(description = "用户ID", example = "1")
    @NotNull(message = "名片ID不能为空")
    private Long userId;

    @Schema(description = "有权看的字段")
    private String fieldName; // 以数组格式，存储字段权限

    @Schema(description = "是否可编辑")
    private Boolean isEdit;


}