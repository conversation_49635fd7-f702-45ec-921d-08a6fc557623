package cn.iocoder.yudao.module.pcm.service.redis;

import cn.iocoder.yudao.module.pcm.controller.admin.card.vo.CardSaveReqVO;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
public class CardRedisService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 保存 CardSaveReqVO 到 Redis，設置 1 小時過期
    public void saveCardToRedis(CardSaveReqVO card) {
        String key = "card:" + card.getId();
        redisTemplate.opsForValue().set(key, card, 1, TimeUnit.HOURS);
    }

    // 從 Redis 獲取 CardSaveReqVO
    public CardSaveReqVO getCardFromRedis(Long id) {
        String key = "card:" + id;
        return (CardSaveReqVO) redisTemplate.opsForValue().get(key);
    }

    // 刪除 Redis 中的 CardSaveReqVO
    public void deleteCardFromRedis(Long id) {
        String key = "card:" + id;
        redisTemplate.delete(key);
    }

}
