package cn.iocoder.yudao.module.pcm.controller.admin.meeting.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商谈记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeetingPageReqVO extends PageParam {

    @Schema(description = "名片ID")
    private Long cardId;

    @Schema(description = "可根据姓名、公司、职位、邮箱、手机号搜索")
    private String keyword;

    @Schema(description = "商谈类型")
    private String type;

    @Schema(description = "商谈主题")
    private String title;

    @Schema(description = "商谈内容")
    private String content;

    @Schema(description = "会议场合")
    private String occasion;

    @Schema(description = "商谈时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] meetingTime;

    @Schema(description = "图片", example = "https://www.iocoder.cn")
    private String imageUrl;

    @Schema(description = "商谈人姓名")
    private String userName;

    @Schema(description = "商谈人职位")
    private String jobTitle;

    @Schema(description = "有权向查看的同事")
    private String userId;

    @Schema(description = "有权限查看的部门")
    private String deptId;

    @Schema(description = "创建人id")
    private Long createUserId;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "下属id")
    private String subordinateId;

}