package cn.iocoder.yudao.module.pcm.service.cardraw;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.pcm.controller.admin.cardraw.vo.CardRawSaveReqVO;
import cn.iocoder.yudao.module.pcm.dal.dataobject.cardraw.CardRawDO;
import cn.iocoder.yudao.module.pcm.dal.mysql.cardraw.CardRawMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.pcm.enums.ErrorCodeConstants.CARDRAW_NOT_EXISTS;

/**
 * 識別名片的原始記錄 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CardRawServiceImpl implements CardRawService {

    private static final Logger log = LoggerFactory.getLogger(CardRawServiceImpl.class);
    @Resource
    private CardRawMapper cardRawMapper;

    @Override
    public Long createCard(CardRawSaveReqVO createReqVO) {
        try {
            CardRawDO cardRawDO = BeanUtils.toBean(createReqVO, CardRawDO.class);
            cardRawMapper.insert(cardRawDO);
            return cardRawDO.getId();
        } catch (Exception e) {
            log.error("新增名片原始記錄失敗，{}", createReqVO, e);
            return null;
        }
    }

    @Override
    public void updateCard(CardRawSaveReqVO updateReqVO) {
        try {
            // 校验存在
            validateCardExists(updateReqVO.getId());
            // 更新
            CardRawDO updateObj = BeanUtils.toBean(updateReqVO, CardRawDO.class);
            cardRawMapper.updateById(updateObj);
        } catch (Exception e) {
            log.error("更新名片原始記錄失敗，id:{}", updateReqVO.getId(), e);
        }
    }

    @Override
    public void deleteCard(Long id) {
        // 校验存在
        cardRawMapper.update(Wrappers.<CardRawDO>lambdaUpdate()
                .set(CardRawDO::getDeleted, 1)
                .eq(CardRawDO::getId, id));
    }

    /**
     * 验证名片是否存在
     *
     * @param id 名片的唯一标识符
     * 自定义异常 如果找不到具有指定ID的名片，则抛出CARD_NOT_EXISTS异常
     */
    private void validateCardExists(Long id) {
        // 检查数据库中是否存在与给定ID匹配的名片
        if (cardRawMapper.selectById(id) == null) {
            // 如果没有找到名片，则抛出预定义的异常
            throw exception(CARDRAW_NOT_EXISTS);
        }
    }

}