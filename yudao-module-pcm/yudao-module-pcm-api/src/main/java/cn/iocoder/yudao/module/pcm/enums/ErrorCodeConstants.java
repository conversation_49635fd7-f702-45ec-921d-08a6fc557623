package cn.iocoder.yudao.module.pcm.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * pmc 错误码枚举类
 *
 * pmc 系统，使用 1-*********** 段
 */
public interface ErrorCodeConstants {

    // ========== CARD 名片 1-*********** ==========
    ErrorCode CARD_NOT_EXISTS = new ErrorCode(1-***********, "名片不存在");
    ErrorCode CARD_IMPORT_LIST_IS_EMPTY = new ErrorCode(1_001_000_001, "導入名片數據不能為空！");
    ErrorCode FILE_NOT_EXISTS = new ErrorCode(1_001_000_002, "檔案不存在");
    ErrorCode CARD_IMPORT_ALL_FAILED = new ErrorCode(1_001_000_003, "所有名片導入均失敗");
    ErrorCode CARD_ID_IS_EMPTY = new ErrorCode(1_001_000_004, "名片ID不能為空！");
    ErrorCode CARD_UPDATE_EXECPTION = new ErrorCode(1_001_000_005, "更新名片異常");
    ErrorCode CARD_INSERT_EXECPTION = new ErrorCode(1_001_000_006, "创建名片異常");
    ErrorCode FINALLY_CARD_EXECPTION = new ErrorCode(1_001_000_007, "儲存成功，這張已經是最後一張名片了");


    // ========== COMPANY 公司 1-*********** ==========
    ErrorCode COMPANY_NOT_EXISTS = new ErrorCode(1-***********, "公司不存在");
    ErrorCode COMPANY_CANNOT_DELETE_WITHOUT_ADMIN = new ErrorCode(1-***********, "刪除公司需由 超級管理員執行，請聯繫您的管理團隊協助處理");
    ErrorCode COMPANY_CANNOT_DELETE_WITH_CARDS = new ErrorCode(1-***********, "無法刪除公司：請先刪除該公司下的所有名片");

    // ========== MEETING 商談記錄 1-***********
    ErrorCode MEETING_NOT_EXISTS = new ErrorCode(1-***********, "商談記錄不存在");

    // ========== TAGS 標籤 1-***********
    ErrorCode TAGS_NOT_EXISTS = new ErrorCode(1-***********, "標籤不存在");
    ErrorCode TAGS_ALREADY_IN_USE = new ErrorCode(1-***********, "標籤已被使用");

    // ========== MEETING_PERMISSION 商談記錄權限 1-***********
    ErrorCode MEETING_PERMISSION_NOT_EXISTS = new ErrorCode(1-***********, "商談記錄的權限不存在");
    ErrorCode MEETING_PERMISSION_DENIED = new ErrorCode(1-***********, "您沒有權限訪問該會議記錄");

    // ========== CARD_PERMISSION 名片權限 1-***********
    ErrorCode CARD_PERMISSION_NOT_EXISTS = new ErrorCode(1-***********, "名片權限不存在");
    ErrorCode CARD_PERMISSION_DENIED = new ErrorCode(1-006-000-001, "您沒有權限訪問該名片");
    ErrorCode CARD_PERMISSION_TARGET_REQUIRED  = new ErrorCode(1-006-000-002, "無法完成授權：您尚未選擇任何人員或權限字段");

    // ========== CARDRAW 名片原始記錄 1-007-000-000
    ErrorCode CARDRAW_NOT_EXISTS = new ErrorCode(1-007-000-001, "名片原始記錄不存在");

    // ========== EXPORT_RECORD 導出記錄 1-008-000-000 ==========
    ErrorCode EXPORT_AT_LEAST_ONE_CARD_REQUIRED = new ErrorCode(400, "你沒有可導出的名片，請至少需要有一張名片符合條件");
    ErrorCode EXPORT_RECORD_NOT_FOUND = new ErrorCode(404, "匯出記錄不存在");
    ErrorCode EXPORT_REQUEST_NOT_APPROVED = new ErrorCode(403, "匯出請求未通過審批");
    ErrorCode EXPORT_RECORD_NO_PERMISSION = new ErrorCode(403, "無權導出此記錄");
    ErrorCode EXPORT_REQUEST_NEEDS_APPROVAL_LIMIT_REACHED = new ErrorCode(403, "匯出請求需審批，累計數量已達50張或以上");
    ErrorCode EXPORT_RECORD_ALREADY_EXPORTED = new ErrorCode(403, "此記錄已導出");
    ErrorCode EXPORT_PERMISSION_VALIDATION_FAILED = new ErrorCode(403, "權限驗證失敗");

    ErrorCode EXPORT_LIMIT_EXCEEDED_NEED_APPROVAL = new ErrorCode(403, "累計導出數量超過50張，請提交申請");
    ErrorCode EXPORT_PENDING_APPROVAL_REQUIRED = new ErrorCode(403, "已有待審批的匯出請求，請等待審批完成");

    ErrorCode APPROVER_COMMENT_EMPTY = new ErrorCode(400, "審批人備註不可為空");
    ErrorCode REQUESTER_NOT_FOUND = new ErrorCode(404, "申請人不存在");
    ErrorCode APPROVER_NOT_FOUND = new ErrorCode(404, "審批人不存在");
    ErrorCode APPROVAL_PERMISSION_DENIED = new ErrorCode(403, "無權進行審批");


}
